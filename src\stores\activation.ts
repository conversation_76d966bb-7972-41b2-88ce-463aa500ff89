import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useActivationStore = defineStore('activation', () => {
  // الحالة
  const isActivated = ref(false)
  const activationDate = ref<Date | null>(null)
  const isLoading = ref(false)

  // الأكواد الصالحة (محفوظة بشكل آمن)
  const validActivationCodes = [
    '773$729#886',
    '777$3236#888', 
    '777$167#794'
  ]

  // مفتاح التخزين المحلي
  const ACTIVATION_STORAGE_KEY = 'app_activation_status'
  const ACTIVATION_DATE_KEY = 'app_activation_date'

  /**
   * التحقق من حالة التفعيل
   */
  async function checkActivationStatus(): Promise<boolean> {
    try {
      // التحقق من التخزين المحلي
      const storedStatus = localStorage.getItem(ACTIVATION_STORAGE_KEY)
      const storedDate = localStorage.getItem(ACTIVATION_DATE_KEY)
      
      if (storedStatus === 'true' && storedDate) {
        isActivated.value = true
        activationDate.value = new Date(storedDate)
        console.log('✅ البرنامج مفعل بالفعل منذ:', activationDate.value)
        return true
      }

      // إذا كان في بيئة Electron، التحقق من الإعدادات المحلية أيضاً
      if (window.electronAPI) {
        try {
          const electronStatus = await window.electronAPI.getActivationStatus()
          if (electronStatus?.activated) {
            isActivated.value = true
            activationDate.value = new Date(electronStatus.date)

            // مزامنة مع localStorage
            localStorage.setItem(ACTIVATION_STORAGE_KEY, 'true')
            localStorage.setItem(ACTIVATION_DATE_KEY, electronStatus.date)

            console.log('✅ البرنامج مفعل (من إعدادات Electron)')
            return true
          }
        } catch (error) {
          console.warn('تعذر التحقق من حالة التفعيل في Electron:', error)
        }
      }

      isActivated.value = false
      activationDate.value = null
      return false
    } catch (error) {
      console.error('خطأ في التحقق من حالة التفعيل:', error)
      return false
    }
  }

  /**
   * التحقق من صحة رمز التفعيل
   */
  async function validateActivationCode(code: string): Promise<boolean> {
    isLoading.value = true
    
    try {
      // تنظيف الكود المدخل
      const cleanCode = code.trim().toUpperCase()
      
      // التحقق من الكود
      const isValid = validActivationCodes.some(validCode => 
        validCode.toUpperCase() === cleanCode
      )

      if (isValid) {
        await activateApplication(cleanCode)
        console.log('✅ تم التفعيل بنجاح باستخدام الكود:', cleanCode)
        return true
      } else {
        console.warn('❌ كود تفعيل غير صحيح:', cleanCode)
        return false
      }
    } catch (error) {
      console.error('خطأ في التحقق من رمز التفعيل:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * تفعيل التطبيق
   */
  async function activateApplication(code: string): Promise<void> {
    try {
      const now = new Date()
      
      // تحديث الحالة
      isActivated.value = true
      activationDate.value = now
      
      // حفظ في localStorage
      localStorage.setItem(ACTIVATION_STORAGE_KEY, 'true')
      localStorage.setItem(ACTIVATION_DATE_KEY, now.toISOString())
      
      // حفظ في إعدادات Electron إذا كان متاحاً
      if (window.electronAPI) {
        try {
          await window.electronAPI.saveActivationStatus({
            activated: true,
            date: now.toISOString(),
            code: hashCode(code) // حفظ hash للكود وليس الكود نفسه
          })
          console.log('✅ تم حفظ حالة التفعيل في Electron')
        } catch (error) {
          console.warn('تعذر حفظ حالة التفعيل في Electron:', error)
        }
      }

      // إضافة سجل للتفعيل
      const activationLog = {
        timestamp: now.toISOString(),
        codeHash: hashCode(code),
        platform: navigator.platform,
        userAgent: navigator.userAgent.substring(0, 100) // أول 100 حرف فقط
      }

      localStorage.setItem('activation_log', JSON.stringify(activationLog))
      
      console.log('🎉 تم تفعيل التطبيق بنجاح!')
    } catch (error) {
      console.error('خطأ في تفعيل التطبيق:', error)
      throw error
    }
  }

  /**
   * إلغاء التفعيل (للاختبار فقط - يجب إزالتها في الإنتاج)
   */
  async function deactivateApplication(): Promise<void> {
    try {
      isActivated.value = false
      activationDate.value = null
      
      // مسح من localStorage
      localStorage.removeItem(ACTIVATION_STORAGE_KEY)
      localStorage.removeItem(ACTIVATION_DATE_KEY)
      localStorage.removeItem('activation_log')
      
      // مسح من إعدادات Electron
      if (window.electronAPI) {
        try {
          await window.electronAPI.clearActivationStatus()
          console.log('✅ تم مسح حالة التفعيل من Electron')
        } catch (error) {
          console.warn('تعذر مسح حالة التفعيل من Electron:', error)
        }
      }
      
      console.log('🔄 تم إلغاء تفعيل التطبيق')
    } catch (error) {
      console.error('خطأ في إلغاء التفعيل:', error)
      throw error
    }
  }

  /**
   * الحصول على معلومات التفعيل
   */
  function getActivationInfo() {
    return {
      isActivated: isActivated.value,
      activationDate: activationDate.value,
      daysSinceActivation: activationDate.value 
        ? Math.floor((Date.now() - activationDate.value.getTime()) / (1000 * 60 * 60 * 24))
        : null
    }
  }

  /**
   * دالة مساعدة لتشفير الكود (hash)
   */
  function hashCode(str: string): string {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // تحويل إلى 32bit integer
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * التحقق من صحة البيئة
   */
  function validateEnvironment(): boolean {
    try {
      // التحقق من وجود localStorage
      if (typeof localStorage === 'undefined') {
        console.error('localStorage غير متاح')
        return false
      }

      // اختبار localStorage
      const testKey = 'activation_test'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)

      return true
    } catch (error) {
      console.error('خطأ في التحقق من البيئة:', error)
      return false
    }
  }

  // تصدير الوظائف والحالة
  return {
    // الحالة
    isActivated,
    activationDate,
    isLoading,
    
    // الوظائف
    checkActivationStatus,
    validateActivationCode,
    activateApplication,
    deactivateApplication, // للاختبار فقط
    getActivationInfo,
    validateEnvironment
  }
})

// تعريف نوع window.electronAPI
declare global {
  interface Window {
    electronAPI?: {
      getActivationStatus: () => Promise<{activated: boolean, date: string}>
      saveActivationStatus: (data: {activated: boolean, date: string, code: string}) => Promise<void>
      clearActivationStatus: () => Promise<void>
      getVersion: () => Promise<string>
      getUserDataPath: () => Promise<string>
      showMessageBox: (options: any) => Promise<any>
    }
  }
}
