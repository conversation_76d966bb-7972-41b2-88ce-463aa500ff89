import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useActivationStore } from '@/stores/activation'
import Activation from '@/views/Activation.vue'
import Login from '@/views/Login.vue'
import Suspects from '@/views/Suspects.vue'
import Database from '@/views/Database.vue'
import Settings from '@/views/Settings.vue'
import Reports from '@/views/Reports.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/activation',
      name: 'activation',
      component: Activation,
      meta: {
        title: 'تفعيل البرنامج',
        requiresAuth: false,
        requiresActivation: false
      }
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: {
        title: 'تسجيل الدخول',
        requiresAuth: false,
        requiresActivation: true
      }
    },
    {
      path: '/',
      redirect: '/suspects'
    },
    {
      path: '/suspects',
      name: 'suspects',
      component: Suspects,
      meta: {
        title: 'بيانات المتهمين',
        icon: 'fas fa-users',
        requiresAuth: true,
        requiresActivation: true
      }
    },
    {
      path: '/database',
      name: 'database',
      component: Database,
      meta: {
        title: 'قاعدة بيانات وسجلات المتهمين',
        icon: 'fas fa-database',
        requiresAuth: true,
        requiresActivation: true
      }
    },
    {
      path: '/reports',
      name: 'reports',
      component: Reports,
      meta: {
        title: 'التقارير',
        icon: 'fas fa-chart-bar',
        requiresAuth: true,
        requiresActivation: true
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: Settings,
      meta: {
        title: 'الإعدادات',
        icon: 'fas fa-cog',
        requiresAuth: true,
        requiresActivation: true
      }
    }
  ]
})

// حماية الصفحات - التحقق من التفعيل وتسجيل الدخول
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const activationStore = useActivationStore()

  // التحقق من حالة التفعيل أولاً
  if (to.meta.requiresActivation !== false) {
    const isActivated = await activationStore.checkActivationStatus()

    if (!isActivated) {
      // إعادة توجيه إلى صفحة التفعيل
      if (to.name !== 'activation') {
        next('/activation')
        return
      }
    }
  }

  // إذا كانت الصفحة تتطلب تسجيل دخول
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // إعادة توجيه إلى صفحة تسجيل الدخول
      next('/login')
      return
    }
  }

  // إذا كان المستخدم مسجل دخول ويحاول الوصول لصفحة تسجيل الدخول
  if (to.name === 'login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  // إذا كان البرنامج مفعل ويحاول الوصول لصفحة التفعيل
  if (to.name === 'activation') {
    const isActivated = await activationStore.checkActivationStatus()
    if (isActivated) {
      next('/login')
      return
    }
  }

  next()
})

export default router
