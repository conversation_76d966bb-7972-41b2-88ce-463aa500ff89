const path = require('path')
const fs = require('fs')
const { app } = require('electron')

class DatabaseService {
  constructor() {
    this.data = {
      suspects: [],
      users: [],
      database_tabs: [],
      settings: {},
      activity_log: [],
      temp_search_data: []
    }
    this.dbPath = null
    this.isInitialized = false
    this.nextId = 1
  }

  /**
   * تهيئة قاعدة البيانات
   */
  async initialize() {
    try {
      // تحديد مسار قاعدة البيانات
      const userDataPath = app.getPath('userData')
      this.dbPath = path.join(userDataPath, 'suspects_data.json')
      
      console.log('📁 Database path:', this.dbPath)
      
      // التأكد من وجود مجلد البيانات
      if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true })
      }

      // تحميل البيانات الموجودة
      await this.loadData()
      
      // إنشاء مستخدم افتراضي إذا لم يوجد
      await this.createDefaultUser()
      
      this.isInitialized = true
      console.log('✅ Database initialized successfully')
      
      return true
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw error
    }
  }

  /**
   * تحميل البيانات من الملف
   */
  async loadData() {
    try {
      if (fs.existsSync(this.dbPath)) {
        const fileContent = fs.readFileSync(this.dbPath, 'utf8')
        const loadedData = JSON.parse(fileContent)
        
        // دمج البيانات المحملة مع البنية الافتراضية
        this.data = { ...this.data, ...loadedData }
        
        // تحديث nextId
        this.updateNextId()
        
        console.log('✅ Data loaded successfully')
      } else {
        console.log('📝 Creating new database file')
        await this.saveData()
      }
    } catch (error) {
      console.error('❌ Failed to load data:', error)
      // في حالة فشل التحميل، نبدأ ببيانات فارغة
      await this.saveData()
    }
  }

  /**
   * حفظ البيانات في الملف
   */
  async saveData() {
    try {
      const dataToSave = {
        ...this.data,
        lastUpdated: new Date().toISOString()
      }
      
      fs.writeFileSync(this.dbPath, JSON.stringify(dataToSave, null, 2), 'utf8')
      console.log('💾 Data saved successfully')
    } catch (error) {
      console.error('❌ Failed to save data:', error)
      throw error
    }
  }

  /**
   * تحديث nextId بناءً على البيانات الموجودة
   */
  updateNextId() {
    let maxId = 0
    
    // البحث في جميع الجداول عن أعلى ID
    Object.values(this.data).forEach(table => {
      if (Array.isArray(table)) {
        table.forEach(record => {
          if (record.id && typeof record.id === 'number' && record.id > maxId) {
            maxId = record.id
          }
        })
      }
    })
    
    this.nextId = maxId + 1
  }

  /**
   * إنشاء مستخدم افتراضي
   */
  async createDefaultUser() {
    if (this.data.users.length === 0) {
      const bcrypt = require('bcryptjs')
      const defaultUser = {
        id: this.nextId++,
        username: 'admin',
        password_hash: await bcrypt.hash('admin123', 10),
        full_name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'admin',
        permissions: JSON.stringify({
          sections: { suspects: true, database: true, settings: true },
          actions: { add: true, edit: true, delete: true, export: true, import: true }
        }),
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      this.data.users.push(defaultUser)
      await this.saveData()
      
      console.log('👤 Default admin user created')
    }
  }

  /**
   * إضافة سجل جديد
   */
  async insert(table, data) {
    try {
      if (!this.data[table]) {
        this.data[table] = []
      }
      
      const record = {
        id: this.nextId++,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      this.data[table].push(record)
      await this.saveData()
      
      return { id: record.id, changes: 1 }
    } catch (error) {
      console.error(`❌ Insert failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * تحديث سجل
   */
  async update(table, id, data) {
    try {
      if (!this.data[table]) {
        throw new Error(`Table ${table} does not exist`)
      }
      
      const index = this.data[table].findIndex(record => record.id === id)
      if (index === -1) {
        throw new Error(`Record with id ${id} not found in table ${table}`)
      }
      
      this.data[table][index] = {
        ...this.data[table][index],
        ...data,
        updated_at: new Date().toISOString()
      }
      
      await this.saveData()
      
      return { changes: 1 }
    } catch (error) {
      console.error(`❌ Update failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * حذف سجل
   */
  async delete(table, id) {
    try {
      if (!this.data[table]) {
        throw new Error(`Table ${table} does not exist`)
      }
      
      const index = this.data[table].findIndex(record => record.id === id)
      if (index === -1) {
        throw new Error(`Record with id ${id} not found in table ${table}`)
      }
      
      this.data[table].splice(index, 1)
      await this.saveData()
      
      return { changes: 1 }
    } catch (error) {
      console.error(`❌ Delete failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * جلب سجل واحد
   */
  async get(table, conditions = {}) {
    try {
      if (!this.data[table]) {
        return null
      }
      
      return this.data[table].find(record => {
        return Object.keys(conditions).every(key => record[key] === conditions[key])
      }) || null
    } catch (error) {
      console.error(`❌ Get failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * جلب جميع السجلات
   */
  async getAll(table, conditions = {}) {
    try {
      if (!this.data[table]) {
        return []
      }
      
      if (Object.keys(conditions).length === 0) {
        return [...this.data[table]]
      }
      
      return this.data[table].filter(record => {
        return Object.keys(conditions).every(key => record[key] === conditions[key])
      })
    } catch (error) {
      console.error(`❌ GetAll failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * البحث النصي
   */
  async search(table, query, limit = 100) {
    try {
      if (!this.data[table] || !query) {
        return []
      }
      
      const searchQuery = query.toLowerCase()
      const results = this.data[table].filter(record => {
        // البحث في جميع الحقول النصية
        return Object.values(record).some(value => {
          if (typeof value === 'string') {
            return value.toLowerCase().includes(searchQuery)
          }
          return false
        })
      })
      
      return results.slice(0, limit)
    } catch (error) {
      console.error(`❌ Search failed for table ${table}:`, error)
      throw error
    }
  }

  /**
   * البحث النصي الكامل للمتهمين
   */
  fullTextSearch(table, query, limit = 100) {
    return this.search(table, query, limit)
  }

  /**
   * تنفيذ استعلام مخصص (للتوافق مع الكود الموجود)
   */
  query(sql, params = []) {
    // هذه دالة مبسطة للتوافق
    // في التطبيق الحقيقي، يجب تحليل SQL وتنفيذه
    console.warn('Custom SQL queries not supported in simple database mode')
    return []
  }

  /**
   * إغلاق قاعدة البيانات
   */
  async close() {
    try {
      await this.saveData()
      this.isInitialized = false
      console.log('✅ Database closed successfully')
    } catch (error) {
      console.error('❌ Failed to close database:', error)
      throw error
    }
  }

  /**
   * إنشاء نسخة احتياطية
   */
  async backup(backupPath) {
    try {
      const backupData = {
        ...this.data,
        backupDate: new Date().toISOString(),
        version: '1.0.0'
      }
      
      fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2), 'utf8')
      console.log('💾 Backup created successfully:', backupPath)
      
      return true
    } catch (error) {
      console.error('❌ Backup failed:', error)
      throw error
    }
  }

  /**
   * استعادة من نسخة احتياطية
   */
  async restore(backupPath) {
    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error('Backup file not found')
      }
      
      const backupContent = fs.readFileSync(backupPath, 'utf8')
      const backupData = JSON.parse(backupContent)
      
      // التحقق من صحة البيانات
      if (!backupData.suspects || !Array.isArray(backupData.suspects)) {
        throw new Error('Invalid backup file format')
      }
      
      this.data = { ...this.data, ...backupData }
      this.updateNextId()
      
      await this.saveData()
      
      console.log('✅ Database restored successfully from:', backupPath)
      return true
    } catch (error) {
      console.error('❌ Restore failed:', error)
      throw error
    }
  }
}

// إنشاء instance واحد
const databaseService = new DatabaseService()

module.exports = databaseService
