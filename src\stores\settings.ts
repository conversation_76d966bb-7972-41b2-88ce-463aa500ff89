import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  AppSettings,
  SuspectField,
  ThemeSettings,
  OrganizationSettings,
  User,
  BackupSettings,
  DeveloperInfo
} from '@/types'
import { db } from '@/utils/database'

export const useSettingsStore = defineStore('settings', () => {
  // State
  const settings = ref<AppSettings | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const theme = computed(() => settings.value?.theme || getDefaultTheme())
  const organization = computed(() => settings.value?.organization || getDefaultOrganization())
  const suspectFields = computed(() => settings.value?.fields || [])
  const backup = computed(() => settings.value?.backup || getDefaultBackup())

  // Actions
  async function loadSettings() {
    try {
      isLoading.value = true
      error.value = null

      const settingsData = await db.settings.orderBy('id').first()
      if (settingsData) {
        settings.value = settingsData
      } else {
        // Create default settings if none exist
        await createDefaultSettings()
      }

      // Load fields separately and convert JSON strings back to arrays
      const fieldsData = await db.fields.orderBy('order').toArray()
      const fieldsWithArrayOptions = fieldsData.map(field => {
        const processedField = { ...field }

        // Process options
        if (field.options) {
          try {
            if (typeof field.options === 'string' && field.options.startsWith('[')) {
              processedField.options = JSON.parse(field.options)
            } else if (typeof field.options === 'string') {
              // Handle old format (comma-separated string)
              processedField.options = field.options.split(',').map(opt => opt.trim())
            } else if (Array.isArray(field.options)) {
              // Already an array
              processedField.options = field.options
            }
          } catch (error) {
            console.warn('Error parsing field options:', error)
            // Fallback: treat as comma-separated string
            processedField.options = field.options.toString().split(',').map(opt => opt.trim())
          }
        }

        // Process validation.fileTypes
        if (field.validation && field.validation.fileTypes) {
          try {
            if (typeof field.validation.fileTypes === 'string' && field.validation.fileTypes.startsWith('[')) {
              processedField.validation = {
                ...field.validation,
                fileTypes: JSON.parse(field.validation.fileTypes)
              }
            } else if (typeof field.validation.fileTypes === 'string') {
              // Handle old format (comma-separated string)
              processedField.validation = {
                ...field.validation,
                fileTypes: field.validation.fileTypes.split(',').map(type => type.trim())
              }
            }
          } catch (error) {
            console.warn('Error parsing field validation.fileTypes:', error)
            // Keep original value
          }
        }

        return processedField
      })

      if (settings.value) {
        settings.value.fields = fieldsWithArrayOptions
      }

      // Apply theme to document after loading
      if (settings.value?.theme) {
        applyThemeToDocument(settings.value.theme)
      }
    } catch (err) {
      error.value = 'فشل في تحميل الإعدادات'
      console.error('Error loading settings:', err)
    } finally {
      isLoading.value = false
    }
  }

  async function updateTheme(newTheme: Partial<ThemeSettings>) {
    try {
      if (!settings.value) return

      const updatedTheme = { ...settings.value.theme, ...newTheme }
      const updatedSettings = { ...settings.value, theme: updatedTheme }
      
      await db.settings.update(settings.value.id!, { theme: updatedTheme })
      settings.value = updatedSettings
      
      // Apply theme to document
      applyThemeToDocument(updatedTheme)
    } catch (err) {
      error.value = 'فشل في تحديث المظهر'
      console.error('Error updating theme:', err)
    }
  }

  async function updateOrganization(newOrganization: Partial<OrganizationSettings>) {
    try {
      if (!settings.value) return

      const updatedOrganization = { ...settings.value.organization, ...newOrganization }
      const updatedSettings = { ...settings.value, organization: updatedOrganization }
      
      await db.settings.update(settings.value.id!, { organization: updatedOrganization })
      settings.value = updatedSettings
    } catch (err) {
      error.value = 'فشل في تحديث بيانات المؤسسة'
      console.error('Error updating organization:', err)
    }
  }

  async function updateSuspectFields(fields: SuspectField[]) {
    try {
      if (!settings.value) return

      // Convert options arrays to JSON strings for storage
      const fieldsToStore = fields.map(field => ({
        ...field,
        options: field.options ? JSON.stringify(field.options) : undefined
      }))

      // Update fields in database
      await db.transaction('rw', db.fields, async () => {
        await db.fields.clear()
        await db.fields.bulkAdd(fieldsToStore.map(f => ({ ...f, id: undefined })))
      })

      // Update settings with original fields (with array options)
      const updatedSettings = { ...settings.value, fields }
      settings.value = updatedSettings
    } catch (err) {
      error.value = 'فشل في تحديث حقول البيانات'
      console.error('Error updating suspect fields:', err)
    }
  }

  async function addSuspectField(field: Omit<SuspectField, 'id'>) {
    try {
      // Deep clone and prepare field for storage
      const fieldToStore: any = JSON.parse(JSON.stringify(field))

      // Convert options array to JSON string
      if (fieldToStore.options && Array.isArray(fieldToStore.options)) {
        fieldToStore.options = JSON.stringify(fieldToStore.options)
      }

      // Convert validation.fileTypes array to JSON string
      if (fieldToStore.validation && fieldToStore.validation.fileTypes && Array.isArray(fieldToStore.validation.fileTypes)) {
        fieldToStore.validation.fileTypes = JSON.stringify(fieldToStore.validation.fileTypes)
      }

      console.log('Storing field:', fieldToStore)

      const newFieldId = await db.fields.add(fieldToStore)

      // Reload all fields and convert options back to arrays
      await loadSettings()

      return newFieldId
    } catch (err) {
      error.value = 'فشل في إضافة الحقل'
      console.error('Error adding suspect field:', err)
      console.error('Original field data:', field)
      throw err
    }
  }

  async function updateSuspectField(fieldId: string, updates: Partial<SuspectField>) {
    try {
      // Deep clone and prepare updates for storage
      const updatesToStore: any = JSON.parse(JSON.stringify(updates))

      // Convert options array to JSON string
      if (updatesToStore.options && Array.isArray(updatesToStore.options)) {
        updatesToStore.options = JSON.stringify(updatesToStore.options)
      }

      // Convert validation.fileTypes array to JSON string
      if (updatesToStore.validation && updatesToStore.validation.fileTypes && Array.isArray(updatesToStore.validation.fileTypes)) {
        updatesToStore.validation.fileTypes = JSON.stringify(updatesToStore.validation.fileTypes)
      }

      await db.fields.update(fieldId, updatesToStore)

      // Reload settings to get updated data
      await loadSettings()
    } catch (err) {
      error.value = 'فشل في تحديث الحقل'
      console.error('Error updating suspect field:', err)
      throw err
    }
  }

  async function deleteSuspectField(fieldId: string) {
    try {
      await db.fields.delete(fieldId)

      // Reload settings to get updated data
      await loadSettings()
    } catch (err) {
      error.value = 'فشل في حذف الحقل'
      console.error('Error deleting suspect field:', err)
      throw err
    }
  }

  async function updateDeveloperInfo(developerInfo: DeveloperInfo) {
    try {
      if (!settings.value) return

      const updatedSettings = {
        ...settings.value,
        developerInfo,
        updatedAt: new Date()
      }

      await db.settings.update(settings.value.id!, {
        developerInfo,
        updatedAt: new Date()
      })

      settings.value = updatedSettings
    } catch (err) {
      error.value = 'فشل في تحديث معلومات المطور'
      console.error('Error updating developer info:', err)
      throw err
    }
  }

  async function updateBackupSettings(newBackup: Partial<BackupSettings>) {
    try {
      if (!settings.value) return

      const updatedBackup = { ...settings.value.backup, ...newBackup }
      const updatedSettings = { ...settings.value, backup: updatedBackup }
      
      await db.settings.update(settings.value.id!, { backup: updatedBackup })
      settings.value = updatedSettings
    } catch (err) {
      error.value = 'فشل في تحديث إعدادات النسخ الاحتياطي'
      console.error('Error updating backup settings:', err)
    }
  }

  async function exportSettings() {
    try {
      const data = await db.exportData()
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `settings-backup-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      return true
    } catch (err) {
      error.value = 'فشل في تصدير الإعدادات'
      console.error('Error exporting settings:', err)
      return false
    }
  }

  async function importSettings(file: File) {
    try {
      const text = await file.text()
      const data = JSON.parse(text)
      
      await db.importData(data)
      await loadSettings()
      
      return true
    } catch (err) {
      error.value = 'فشل في استيراد الإعدادات'
      console.error('Error importing settings:', err)
      return false
    }
  }

  // Helper functions
  async function createDefaultSettings() {
    const defaultSettings: Omit<AppSettings, 'id'> = {
      theme: getDefaultTheme(),
      organization: getDefaultOrganization(),
      fields: [],
      users: [],
      backup: getDefaultBackup(),
      developerInfo: getDefaultDeveloperInfo()
    }

    const id = await db.settings.add(defaultSettings)
    settings.value = { ...defaultSettings, id }

    // Apply default theme to document
    applyThemeToDocument(defaultSettings.theme)
  }

  function getDefaultTheme(): ThemeSettings {
    return {
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      backgroundColor: '#f8fafc',
      fontFamily: 'Cairo',
      fontSize: 14,
      borderRadius: 20,
      logoPosition: 'top-center',
      logoSize: 100
    }
  }

  function getDefaultOrganization(): OrganizationSettings {
    return {
      name: 'اسم المؤسسة',
      address: 'عنوان المؤسسة',
      phone: '+966xxxxxxxxx',
      email: '<EMAIL>'
    }
  }

  function getDefaultBackup(): BackupSettings {
    return {
      autoBackup: false,
      backupInterval: 24,
      maxBackups: 10
    }
  }

  function getDefaultDeveloperInfo(): DeveloperInfo {
    return {
      title: 'تطوير وتصميم',
      developerName: 'م- محرم اليفرسي',
      supervisorName: 'ق/عبدالرحمن اليفرسي',
      icon: 'fas fa-code'
    }
  }

  function applyThemeToDocument(theme: ThemeSettings) {
    const root = document.documentElement
    root.style.setProperty('--primary-color', theme.primaryColor)
    root.style.setProperty('--secondary-color', theme.secondaryColor)
    root.style.setProperty('--background-color', theme.backgroundColor)
    root.style.setProperty('--font-family', theme.fontFamily)
    root.style.setProperty('--font-size', `${theme.fontSize}px`)
    root.style.setProperty('--border-radius', `${theme.borderRadius}px`)
  }

  // Initialize
  loadSettings()

  return {
    // State
    settings,
    isLoading,
    error,
    
    // Getters
    theme,
    organization,
    suspectFields,
    backup,
    
    // Actions
    loadSettings,
    updateTheme,
    updateOrganization,
    updateSuspectFields,
    addSuspectField,
    updateSuspectField,
    deleteSuspectField,
    updateDeveloperInfo,
    updateBackupSettings,
    exportSettings,
    importSettings
  }
})
