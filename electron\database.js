const Database = require('better-sqlite3')
const path = require('path')
const fs = require('fs')
const { app } = require('electron')

class DatabaseService {
  constructor() {
    this.db = null
    this.isInitialized = false
    this.dbPath = null
  }

  /**
   * تهيئة قاعدة البيانات
   */
  async initialize() {
    try {
      // تحديد مسار قاعدة البيانات
      const userDataPath = app.getPath('userData')
      this.dbPath = path.join(userDataPath, 'suspects_data.db')
      
      console.log('📁 Database path:', this.dbPath)
      
      // التأكد من وجود مجلد البيانات
      if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true })
      }

      // فتح قاعدة البيانات
      this.db = new Database(this.dbPath)
      
      // تفعيل WAL mode للأداء الأفضل
      this.db.pragma('journal_mode = WAL')
      this.db.pragma('synchronous = NORMAL')
      this.db.pragma('cache_size = 1000000')
      this.db.pragma('temp_store = memory')
      
      // إنشاء الجداول
      await this.createTables()
      
      // إنشاء الفهارس
      await this.createIndexes()
      
      this.isInitialized = true
      console.log('✅ Database initialized successfully')
      
      return true
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw error
    }
  }

  /**
   * إنشاء الجداول الأساسية
   */
  async createTables() {
    const tables = [
      // جدول المتهمين
      `CREATE TABLE IF NOT EXISTS suspects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active',
        search_text TEXT
      )`,
      
      // جدول التبويبات الديناميكية
      `CREATE TABLE IF NOT EXISTS database_tabs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        columns TEXT NOT NULL,
        rows TEXT NOT NULL,
        settings TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول البيانات المؤقتة للبحث
      `CREATE TABLE IF NOT EXISTS temp_search_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tab_name TEXT NOT NULL,
        data TEXT NOT NULL,
        search_session TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الإعدادات
      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المستخدمين
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT,
        role TEXT NOT NULL,
        permissions TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول سجل النشاط
      `CREATE TABLE IF NOT EXISTS activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        details TEXT,
        ip_address TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`
    ]

    for (const tableSQL of tables) {
      this.db.exec(tableSQL)
    }
    
    console.log('✅ Tables created successfully')
  }

  /**
   * إنشاء الفهارس لتحسين الأداء
   */
  async createIndexes() {
    const indexes = [
      // فهارس جدول المتهمين
      'CREATE INDEX IF NOT EXISTS idx_suspects_status ON suspects(status)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_created_at ON suspects(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_search_text ON suspects(search_text)',
      
      // فهارس جدول التبويبات
      'CREATE INDEX IF NOT EXISTS idx_database_tabs_name ON database_tabs(name)',
      'CREATE INDEX IF NOT EXISTS idx_database_tabs_created_at ON database_tabs(created_at)',
      
      // فهارس البيانات المؤقتة
      'CREATE INDEX IF NOT EXISTS idx_temp_search_session ON temp_search_data(search_session)',
      'CREATE INDEX IF NOT EXISTS idx_temp_search_tab_name ON temp_search_data(tab_name)',
      
      // فهارس المستخدمين
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)',
      
      // فهارس سجل النشاط
      'CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON activity_log(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_activity_log_action ON activity_log(action)'
    ]

    for (const indexSQL of indexes) {
      this.db.exec(indexSQL)
    }
    
    console.log('✅ Indexes created successfully')
  }

  /**
   * إعداد البحث النصي الكامل (FTS)
   */
  async setupFullTextSearch() {
    try {
      // إنشاء جدول FTS للمتهمين
      this.db.exec(`
        CREATE VIRTUAL TABLE IF NOT EXISTS suspects_fts USING fts5(
          id UNINDEXED,
          search_text,
          content='suspects',
          content_rowid='id'
        )
      `)
      
      // إنشاء جدول FTS للتبويبات
      this.db.exec(`
        CREATE VIRTUAL TABLE IF NOT EXISTS database_tabs_fts USING fts5(
          id UNINDEXED,
          name,
          search_content,
          content='database_tabs',
          content_rowid='id'
        )
      `)
      
      // إنشاء triggers لمزامنة FTS
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_insert AFTER INSERT ON suspects BEGIN
          INSERT INTO suspects_fts(rowid, search_text) VALUES (new.id, new.search_text);
        END
      `)
      
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_update AFTER UPDATE ON suspects BEGIN
          UPDATE suspects_fts SET search_text = new.search_text WHERE rowid = new.id;
        END
      `)
      
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_delete AFTER DELETE ON suspects BEGIN
          DELETE FROM suspects_fts WHERE rowid = old.id;
        END
      `)
      
      console.log('✅ Full-text search setup completed')
    } catch (error) {
      console.error('❌ FTS setup failed:', error)
    }
  }

  /**
   * تنفيذ استعلام
   */
  query(sql, params = []) {
    if (!this.isInitialized) {
      throw new Error('Database not initialized')
    }
    
    try {
      const stmt = this.db.prepare(sql)
      return stmt.all(params)
    } catch (error) {
      console.error('❌ Query failed:', error)
      throw error
    }
  }

  /**
   * تنفيذ استعلام واحد
   */
  get(sql, params = []) {
    if (!this.isInitialized) {
      throw new Error('Database not initialized')
    }
    
    try {
      const stmt = this.db.prepare(sql)
      return stmt.get(params)
    } catch (error) {
      console.error('❌ Get query failed:', error)
      throw error
    }
  }

  /**
   * تنفيذ استعلام تحديث/إدراج/حذف
   */
  run(sql, params = []) {
    if (!this.isInitialized) {
      throw new Error('Database not initialized')
    }
    
    try {
      const stmt = this.db.prepare(sql)
      return stmt.run(params)
    } catch (error) {
      console.error('❌ Run query failed:', error)
      throw error
    }
  }

  /**
   * تنفيذ معاملة
   */
  transaction(callback) {
    if (!this.isInitialized) {
      throw new Error('Database not initialized')
    }
    
    const transaction = this.db.transaction(callback)
    return transaction
  }

  /**
   * بحث نصي كامل
   */
  fullTextSearch(table, query, limit = 100) {
    if (!this.isInitialized) {
      throw new Error('Database not initialized')
    }
    
    try {
      const ftsTable = `${table}_fts`
      const sql = `
        SELECT * FROM ${ftsTable} 
        WHERE ${ftsTable} MATCH ? 
        ORDER BY rank 
        LIMIT ?
      `
      return this.query(sql, [query, limit])
    } catch (error) {
      console.error('❌ Full-text search failed:', error)
      throw error
    }
  }

  /**
   * إغلاق قاعدة البيانات
   */
  close() {
    if (this.db) {
      this.db.close()
      this.isInitialized = false
      console.log('✅ Database closed')
    }
  }

  /**
   * الحصول على معلومات قاعدة البيانات
   */
  getInfo() {
    if (!this.isInitialized) {
      return null
    }
    
    try {
      const info = {
        path: this.dbPath,
        size: fs.statSync(this.dbPath).size,
        tables: this.query("SELECT name FROM sqlite_master WHERE type='table'"),
        indexes: this.query("SELECT name FROM sqlite_master WHERE type='index'"),
        pragma: {
          journal_mode: this.db.pragma('journal_mode', { simple: true }),
          synchronous: this.db.pragma('synchronous', { simple: true }),
          cache_size: this.db.pragma('cache_size', { simple: true })
        }
      }
      
      return info
    } catch (error) {
      console.error('❌ Failed to get database info:', error)
      return null
    }
  }
}

// إنشاء instance واحد
const databaseService = new DatabaseService()

module.exports = databaseService
