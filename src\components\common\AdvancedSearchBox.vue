<template>
  <div class="advanced-search-box">
    <!-- شريط البحث الرئيسي -->
    <div class="search-input-container">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="placeholder"
          @input="handleInput"
          @keydown.enter="performSearch"
          @focus="showSuggestions = true"
          :disabled="isLoading"
        />
        
        <!-- زر مسح البحث -->
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="clear-button"
          type="button"
        >
          <i class="fas fa-times"></i>
        </button>
        
        <!-- مؤشر التحميل -->
        <div v-if="isLoading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
      </div>
      
      <!-- زر الإعدادات المتقدمة -->
      <button
        @click="showAdvancedOptions = !showAdvancedOptions"
        class="advanced-options-toggle"
        :class="{ active: showAdvancedOptions }"
        type="button"
      >
        <i class="fas fa-sliders-h"></i>
      </button>
    </div>

    <!-- الخيارات المتقدمة -->
    <div v-if="showAdvancedOptions" class="advanced-options">
      <div class="options-grid">
        <!-- حد النتائج -->
        <div class="option-group">
          <label class="option-label">عدد النتائج</label>
          <select v-model="searchOptions.limit" class="option-select">
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="500">500</option>
            <option value="1000">1000</option>
          </select>
        </div>

        <!-- دقة البحث -->
        <div class="option-group">
          <label class="option-label">دقة البحث</label>
          <select v-model="searchOptions.threshold" class="option-select">
            <option value="0.1">عالية جداً</option>
            <option value="0.3">عالية</option>
            <option value="0.5">متوسطة</option>
            <option value="0.7">منخفضة</option>
          </select>
        </div>

        <!-- ترتيب النتائج -->
        <div class="option-group">
          <label class="option-label">ترتيب حسب</label>
          <select v-model="searchOptions.sortBy" class="option-select">
            <option value="relevance">الصلة</option>
            <option value="date">التاريخ</option>
            <option value="name">الاسم</option>
          </select>
        </div>

        <!-- اتجاه الترتيب -->
        <div class="option-group">
          <label class="option-label">الاتجاه</label>
          <select v-model="searchOptions.sortOrder" class="option-select">
            <option value="desc">تنازلي</option>
            <option value="asc">تصاعدي</option>
          </select>
        </div>
      </div>
    </div>

    <!-- اقتراحات البحث -->
    <div v-if="showSuggestions && suggestions.length > 0" class="search-suggestions">
      <div class="suggestions-header">
        <span>اقتراحات البحث</span>
        <button @click="showSuggestions = false" class="close-suggestions">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="suggestions-list">
        <button
          v-for="suggestion in suggestions"
          :key="suggestion"
          @click="selectSuggestion(suggestion)"
          class="suggestion-item"
        >
          <i class="fas fa-history"></i>
          {{ suggestion }}
        </button>
      </div>
    </div>

    <!-- إحصائيات البحث -->
    <div v-if="searchStats" class="search-stats">
      <div class="stats-item">
        <i class="fas fa-list-ol"></i>
        <span>{{ searchStats.totalResults }} نتيجة</span>
      </div>
      <div class="stats-item">
        <i class="fas fa-clock"></i>
        <span>{{ Math.round(searchStats.searchTime) }} مللي ثانية</span>
      </div>
      <div class="stats-item">
        <i class="fas fa-cog"></i>
        <span>{{ getMethodLabel(searchStats.method) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface SearchOptions {
  limit: number
  threshold: number
  sortBy: 'relevance' | 'date' | 'name'
  sortOrder: 'asc' | 'desc'
}

interface SearchStats {
  totalResults: number
  searchTime: number
  method: string
  query: string
}

// Props
interface Props {
  placeholder?: string
  debounceMs?: number
  autoSearch?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'ابحث...',
  debounceMs: 300,
  autoSearch: true
})

// Emits
const emit = defineEmits<{
  search: [query: string, options: SearchOptions]
  clear: []
  statsUpdate: [stats: SearchStats]
}>()

// State
const searchQuery = ref('')
const isLoading = ref(false)
const showAdvancedOptions = ref(false)
const showSuggestions = ref(false)
const suggestions = ref<string[]>([])
const searchStats = ref<SearchStats | null>(null)

const searchOptions = ref<SearchOptions>({
  limit: 100,
  threshold: 0.3,
  sortBy: 'relevance',
  sortOrder: 'desc'
})

// Debounce timer
let debounceTimer: NodeJS.Timeout | null = null

// Methods
function handleInput() {
  if (!props.autoSearch) return

  // إلغاء المؤقت السابق
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // تعيين مؤقت جديد
  debounceTimer = setTimeout(() => {
    if (searchQuery.value.trim()) {
      performSearch()
    } else {
      clearSearch()
    }
  }, props.debounceMs)
}

function performSearch() {
  if (!searchQuery.value.trim()) return

  isLoading.value = true
  showSuggestions.value = false

  // إضافة للاقتراحات
  addToSuggestions(searchQuery.value.trim())

  // إرسال البحث
  emit('search', searchQuery.value.trim(), searchOptions.value)
}

function clearSearch() {
  searchQuery.value = ''
  searchStats.value = null
  showSuggestions.value = false
  emit('clear')
}

function selectSuggestion(suggestion: string) {
  searchQuery.value = suggestion
  showSuggestions.value = false
  performSearch()
}

function addToSuggestions(query: string) {
  // إزالة الاستعلام إذا كان موجوداً
  const index = suggestions.value.indexOf(query)
  if (index > -1) {
    suggestions.value.splice(index, 1)
  }

  // إضافة في المقدمة
  suggestions.value.unshift(query)

  // الاحتفاظ بآخر 10 اقتراحات فقط
  if (suggestions.value.length > 10) {
    suggestions.value = suggestions.value.slice(0, 10)
  }

  // حفظ في localStorage
  localStorage.setItem('search_suggestions', JSON.stringify(suggestions.value))
}

function loadSuggestions() {
  try {
    const saved = localStorage.getItem('search_suggestions')
    if (saved) {
      suggestions.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load search suggestions:', error)
  }
}

function getMethodLabel(method: string): string {
  switch (method) {
    case 'fts':
      return 'بحث نصي كامل'
    case 'fuzzy':
      return 'بحث ذكي'
    case 'exact':
      return 'بحث دقيق'
    case 'fallback':
      return 'بحث احتياطي'
    default:
      return method
  }
}

// Watchers
watch(() => searchStats.value, (newStats) => {
  if (newStats) {
    emit('statsUpdate', newStats)
  }
})

// Lifecycle
onMounted(() => {
  loadSuggestions()
})

// Expose methods for parent component
defineExpose({
  setLoading: (loading: boolean) => {
    isLoading.value = loading
  },
  setStats: (stats: SearchStats) => {
    searchStats.value = stats
    isLoading.value = false
  },
  focus: () => {
    const input = document.querySelector('.search-input') as HTMLInputElement
    input?.focus()
  }
})
</script>

<style scoped>
.advanced-search-box {
  width: 100%;
  position: relative;
}

.search-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #64748b;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8fafc;
  direction: rtl;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input:disabled {
  background: #f1f5f9;
  cursor: not-allowed;
}

.clear-button {
  position: absolute;
  left: 12px;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-button:hover {
  color: #ef4444;
  background: #fef2f2;
}

.loading-indicator {
  position: absolute;
  left: 12px;
  color: #3b82f6;
}

.advanced-options-toggle {
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.advanced-options-toggle:hover,
.advanced-options-toggle.active {
  border-color: #3b82f6;
  color: #3b82f6;
  background: white;
}

.advanced-options {
  margin-top: 12px;
  padding: 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.option-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.option-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  direction: rtl;
}

.option-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 50;
  margin-top: 4px;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.close-suggestions {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-suggestions:hover {
  color: #ef4444;
  background: #fef2f2;
}

.suggestions-list {
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: right;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.2s ease;
}

.suggestion-item:hover {
  background: #f8fafc;
}

.suggestion-item i {
  color: #64748b;
  font-size: 0.875rem;
}

.search-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  padding: 8px 0;
  font-size: 0.75rem;
  color: #64748b;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-item i {
  font-size: 0.7rem;
}

@media (max-width: 768px) {
  .options-grid {
    grid-template-columns: 1fr;
  }
  
  .search-stats {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
