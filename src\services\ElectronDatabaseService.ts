/**
 * خدمة قاعدة البيانات لـ Electron
 * تتعامل مع SQLite من خلال IPC
 */

export class ElectronDatabaseService {
  private isElectron: boolean

  constructor() {
    this.isElectron = !!window.electronAPI
  }

  /**
   * التحقق من توفر Electron
   */
  isAvailable(): boolean {
    return this.isElectron
  }

  /**
   * تنفيذ استعلام SELECT
   */
  async query(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.dbQuery(sql, params)
    } catch (error) {
      console.error('Database query failed:', error)
      throw error
    }
  }

  /**
   * تنفيذ استعلام للحصول على صف واحد
   */
  async get(sql: string, params: any[] = []): Promise<any | null> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.dbGet(sql, params)
    } catch (error) {
      console.error('Database get failed:', error)
      throw error
    }
  }

  /**
   * تنفيذ استعلام INSERT/UPDATE/DELETE
   */
  async run(sql: string, params: any[] = []): Promise<{ lastInsertRowid: number; changes: number }> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.dbRun(sql, params)
    } catch (error) {
      console.error('Database run failed:', error)
      throw error
    }
  }

  /**
   * البحث النصي الكامل
   */
  async fullTextSearch(table: string, query: string, limit: number = 100): Promise<any[]> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.dbFullTextSearch(table, query, limit)
    } catch (error) {
      console.error('Full-text search failed:', error)
      throw error
    }
  }

  /**
   * الحصول على معلومات قاعدة البيانات
   */
  async getInfo(): Promise<any> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.dbGetInfo()
    } catch (error) {
      console.error('Get database info failed:', error)
      throw error
    }
  }

  // ===== وظائف خاصة بالمتهمين =====

  /**
   * إضافة متهم جديد
   */
  async addSuspect(suspectData: any): Promise<{ lastInsertRowid: number; changes: number }> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.suspectsAdd(suspectData)
    } catch (error) {
      console.error('Add suspect failed:', error)
      throw error
    }
  }

  /**
   * الحصول على جميع المتهمين
   */
  async getAllSuspects(): Promise<any[]> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.suspectsGetAll()
    } catch (error) {
      console.error('Get all suspects failed:', error)
      throw error
    }
  }

  /**
   * البحث في المتهمين
   */
  async searchSuspects(query: string, limit: number = 100): Promise<any[]> {
    if (!this.isElectron) {
      throw new Error('Electron database not available')
    }

    try {
      return await window.electronAPI!.suspectsSearch(query, limit)
    } catch (error) {
      console.error('Search suspects failed:', error)
      throw error
    }
  }

  // ===== وظائف خاصة بالتبويبات الديناميكية =====

  /**
   * إضافة تبويب جديد
   */
  async addDatabaseTab(name: string, columns: any[], rows: any[], settings: any = {}): Promise<{ lastInsertRowid: number; changes: number }> {
    const sql = `
      INSERT INTO database_tabs (name, columns, rows, settings, updated_at) 
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `
    const params = [
      name,
      JSON.stringify(columns),
      JSON.stringify(rows),
      JSON.stringify(settings)
    ]

    return await this.run(sql, params)
  }

  /**
   * تحديث تبويب موجود
   */
  async updateDatabaseTab(id: number, columns: any[], rows: any[], settings: any = {}): Promise<{ lastInsertRowid: number; changes: number }> {
    const sql = `
      UPDATE database_tabs 
      SET columns = ?, rows = ?, settings = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `
    const params = [
      JSON.stringify(columns),
      JSON.stringify(rows),
      JSON.stringify(settings),
      id
    ]

    return await this.run(sql, params)
  }

  /**
   * الحصول على جميع التبويبات
   */
  async getAllDatabaseTabs(): Promise<any[]> {
    const sql = 'SELECT * FROM database_tabs ORDER BY created_at ASC'
    const tabs = await this.query(sql)
    
    return tabs.map(tab => ({
      ...tab,
      columns: JSON.parse(tab.columns),
      rows: JSON.parse(tab.rows),
      settings: JSON.parse(tab.settings || '{}')
    }))
  }

  /**
   * حذف تبويب
   */
  async deleteDatabaseTab(id: number): Promise<{ lastInsertRowid: number; changes: number }> {
    const sql = 'DELETE FROM database_tabs WHERE id = ?'
    return await this.run(sql, [id])
  }

  /**
   * البحث في التبويبات
   */
  async searchInDatabaseTabs(searchQuery: string, tabNames: string[] = []): Promise<any[]> {
    let sql = `
      SELECT * FROM database_tabs 
      WHERE (name LIKE ? OR rows LIKE ?)
    `
    let params = [`%${searchQuery}%`, `%${searchQuery}%`]

    if (tabNames.length > 0) {
      const placeholders = tabNames.map(() => '?').join(',')
      sql += ` AND name IN (${placeholders})`
      params = params.concat(tabNames)
    }

    sql += ' ORDER BY created_at ASC'

    const tabs = await this.query(sql, params)
    
    return tabs.map(tab => ({
      ...tab,
      columns: JSON.parse(tab.columns),
      rows: JSON.parse(tab.rows),
      settings: JSON.parse(tab.settings || '{}')
    }))
  }

  // ===== وظائف خاصة بالبيانات المؤقتة =====

  /**
   * حفظ بيانات بحث مؤقتة
   */
  async saveTempSearchData(tabName: string, data: any, sessionId: string): Promise<{ lastInsertRowid: number; changes: number }> {
    const sql = `
      INSERT INTO temp_search_data (tab_name, data, search_session) 
      VALUES (?, ?, ?)
    `
    const params = [tabName, JSON.stringify(data), sessionId]

    return await this.run(sql, params)
  }

  /**
   * الحصول على البيانات المؤقتة
   */
  async getTempSearchData(sessionId: string): Promise<any[]> {
    const sql = 'SELECT * FROM temp_search_data WHERE search_session = ? ORDER BY created_at ASC'
    const results = await this.query(sql, [sessionId])
    
    return results.map(result => ({
      ...result,
      data: JSON.parse(result.data)
    }))
  }

  /**
   * مسح البيانات المؤقتة
   */
  async clearTempSearchData(sessionId?: string): Promise<{ lastInsertRowid: number; changes: number }> {
    if (sessionId) {
      const sql = 'DELETE FROM temp_search_data WHERE search_session = ?'
      return await this.run(sql, [sessionId])
    } else {
      const sql = 'DELETE FROM temp_search_data'
      return await this.run(sql)
    }
  }

  // ===== وظائف الإعدادات =====

  /**
   * حفظ إعداد
   */
  async saveSetting(key: string, value: any): Promise<{ lastInsertRowid: number; changes: number }> {
    const sql = `
      INSERT OR REPLACE INTO app_settings (key, value, updated_at) 
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `
    const params = [key, JSON.stringify(value)]

    return await this.run(sql, params)
  }

  /**
   * الحصول على إعداد
   */
  async getSetting(key: string): Promise<any | null> {
    const sql = 'SELECT value FROM app_settings WHERE key = ?'
    const result = await this.get(sql, [key])
    
    if (result) {
      try {
        return JSON.parse(result.value)
      } catch (error) {
        return result.value
      }
    }
    
    return null
  }

  /**
   * الحصول على جميع الإعدادات
   */
  async getAllSettings(): Promise<Record<string, any>> {
    const sql = 'SELECT key, value FROM app_settings'
    const results = await this.query(sql)
    
    const settings: Record<string, any> = {}
    
    for (const result of results) {
      try {
        settings[result.key] = JSON.parse(result.value)
      } catch (error) {
        settings[result.key] = result.value
      }
    }
    
    return settings
  }
}

// إنشاء instance واحد
export const electronDatabaseService = new ElectronDatabaseService()

// تحديث تعريف window.electronAPI
declare global {
  interface Window {
    electronAPI?: {
      // Database operations
      dbQuery: (sql: string, params: any[]) => Promise<any[]>
      dbGet: (sql: string, params: any[]) => Promise<any | null>
      dbRun: (sql: string, params: any[]) => Promise<{ lastInsertRowid: number; changes: number }>
      dbFullTextSearch: (table: string, query: string, limit: number) => Promise<any[]>
      dbGetInfo: () => Promise<any>
      
      // Suspects operations
      suspectsAdd: (data: any) => Promise<{ lastInsertRowid: number; changes: number }>
      suspectsGetAll: () => Promise<any[]>
      suspectsSearch: (query: string, limit: number) => Promise<any[]>
      
      // Activation operations
      getActivationStatus: () => Promise<{activated: boolean, date: string}>
      saveActivationStatus: (data: {activated: boolean, date: string, code: string}) => Promise<void>
      clearActivationStatus: () => Promise<void>
      
      // Other operations
      getVersion: () => Promise<string>
      getUserDataPath: () => Promise<string>
      showMessageBox: (options: any) => Promise<any>
    }
  }
}
