const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const fs = require('fs').promises
const os = require('os')
const databaseService = require('./database-simple')
const ImportService = require('./import-service')
const updaterService = require('./updater')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow
let importService

// مسار ملف الإعدادات
const userDataPath = app.getPath('userData')
const settingsPath = path.join(userDataPath, 'app-settings.json')
const activationPath = path.join(userDataPath, 'activation.json')

function createWindow() {
  // Create the browser window with enhanced security
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      sandbox: false, // نحتاج false للوصول لقاعدة البيانات
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      enableBlinkFeatures: '',
      disableBlinkFeatures: '',
      // Content Security Policy
      additionalArguments: [
        '--disable-web-security=false',
        '--disable-features=VizDisplayCompositor'
      ]
    },
    icon: path.join(__dirname, '../public/icon.png'),
    show: false,
    titleBarStyle: 'default',
    // إعدادات أمان إضافية
    autoHideMenuBar: !isDev,
    fullscreenable: true,
    maximizable: true,
    resizable: true
  })

  // Load the app
  if (isDev) {
    console.log('🔧 Loading development server...')
    mainWindow.loadURL('http://localhost:5175')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    console.log('📦 Loading production build...')
    const indexPath = path.join(__dirname, '../dist/index.html')
    console.log('📁 Index path:', indexPath)
    mainWindow.loadFile(indexPath)
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    console.log('✅ Window ready to show')
    mainWindow.show()

    // إعداد خدمة التحديث
    updaterService.setMainWindow(mainWindow)

    // بدء فحص التحديثات في الإنتاج فقط
    if (!isDev) {
      updaterService.setupAutoUpdateOnStartup()
    }
  })

  // Handle loading errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ Failed to load:', errorCode, errorDescription)
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Set up menu
  createMenu()
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            // Show about dialog
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// App event handlers
app.whenReady().then(async () => {
  console.log('🚀 Electron app is ready')

  // تهيئة قاعدة البيانات
  try {
    await databaseService.initialize()
    await databaseService.setupFullTextSearch()
    console.log('✅ Database service initialized')

    // تهيئة خدمة الاستيراد
    importService = new ImportService(databaseService)
    console.log('✅ Import service initialized')
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    // يمكن إظهار رسالة خطأ للمستخدم هنا
  }

  createWindow()
})

app.on('window-all-closed', () => {
  // إغلاق قاعدة البيانات
  databaseService.close()

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// ===== إعدادات الأمان المتقدمة =====

// منع إنشاء نوافذ جديدة
app.on('web-contents-created', (event, contents) => {
  // منع فتح نوافذ جديدة
  contents.on('new-window', (event, navigationUrl) => {
    console.warn('🚫 Blocked new window creation:', navigationUrl)
    event.preventDefault()
  })

  // منع التنقل لمواقع خارجية
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    // السماح فقط بالتنقل المحلي
    if (parsedUrl.origin !== 'http://localhost:5175' && parsedUrl.origin !== 'file://') {
      console.warn('🚫 Blocked external navigation:', navigationUrl)
      event.preventDefault()
    }
  })

  // منع تحميل الموارد الخارجية غير المصرح بها
  contents.session.webRequest.onBeforeRequest((details, callback) => {
    const url = new URL(details.url)

    // قائمة المجالات المسموحة
    const allowedOrigins = [
      'http://localhost:5175',
      'file://',
      'chrome-extension://', // للـ DevTools
      'devtools://'
    ]

    const isAllowed = allowedOrigins.some(origin => details.url.startsWith(origin))

    if (!isAllowed) {
      console.warn('🚫 Blocked external resource:', details.url)
      callback({ cancel: true })
    } else {
      callback({ cancel: false })
    }
  })

  // تطبيق Content Security Policy
  contents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
          "style-src 'self' 'unsafe-inline'; " +
          "img-src 'self' data: blob:; " +
          "font-src 'self' data:; " +
          "connect-src 'self'; " +
          "media-src 'self'; " +
          "object-src 'none'; " +
          "base-uri 'self'; " +
          "form-action 'self';"
        ]
      }
    })
  })
})

// منع تشغيل JavaScript غير مصرح به
app.on('web-contents-created', (event, contents) => {
  contents.on('dom-ready', () => {
    // حقن كود أمان إضافي
    contents.executeJavaScript(`
      // منع console.log في الإنتاج
      if (!${isDev}) {
        console.log = console.warn = console.error = () => {};
      }

      // منع الوصول لـ DevTools في الإنتاج
      if (!${isDev}) {
        document.addEventListener('keydown', (e) => {
          if (e.key === 'F12' ||
              (e.ctrlKey && e.shiftKey && e.key === 'I') ||
              (e.ctrlKey && e.shiftKey && e.key === 'C') ||
              (e.ctrlKey && e.key === 'U')) {
            e.preventDefault();
            return false;
          }
        });

        document.addEventListener('contextmenu', (e) => {
          e.preventDefault();
          return false;
        });
      }
    `)
  })
})

// ===== دوال إدارة التفعيل =====

/**
 * إنشاء مجلد البيانات إذا لم يكن موجوداً
 */
async function ensureUserDataDirectory() {
  try {
    await fs.access(userDataPath)
  } catch (error) {
    await fs.mkdir(userDataPath, { recursive: true })
    console.log('✅ تم إنشاء مجلد البيانات:', userDataPath)
  }
}

/**
 * قراءة حالة التفعيل
 */
async function getActivationStatus() {
  try {
    await ensureUserDataDirectory()
    const data = await fs.readFile(activationPath, 'utf8')
    const activation = JSON.parse(data)

    // التحقق من صحة البيانات
    if (activation && activation.activated && activation.date) {
      console.log('✅ تم العثور على حالة تفعيل صالحة')
      return activation
    }

    return { activated: false }
  } catch (error) {
    // إذا لم يوجد الملف أو كان تالفاً
    console.log('ℹ️ لم يتم العثور على ملف التفعيل')
    return { activated: false }
  }
}

/**
 * حفظ حالة التفعيل
 */
async function saveActivationStatus(activationData) {
  try {
    await ensureUserDataDirectory()

    const dataToSave = {
      activated: activationData.activated,
      date: activationData.date,
      codeHash: activationData.code, // سيتم تشفيره في الواجهة الأمامية
      platform: os.platform(),
      arch: os.arch(),
      version: app.getVersion(),
      savedAt: new Date().toISOString()
    }

    await fs.writeFile(activationPath, JSON.stringify(dataToSave, null, 2), 'utf8')
    console.log('✅ تم حفظ حالة التفعيل بنجاح')

    return true
  } catch (error) {
    console.error('❌ خطأ في حفظ حالة التفعيل:', error)
    throw error
  }
}

/**
 * مسح حالة التفعيل
 */
async function clearActivationStatus() {
  try {
    await fs.unlink(activationPath)
    console.log('✅ تم مسح حالة التفعيل')
    return true
  } catch (error) {
    if (error.code === 'ENOENT') {
      // الملف غير موجود أصلاً
      return true
    }
    console.error('❌ خطأ في مسح حالة التفعيل:', error)
    throw error
  }
}

// ===== معالجات IPC للتفعيل =====

ipcMain.handle('activation:getStatus', async () => {
  return await getActivationStatus()
})

ipcMain.handle('activation:saveStatus', async (event, activationData) => {
  return await saveActivationStatus(activationData)
})

ipcMain.handle('activation:clearStatus', async () => {
  return await clearActivationStatus()
})

// معالجات IPC إضافية
ipcMain.handle('app:getVersion', () => {
  return app.getVersion()
})

ipcMain.handle('app:getUserDataPath', () => {
  return userDataPath
})

ipcMain.handle('dialog:showMessageBox', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})

// ===== معالجات IPC لقاعدة البيانات =====

ipcMain.handle('db:query', async (event, sql, params) => {
  try {
    return databaseService.query(sql, params)
  } catch (error) {
    console.error('❌ Database query failed:', error)
    throw error
  }
})

ipcMain.handle('db:get', async (event, sql, params) => {
  try {
    return databaseService.get(sql, params)
  } catch (error) {
    console.error('❌ Database get failed:', error)
    throw error
  }
})

ipcMain.handle('db:run', async (event, sql, params) => {
  try {
    return databaseService.run(sql, params)
  } catch (error) {
    console.error('❌ Database run failed:', error)
    throw error
  }
})

ipcMain.handle('db:fullTextSearch', async (event, table, query, limit) => {
  try {
    return databaseService.fullTextSearch(table, query, limit)
  } catch (error) {
    console.error('❌ Full-text search failed:', error)
    throw error
  }
})

ipcMain.handle('db:getInfo', async () => {
  try {
    return databaseService.getInfo()
  } catch (error) {
    console.error('❌ Get database info failed:', error)
    throw error
  }
})

// معالجات خاصة للمتهمين
ipcMain.handle('db:suspects:add', async (event, suspectData) => {
  try {
    const searchText = JSON.stringify(suspectData).toLowerCase()
    const result = databaseService.run(
      'INSERT INTO suspects (data, search_text) VALUES (?, ?)',
      [JSON.stringify(suspectData), searchText]
    )
    return result
  } catch (error) {
    console.error('❌ Add suspect failed:', error)
    throw error
  }
})

ipcMain.handle('db:suspects:getAll', async () => {
  try {
    const suspects = databaseService.query('SELECT * FROM suspects WHERE status = ? ORDER BY created_at DESC', ['active'])
    return suspects.map(suspect => ({
      ...suspect,
      data: JSON.parse(suspect.data)
    }))
  } catch (error) {
    console.error('❌ Get all suspects failed:', error)
    throw error
  }
})

ipcMain.handle('db:suspects:search', async (event, searchQuery, limit = 100) => {
  try {
    if (!searchQuery || searchQuery.trim() === '') {
      return []
    }

    // استخدام البحث النصي الكامل
    const results = databaseService.fullTextSearch('suspects', searchQuery, limit)

    // جلب البيانات الكاملة
    const suspectIds = results.map(r => r.id)
    if (suspectIds.length === 0) {
      return []
    }

    const placeholders = suspectIds.map(() => '?').join(',')
    const suspects = databaseService.query(
      `SELECT * FROM suspects WHERE id IN (${placeholders}) AND status = ?`,
      [...suspectIds, 'active']
    )

    return suspects.map(suspect => ({
      ...suspect,
      data: JSON.parse(suspect.data)
    }))
  } catch (error) {
    console.error('❌ Search suspects failed:', error)
    throw error
  }
})

// ===== معالجات IPC للاستيراد المتقدم =====

ipcMain.handle('import:selectFile', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'اختر ملف للاستيراد',
      filters: [
        { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      return { success: true, filePath: result.filePaths[0] }
    }

    return { success: false, cancelled: true }
  } catch (error) {
    console.error('❌ File selection failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('import:startImport', async (event, filePath, options) => {
  try {
    if (!importService) {
      throw new Error('Import service not initialized')
    }

    // بدء عملية الاستيراد في الخلفية
    const result = await importService.importLargeFile(filePath, options)
    return { success: true, result }
  } catch (error) {
    console.error('❌ Import failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('import:getProgress', async () => {
  try {
    if (!importService) {
      return { success: false, error: 'Import service not initialized' }
    }

    const progress = importService.getProgress()
    return { success: true, progress }
  } catch (error) {
    console.error('❌ Get import progress failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('import:cancel', async () => {
  try {
    if (!importService) {
      return { success: false, error: 'Import service not initialized' }
    }

    importService.cancelImport()
    return { success: true }
  } catch (error) {
    console.error('❌ Cancel import failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('import:isInProgress', async () => {
  try {
    if (!importService) {
      return { success: true, inProgress: false }
    }

    const inProgress = importService.isImportInProgress()
    return { success: true, inProgress }
  } catch (error) {
    console.error('❌ Check import status failed:', error)
    return { success: false, error: error.message }
  }
})

// ===== معالجات IPC للتحديث التلقائي =====

ipcMain.handle('updater:checkForUpdates', async () => {
  try {
    await updaterService.checkForUpdates(true)
    return { success: true }
  } catch (error) {
    console.error('❌ Check for updates failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('updater:downloadUpdate', async () => {
  try {
    await updaterService.downloadUpdate()
    return { success: true }
  } catch (error) {
    console.error('❌ Download update failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('updater:installUpdate', async () => {
  try {
    updaterService.installUpdate()
    return { success: true }
  } catch (error) {
    console.error('❌ Install update failed:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('updater:getInfo', async () => {
  try {
    const info = updaterService.getUpdateInfo()
    return { success: true, info }
  } catch (error) {
    console.error('❌ Get updater info failed:', error)
    return { success: false, error: error.message }
  }
})
