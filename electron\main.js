const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const fs = require('fs').promises
const os = require('os')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow

// مسار ملف الإعدادات
const userDataPath = app.getPath('userData')
const settingsPath = path.join(userDataPath, 'app-settings.json')
const activationPath = path.join(userDataPath, 'activation.json')

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app
  if (isDev) {
    console.log('🔧 Loading development server...')
    mainWindow.loadURL('http://localhost:5173')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    console.log('📦 Loading production build...')
    const indexPath = path.join(__dirname, '../dist/index.html')
    console.log('📁 Index path:', indexPath)
    mainWindow.loadFile(indexPath)
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    console.log('✅ Window ready to show')
    mainWindow.show()
  })

  // Handle loading errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ Failed to load:', errorCode, errorDescription)
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Set up menu
  createMenu()
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            // Show about dialog
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// App event handlers
app.whenReady().then(() => {
  console.log('🚀 Electron app is ready')
  createWindow()
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
  })
})

// ===== دوال إدارة التفعيل =====

/**
 * إنشاء مجلد البيانات إذا لم يكن موجوداً
 */
async function ensureUserDataDirectory() {
  try {
    await fs.access(userDataPath)
  } catch (error) {
    await fs.mkdir(userDataPath, { recursive: true })
    console.log('✅ تم إنشاء مجلد البيانات:', userDataPath)
  }
}

/**
 * قراءة حالة التفعيل
 */
async function getActivationStatus() {
  try {
    await ensureUserDataDirectory()
    const data = await fs.readFile(activationPath, 'utf8')
    const activation = JSON.parse(data)

    // التحقق من صحة البيانات
    if (activation && activation.activated && activation.date) {
      console.log('✅ تم العثور على حالة تفعيل صالحة')
      return activation
    }

    return { activated: false }
  } catch (error) {
    // إذا لم يوجد الملف أو كان تالفاً
    console.log('ℹ️ لم يتم العثور على ملف التفعيل')
    return { activated: false }
  }
}

/**
 * حفظ حالة التفعيل
 */
async function saveActivationStatus(activationData) {
  try {
    await ensureUserDataDirectory()

    const dataToSave = {
      activated: activationData.activated,
      date: activationData.date,
      codeHash: activationData.code, // سيتم تشفيره في الواجهة الأمامية
      platform: os.platform(),
      arch: os.arch(),
      version: app.getVersion(),
      savedAt: new Date().toISOString()
    }

    await fs.writeFile(activationPath, JSON.stringify(dataToSave, null, 2), 'utf8')
    console.log('✅ تم حفظ حالة التفعيل بنجاح')

    return true
  } catch (error) {
    console.error('❌ خطأ في حفظ حالة التفعيل:', error)
    throw error
  }
}

/**
 * مسح حالة التفعيل
 */
async function clearActivationStatus() {
  try {
    await fs.unlink(activationPath)
    console.log('✅ تم مسح حالة التفعيل')
    return true
  } catch (error) {
    if (error.code === 'ENOENT') {
      // الملف غير موجود أصلاً
      return true
    }
    console.error('❌ خطأ في مسح حالة التفعيل:', error)
    throw error
  }
}

// ===== معالجات IPC للتفعيل =====

ipcMain.handle('activation:getStatus', async () => {
  return await getActivationStatus()
})

ipcMain.handle('activation:saveStatus', async (event, activationData) => {
  return await saveActivationStatus(activationData)
})

ipcMain.handle('activation:clearStatus', async () => {
  return await clearActivationStatus()
})

// معالجات IPC إضافية
ipcMain.handle('app:getVersion', () => {
  return app.getVersion()
})

ipcMain.handle('app:getUserDataPath', () => {
  return userDataPath
})

ipcMain.handle('dialog:showMessageBox', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})
