<template>
  <div class="space-y-8">
    <!-- Charts Header -->
    <div class="neumorphic-card">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon">
            <i class="fas fa-chart-pie text-primary-600"></i>
          </div>
          <div>
            <h2 class="text-xl font-bold text-secondary-800">الرسوم البيانية الملخصة</h2>
            <p class="text-secondary-600">تحليل شامل لبيانات المتهمين والإحصائيات</p>
          </div>
        </div>
        
        <!-- Date Range Filter -->
        <div class="flex items-center gap-3">
          <select v-model="selectedPeriod" class="neumorphic-select">
            <option value="all">جميع الفترات</option>
            <option value="week">آخر أسبوع</option>
            <option value="month">آخر شهر</option>
            <option value="quarter">آخر 3 أشهر</option>
            <option value="year">آخر سنة</option>
          </select>
          <button
            @click="refreshData"
            class="neumorphic-button p-2 text-primary-600"
            title="تحديث البيانات"
          >
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Suspects -->
      <div class="neumorphic-card bg-gradient-to-br from-primary-50 to-primary-100 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-primary-800">إجمالي المتهمين</h3>
            <p class="text-3xl font-bold text-primary-600">{{ statistics.total }}</p>
            <p class="text-sm text-primary-600">{{ getPeriodText() }}</p>
          </div>
          <div class="neumorphic-icon bg-primary-200">
            <i class="fas fa-users text-primary-600 text-xl"></i>
          </div>
        </div>
      </div>

      <!-- Detained -->
      <div class="neumorphic-card bg-gradient-to-br from-danger-50 to-danger-100 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-danger-800">رهن الاعتقال</h3>
            <p class="text-3xl font-bold text-danger-600">{{ statistics.detained }}</p>
            <p class="text-sm text-danger-600">{{ statistics.detainedPercentage }}% من الإجمالي</p>
          </div>
          <div class="neumorphic-icon bg-danger-200">
            <i class="fas fa-lock text-danger-600 text-xl"></i>
          </div>
        </div>
      </div>

      <!-- Released -->
      <div class="neumorphic-card bg-gradient-to-br from-success-50 to-success-100 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-success-800">مفرج عنهم</h3>
            <p class="text-3xl font-bold text-success-600">{{ statistics.released }}</p>
            <p class="text-sm text-success-600">{{ statistics.releasedPercentage }}% من الإجمالي</p>
          </div>
          <div class="neumorphic-icon bg-success-200">
            <i class="fas fa-unlock text-success-600 text-xl"></i>
          </div>
        </div>
      </div>

      <!-- Transferred -->
      <div class="neumorphic-card bg-gradient-to-br from-blue-50 to-blue-100 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-blue-800">محالين للنيابة</h3>
            <p class="text-3xl font-bold text-blue-600">{{ statistics.transferred }}</p>
            <p class="text-sm text-blue-600">{{ statistics.transferredPercentage }}% من الإجمالي</p>
          </div>
          <div class="neumorphic-icon bg-blue-200">
            <i class="fas fa-exchange-alt text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Status Distribution Pie Chart -->
      <div class="neumorphic-card">
        <h3 class="text-lg font-bold text-secondary-800 mb-6 flex items-center gap-2">
          <i class="fas fa-chart-pie text-primary-600"></i>
          توزيع الحالات
        </h3>
        <div class="relative h-64 flex items-center justify-center">
          <svg viewBox="0 0 200 200" class="w-48 h-48">
            <!-- Pie Chart Segments -->
            <circle
              v-for="(segment, index) in pieChartSegments"
              :key="index"
              cx="100"
              cy="100"
              r="80"
              fill="none"
              :stroke="segment.color"
              stroke-width="20"
              :stroke-dasharray="segment.dashArray"
              :stroke-dashoffset="segment.dashOffset"
              class="transition-all duration-500"
            />
            <!-- Center Text -->
            <text x="100" y="95" text-anchor="middle" class="text-lg font-bold fill-secondary-800">
              {{ statistics.total }}
            </text>
            <text x="100" y="115" text-anchor="middle" class="text-sm fill-secondary-600">
              إجمالي
            </text>
          </svg>
        </div>
        
        <!-- Legend -->
        <div class="grid grid-cols-3 gap-4 mt-6">
          <div class="flex items-center gap-2">
            <div class="w-4 h-4 bg-danger-500 rounded"></div>
            <span class="text-sm text-secondary-700">معتقل ({{ statistics.detained }})</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-4 h-4 bg-success-500 rounded"></div>
            <span class="text-sm text-secondary-700">مفرج ({{ statistics.released }})</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-4 h-4 bg-blue-500 rounded"></div>
            <span class="text-sm text-secondary-700">محال ({{ statistics.transferred }})</span>
          </div>
        </div>
      </div>

      <!-- Monthly Trend Chart -->
      <div class="neumorphic-card">
        <h3 class="text-lg font-bold text-secondary-800 mb-6 flex items-center gap-2">
          <i class="fas fa-chart-line text-primary-600"></i>
          الاتجاه الشهري
        </h3>
        <div class="relative h-64">
          <svg viewBox="0 0 400 200" class="w-full h-full">
            <!-- Grid Lines -->
            <defs>
              <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#e2e8f0" stroke-width="1"/>
              </pattern>
            </defs>
            <rect width="400" height="200" fill="url(#grid)" />
            
            <!-- Line Chart -->
            <polyline
              :points="lineChartPoints"
              fill="none"
              stroke="#3b82f6"
              stroke-width="3"
              class="transition-all duration-500"
            />
            
            <!-- Data Points -->
            <circle
              v-for="(point, index) in monthlyData"
              :key="index"
              :cx="40 + (index * 40)"
              :cy="200 - (point.value * 4)"
              r="4"
              fill="#3b82f6"
              class="transition-all duration-500"
            />
          </svg>
          
          <!-- X-axis Labels -->
          <div class="flex justify-between mt-2 text-xs text-secondary-600">
            <span v-for="month in monthlyData" :key="month.label">
              {{ month.label }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Average Detention Time -->
      <div class="neumorphic-card">
        <h3 class="text-lg font-bold text-secondary-800 mb-4 flex items-center gap-2">
          <i class="fas fa-clock text-warning-600"></i>
          متوسط فترة الاعتقال
        </h3>
        <div class="text-center">
          <p class="text-3xl font-bold text-warning-600">{{ averageDetentionDays }}</p>
          <p class="text-sm text-secondary-600">يوم</p>
        </div>
      </div>

      <!-- Most Common Nationality -->
      <div class="neumorphic-card">
        <h3 class="text-lg font-bold text-secondary-800 mb-4 flex items-center gap-2">
          <i class="fas fa-flag text-purple-600"></i>
          الجنسية الأكثر شيوعاً
        </h3>
        <div class="text-center">
          <p class="text-xl font-bold text-purple-600">{{ mostCommonNationality.name }}</p>
          <p class="text-sm text-secondary-600">{{ mostCommonNationality.count }} متهم</p>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="neumorphic-card">
        <h3 class="text-lg font-bold text-secondary-800 mb-4 flex items-center gap-2">
          <i class="fas fa-activity text-indigo-600"></i>
          النشاط الأخير
        </h3>
        <div class="space-y-2">
          <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center gap-2 text-sm">
            <i :class="activity.icon + ' text-' + activity.color + '-600'"></i>
            <span class="text-secondary-700">{{ activity.text }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SuspectData } from '@/types'

// Props
interface Props {
  statistics: {
    total: number
    detained: number
    released: number
    transferred: number
    detainedPercentage: number
    releasedPercentage: number
    transferredPercentage: number
  }
  suspects: SuspectData[]
  loading: boolean
}

const props = defineProps<Props>()

// Reactive data
const selectedPeriod = ref('all')

// Computed
const pieChartSegments = computed(() => {
  const total = props.statistics.total
  if (total === 0) return []

  const circumference = 2 * Math.PI * 80 // radius = 80
  let currentOffset = 0

  const segments = []

  // Detained segment
  if (props.statistics.detained > 0) {
    const detainedPercentage = props.statistics.detained / total
    const detainedLength = circumference * detainedPercentage
    segments.push({
      color: '#ef4444',
      dashArray: `${detainedLength} ${circumference}`,
      dashOffset: -currentOffset
    })
    currentOffset += detainedLength
  }

  // Released segment
  if (props.statistics.released > 0) {
    const releasedPercentage = props.statistics.released / total
    const releasedLength = circumference * releasedPercentage
    segments.push({
      color: '#22c55e',
      dashArray: `${releasedLength} ${circumference}`,
      dashOffset: -currentOffset
    })
    currentOffset += releasedLength
  }

  // Transferred segment
  if (props.statistics.transferred > 0) {
    const transferredPercentage = props.statistics.transferred / total
    const transferredLength = circumference * transferredPercentage
    segments.push({
      color: '#3b82f6',
      dashArray: `${transferredLength} ${circumference}`,
      dashOffset: -currentOffset
    })
  }

  return segments
})

const monthlyData = computed(() => {
  // Generate mock monthly data for the last 6 months
  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
  return months.map((month, index) => ({
    label: month,
    value: Math.floor(Math.random() * 50) + 10 // Random data for demo
  }))
})

const lineChartPoints = computed(() => {
  return monthlyData.value
    .map((point, index) => `${40 + (index * 40)},${200 - (point.value * 4)}`)
    .join(' ')
})

const averageDetentionDays = computed(() => {
  const detainedSuspects = props.suspects.filter(s => !s.fields.isReleased && !s.fields.isTransferred)
  if (detainedSuspects.length === 0) return 0

  const totalDays = detainedSuspects.reduce((sum, suspect) => {
    // Find arrest date field properly
    let arrestDate: Date

    // Method 1: Look for arrest date field by label (need to get fields from parent)
    // For now, try common field names
    if (suspect.fields.arrestDate) {
      arrestDate = new Date(suspect.fields.arrestDate)
    } else if (suspect.fields['تاريخ_القبض']) {
      arrestDate = new Date(suspect.fields['تاريخ_القبض'])
    } else if (suspect.fields['تاريخ_الاعتقال']) {
      arrestDate = new Date(suspect.fields['تاريخ_الاعتقال'])
    } else if (suspect.fields['تاريخ_الإيقاف']) {
      arrestDate = new Date(suspect.fields['تاريخ_الإيقاف'])
    } else {
      // Fallback to creation date
      arrestDate = new Date(suspect.createdAt)
    }

    const now = new Date()
    const diffTime = Math.abs(now.getTime() - arrestDate.getTime())
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return sum + days
  }, 0)

  return Math.round(totalDays / detainedSuspects.length)
})

const mostCommonNationality = computed(() => {
  const nationalityCounts: Record<string, number> = {}

  props.suspects.forEach(suspect => {
    const nationality = suspect.fields.nationality || 'غير محدد'
    nationalityCounts[nationality] = (nationalityCounts[nationality] || 0) + 1
  })

  let maxCount = 0
  let mostCommon = 'غير محدد'

  Object.entries(nationalityCounts).forEach(([nationality, count]) => {
    if (count > maxCount) {
      maxCount = count
      mostCommon = nationality
    }
  })

  return {
    name: mostCommon,
    count: maxCount
  }
})

const recentActivities = computed(() => {
  // Generate recent activities based on suspects data
  const activities = []

  // Recent additions
  const recentSuspects = props.suspects
    .filter(s => {
      const daysDiff = Math.abs(new Date().getTime() - s.createdAt.getTime()) / (1000 * 60 * 60 * 24)
      return daysDiff <= 7
    })
    .length

  if (recentSuspects > 0) {
    activities.push({
      id: 1,
      icon: 'fas fa-user-plus',
      color: 'blue',
      text: `${recentSuspects} متهم جديد هذا الأسبوع`
    })
  }

  // Recent releases
  const recentReleases = props.suspects
    .filter(s => {
      if (!s.fields.isReleased || !s.fields.releaseDate) return false
      const daysDiff = Math.abs(new Date().getTime() - new Date(s.fields.releaseDate).getTime()) / (1000 * 60 * 60 * 24)
      return daysDiff <= 7
    })
    .length

  if (recentReleases > 0) {
    activities.push({
      id: 2,
      icon: 'fas fa-unlock',
      color: 'green',
      text: `${recentReleases} إفراج هذا الأسبوع`
    })
  }

  // Recent transfers
  const recentTransfers = props.suspects
    .filter(s => {
      if (!s.fields.isTransferred || !s.fields.transferDate) return false
      const daysDiff = Math.abs(new Date().getTime() - new Date(s.fields.transferDate).getTime()) / (1000 * 60 * 60 * 24)
      return daysDiff <= 7
    })
    .length

  if (recentTransfers > 0) {
    activities.push({
      id: 3,
      icon: 'fas fa-exchange-alt',
      color: 'purple',
      text: `${recentTransfers} إحالة هذا الأسبوع`
    })
  }

  // If no recent activities, show default message
  if (activities.length === 0) {
    activities.push({
      id: 4,
      icon: 'fas fa-info-circle',
      color: 'gray',
      text: 'لا توجد أنشطة حديثة'
    })
  }

  return activities.slice(0, 3) // Limit to 3 activities
})

// Methods
function getPeriodText(): string {
  const periods: Record<string, string> = {
    all: 'جميع الفترات',
    week: 'آخر أسبوع',
    month: 'آخر شهر',
    quarter: 'آخر 3 أشهر',
    year: 'آخر سنة'
  }
  return periods[selectedPeriod.value] || 'جميع الفترات'
}

function refreshData() {
  // In a real app, this would refresh the data from the store
  console.log('Refreshing chart data...')
}
</script>
