import { db } from '@/utils/database'
import type { User, UserRole, UserPermissions, DEFAULT_ROLES } from '@/types'
import { permissionsService } from './PermissionsService'
import CryptoJS from 'crypto-js'

export class UserService {
  private static instance: UserService

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  /**
   * الحصول على جميع المستخدمين
   */
  async getAllUsers(): Promise<User[]> {
    try {
      return await db.users.toArray()
    } catch (error) {
      console.error('Error fetching users:', error)
      throw new Error('فشل في تحميل قائمة المستخدمين')
    }
  }

  /**
   * الحصول على مستخدم بواسطة ID
   */
  async getUserById(id: string): Promise<User | undefined> {
    try {
      return await db.users.get(id)
    } catch (error) {
      console.error('Error fetching user:', error)
      throw new Error('فشل في تحميل بيانات المستخدم')
    }
  }

  /**
   * الحصول على مستخدم بواسطة اسم المستخدم
   */
  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      return await db.users.where('username').equals(username).first()
    } catch (error) {
      console.error('Error fetching user by username:', error)
      throw new Error('فشل في البحث عن المستخدم')
    }
  }

  /**
   * إضافة مستخدم جديد
   */
  async addUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>, password: string): Promise<User> {
    try {
      // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
      const existingUser = await this.getUserByUsername(userData.username)
      if (existingUser) {
        throw new Error('اسم المستخدم موجود بالفعل')
      }

      // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
      const existingEmail = await db.users.where('email').equals(userData.email).first()
      if (existingEmail) {
        throw new Error('البريد الإلكتروني موجود بالفعل')
      }

      // تشفير كلمة المرور
      const hashedPassword = this.hashPassword(password)

      // إنشاء المستخدم الجديد
      const newUser: Omit<User, 'id'> = {
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLogin: undefined
      }

      // إضافة المستخدم إلى قاعدة البيانات
      const userId = await db.users.add(newUser)
      
      // حفظ كلمة المرور المشفرة (في تطبيق حقيقي يجب استخدام قاعدة بيانات منفصلة)
      await this.saveUserPassword(userId.toString(), hashedPassword)

      // إنشاء صلاحيات افتراضية للمستخدم
      const userPermissions = permissionsService.createDefaultPermissions(userId.toString(), userData.role.id)
      await this.saveUserPermissionsPrivate(userId.toString(), userPermissions)

      // إرجاع المستخدم المُضاف
      const addedUser = await this.getUserById(userId.toString())
      if (!addedUser) {
        throw new Error('فشل في إضافة المستخدم')
      }

      console.log('✅ تم إضافة المستخدم بنجاح:', addedUser.name)
      return addedUser

    } catch (error) {
      console.error('Error adding user:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('فشل في إضافة المستخدم')
    }
  }

  /**
   * تحديث بيانات المستخدم
   */
  async updateUser(userId: string, userData: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<User> {
    try {
      // التحقق من وجود المستخدم
      const existingUser = await this.getUserById(userId)
      if (!existingUser) {
        throw new Error('المستخدم غير موجود')
      }

      // التحقق من عدم تضارب اسم المستخدم
      if (userData.username && userData.username !== existingUser.username) {
        const usernameExists = await this.getUserByUsername(userData.username)
        if (usernameExists && usernameExists.id !== userId) {
          throw new Error('اسم المستخدم موجود بالفعل')
        }
      }

      // التحقق من عدم تضارب البريد الإلكتروني
      if (userData.email && userData.email !== existingUser.email) {
        const emailExists = await db.users.where('email').equals(userData.email).first()
        if (emailExists && emailExists.id !== userId) {
          throw new Error('البريد الإلكتروني موجود بالفعل')
        }
      }

      // تحديث البيانات
      const updatedData = {
        ...userData,
        updatedAt: new Date()
      }

      await db.users.update(userId, updatedData)

      // إذا تم تغيير الدور، تحديث الصلاحيات
      if (userData.role && userData.role.id !== existingUser.role.id) {
        const userPermissions = permissionsService.createDefaultPermissions(userId, userData.role.id)
        await this.saveUserPermissionsPrivate(userId, userPermissions)
      }

      // إرجاع المستخدم المُحدث
      const updatedUser = await this.getUserById(userId)
      if (!updatedUser) {
        throw new Error('فشل في تحديث المستخدم')
      }

      console.log('✅ تم تحديث المستخدم بنجاح:', updatedUser.name)
      return updatedUser

    } catch (error) {
      console.error('Error updating user:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('فشل في تحديث المستخدم')
    }
  }

  /**
   * تفعيل/إلغاء تفعيل المستخدم
   */
  async toggleUserStatus(userId: string): Promise<User> {
    try {
      const user = await this.getUserById(userId)
      if (!user) {
        throw new Error('المستخدم غير موجود')
      }

      const newStatus = !user.isActive
      await db.users.update(userId, { 
        isActive: newStatus,
        updatedAt: new Date()
      })

      const updatedUser = await this.getUserById(userId)
      if (!updatedUser) {
        throw new Error('فشل في تحديث حالة المستخدم')
      }

      console.log(`✅ تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم:`, updatedUser.name)
      return updatedUser

    } catch (error) {
      console.error('Error toggling user status:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('فشل في تغيير حالة المستخدم')
    }
  }

  /**
   * إعادة تعيين كلمة المرور
   */
  async resetPassword(userId: string, newPassword: string): Promise<void> {
    try {
      const user = await this.getUserById(userId)
      if (!user) {
        throw new Error('المستخدم غير موجود')
      }

      // تشفير كلمة المرور الجديدة
      const hashedPassword = this.hashPassword(newPassword)
      
      // حفظ كلمة المرور الجديدة
      await this.saveUserPassword(userId, hashedPassword)

      // تحديث تاريخ التعديل
      await db.users.update(userId, { updatedAt: new Date() })

      console.log('✅ تم إعادة تعيين كلمة المرور للمستخدم:', user.name)

    } catch (error) {
      console.error('Error resetting password:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('فشل في إعادة تعيين كلمة المرور')
    }
  }

  /**
   * تحديث آخر دخول للمستخدم
   */
  async updateLastLogin(userId: string): Promise<void> {
    try {
      await db.users.update(userId, { 
        lastLogin: new Date(),
        updatedAt: new Date()
      })
    } catch (error) {
      console.error('Error updating last login:', error)
    }
  }

  /**
   * حذف المستخدم
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const user = await this.getUserById(userId)
      if (!user) {
        throw new Error('المستخدم غير موجود')
      }

      // منع حذف المدير الرئيسي
      if (user.role.id === 'admin' && user.username === 'admin') {
        throw new Error('لا يمكن حذف المدير الرئيسي')
      }

      // حذف المستخدم
      await db.users.delete(userId)
      
      // حذف كلمة المرور والصلاحيات
      await this.deleteUserPassword(userId)
      await this.deleteUserPermissions(userId)

      console.log('✅ تم حذف المستخدم:', user.name)

    } catch (error) {
      console.error('Error deleting user:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('فشل في حذف المستخدم')
    }
  }

  /**
   * الحصول على الأدوار المتاحة
   */
  getAvailableRoles(): UserRole[] {
    return [
      {
        id: 'admin',
        name: 'admin',
        displayName: 'مدير عام',
        description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
        permissions: []
      },
      {
        id: 'supervisor',
        name: 'supervisor',
        displayName: 'مشرف',
        description: 'صلاحيات إشرافية مع إمكانية إدارة البيانات والتقارير',
        permissions: []
      },
      {
        id: 'officer',
        name: 'officer',
        displayName: 'ضابط',
        description: 'صلاحيات تشغيلية لإدارة بيانات المتهمين وإنشاء التقارير',
        permissions: []
      },
      {
        id: 'investigator',
        name: 'investigator',
        displayName: 'محقق رئيسي',
        description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل',
        permissions: []
      },
      {
        id: 'viewer',
        name: 'viewer',
        displayName: 'مراقب',
        description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير',
        permissions: []
      }
    ]
  }

  /**
   * تشفير كلمة المرور
   */
  private hashPassword(password: string): string {
    return CryptoJS.SHA256(password + 'suspects_app_salt').toString()
  }

  /**
   * حفظ كلمة المرور المشفرة
   */
  private async saveUserPassword(userId: string, hashedPassword: string): Promise<void> {
    // في تطبيق حقيقي، يجب حفظ كلمات المرور في جدول منفصل ومشفر
    localStorage.setItem(`user_password_${userId}`, hashedPassword)
  }

  /**
   * حذف كلمة المرور
   */
  private async deleteUserPassword(userId: string): Promise<void> {
    localStorage.removeItem(`user_password_${userId}`)
  }

  /**
   * حفظ صلاحيات المستخدم
   */
  async saveUserPermissions(userId: string, permissions: UserPermissions): Promise<void> {
    localStorage.setItem(`user_permissions_${userId}`, JSON.stringify(permissions))
  }

  /**
   * حفظ صلاحيات المستخدم (دالة خاصة)
   */
  private async saveUserPermissionsPrivate(userId: string, permissions: UserPermissions): Promise<void> {
    localStorage.setItem(`user_permissions_${userId}`, JSON.stringify(permissions))
  }

  /**
   * حذف صلاحيات المستخدم
   */
  private async deleteUserPermissions(userId: string): Promise<void> {
    localStorage.removeItem(`user_permissions_${userId}`)
  }

  /**
   * الحصول على صلاحيات المستخدم
   */
  async getUserPermissions(userId: string): Promise<UserPermissions | null> {
    try {
      const permissionsData = localStorage.getItem(`user_permissions_${userId}`)
      return permissionsData ? JSON.parse(permissionsData) : null
    } catch (error) {
      console.error('Error loading user permissions:', error)
      return null
    }
  }
}

// تصدير instance واحد
export const userService = UserService.getInstance()
