import type { SuspectData, SuspectField } from '@/types'
import { generateBrandedHTMLDocument } from '@/utils/report-template'

export interface ExportData {
  suspects: SuspectData[]
  fields: SuspectField[]
}

// Format date for export
function formatDateForExport(date: any): string {
  if (!date) return ''
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

// Get field value for display
function getFieldValue(suspect: SuspectData, field: SuspectField): string {
  const value = suspect.fields[field.id!]
  if (!value) return ''
  
  if (field.inputType === 'date') {
    return formatDateForExport(value)
  }
  
  if (field.inputType === 'image' || field.inputType === 'file') {
    return value ? 'مرفق' : ''
  }
  
  return value.toString()
}

// Get suspect status
function getSuspectStatus(suspect: SuspectData): string {
  if (suspect.fields.isReleased) return 'مفرج عنه'
  if (suspect.fields.isTransferred) return 'محال للنيابة'
  return 'رهن الاعتقال'
}

// Get days detained
function getDaysDetained(suspect: SuspectData, fields: SuspectField[]): number {
  const arrestDateField = fields.find(field => field.order === 12)
  let arrestDate: Date
  
  if (arrestDateField && suspect.fields[arrestDateField.id!]) {
    arrestDate = new Date(suspect.fields[arrestDateField.id!])
  } else {
    arrestDate = new Date(suspect.createdAt)
  }
  
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - arrestDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// Export to Excel (CSV format for now)
export function exportToExcel(data: ExportData, filename?: string): void {
  const { suspects, fields } = data

  // Prepare suspects data
  const suspectsHeaders = fields.filter(field => field.isVisible).map(field => field.label)
  const suspectsRows = suspects.map(suspect =>
    fields.filter(field => field.isVisible).map(field => getFieldValue(suspect, field))
  )

  // Prepare status data
  const statusHeaders = ['الاسم', 'رقم الملف', 'الحالة', 'تاريخ الإفراج', 'تاريخ الإحالة', 'أيام الاعتقال', 'تاريخ الإنشاء']
  const statusRows = suspects.map(suspect => [
    suspect.fields.fullName || '',
    suspect.fields.fileNumber || '',
    getSuspectStatus(suspect),
    suspect.fields.isReleased ? formatDateForExport(suspect.fields.releaseDate) : '',
    suspect.fields.isTransferred ? formatDateForExport(suspect.fields.transferDate) : '',
    !suspect.fields.isReleased && !suspect.fields.isTransferred ? getDaysDetained(suspect, fields).toString() : '',
    formatDateForExport(suspect.createdAt)
  ])

  // Create CSV content
  let csvContent = '\uFEFF' // BOM for UTF-8

  // Add suspects data
  csvContent += 'بيانات المتهمين\n'
  csvContent += suspectsHeaders.join(',') + '\n'
  suspectsRows.forEach(row => {
    csvContent += row.map(cell => `"${cell}"`).join(',') + '\n'
  })

  csvContent += '\n\nالوضع الحالي\n'
  csvContent += statusHeaders.join(',') + '\n'
  statusRows.forEach(row => {
    csvContent += row.map(cell => `"${cell}"`).join(',') + '\n'
  })

  // Generate filename
  const exportFilename = filename || `بيانات_المتهمين_${new Date().toISOString().split('T')[0]}.csv`

  // Download file
  downloadFile(csvContent, exportFilename, 'text/csv')
}

// Export to PDF (HTML format for now)
export function exportToPDF(data: ExportData, filename?: string): void {
  const { suspects, fields } = data

  // Create table content for suspects data
  let suspectsTableContent = `
    <h2>بيانات المتهمين</h2>
    <table>
      <thead>
        <tr>
          ${fields.filter(field => field.isVisible).map(field => `<th>${field.label}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${suspects.map(suspect => `
          <tr>
            ${fields.filter(field => field.isVisible).map(field => `<td>${getFieldValue(suspect, field)}</td>`).join('')}
          </tr>
        `).join('')}
      </tbody>
    </table>
  `

  // Create table content for status data
  let statusTableContent = `
    <h2>الوضع الحالي</h2>
    <table class="status-table">
      <thead>
        <tr>
          <th>الاسم</th>
          <th>رقم الملف</th>
          <th>الحالة</th>
          <th>تاريخ الإفراج</th>
          <th>تاريخ الإحالة</th>
          <th>أيام الاعتقال</th>
          <th>تاريخ الإنشاء</th>
        </tr>
      </thead>
      <tbody>
        ${suspects.map(suspect => `
          <tr>
            <td>${suspect.fields.fullName || ''}</td>
            <td>${suspect.fields.fileNumber || ''}</td>
            <td>${getSuspectStatus(suspect)}</td>
            <td>${suspect.fields.isReleased ? formatDateForExport(suspect.fields.releaseDate) : ''}</td>
            <td>${suspect.fields.isTransferred ? formatDateForExport(suspect.fields.transferDate) : ''}</td>
            <td>${!suspect.fields.isReleased && !suspect.fields.isTransferred ? getDaysDetained(suspect, fields) : ''}</td>
            <td>${formatDateForExport(suspect.createdAt)}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `

  // Combine content
  const content = suspectsTableContent + statusTableContent

  // Generate complete HTML document with branding
  const htmlContent = generateBrandedHTMLDocument(
    'تقرير بيانات المتهمين',
    content,
    `إجمالي المتهمين: ${suspects.length}`
  )

  // Generate filename
  const exportFilename = filename || `بيانات_المتهمين_${new Date().toISOString().split('T')[0]}.html`

  // Download file
  downloadFile(htmlContent, exportFilename, 'text/html')
}

// Helper function to download file (will be replaced by the updated version below)

// Get suspect name from fields
function getSuspectNameForExport(suspect: SuspectData, fields: SuspectField[]): string {
  // Try different ways to get the name
  if (suspect.fields.fullName) {
    return suspect.fields.fullName
  }

  // Find the name field by order (should be order 2)
  const nameField = fields.find(field => field.order === 2)
  if (nameField && suspect.fields[nameField.id!]) {
    return suspect.fields[nameField.id!]
  }

  // Try common field names
  const possibleNameFields = ['fullName', 'name', 'الاسم_الرباعي', 'اسم_المتهم']
  for (const fieldName of possibleNameFields) {
    if (suspect.fields[fieldName]) {
      return suspect.fields[fieldName]
    }
  }

  // Find any field with "اسم" in the label
  const nameFieldByLabel = fields.find(field =>
    field.label.includes('اسم') || field.label.includes('الاسم')
  )
  if (nameFieldByLabel && suspect.fields[nameFieldByLabel.id!]) {
    return suspect.fields[nameFieldByLabel.id!]
  }

  return 'متهم_غير_محدد'
}

// Export single suspect data with attachments
export async function exportSuspectData(suspect: SuspectData, fields: SuspectField[]): Promise<void> {
  const suspectName = getSuspectNameForExport(suspect, fields)
  const fileNumber = suspect.fields.fileNumber || 'بدون_رقم'
  const folderName = `بطاقة_بيانات_المتهم_${suspectName}_${fileNumber}`

  // Create ZIP file using JSZip (we'll add this library)
  try {
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()

    // Add CSV file
    const csvContent = createSuspectCSV(suspect, fields)
    zip.file(`بطاقة_بيانات_المتهم_${suspectName}.csv`, csvContent)

    // Add HTML file
    const htmlContent = createSuspectHTML(suspect, fields)
    zip.file(`بطاقة_بيانات_المتهم_${suspectName}.html`, htmlContent)

    // Add attachments
    await addSuspectAttachments(zip, suspect, fields)

    // Generate and download ZIP
    const zipBlob = await zip.generateAsync({ type: 'blob' })
    downloadFile(zipBlob, `${folderName}.zip`, 'application/zip')

  } catch (error) {
    console.error('Error creating ZIP file:', error)
    // Fallback: export as CSV only
    const csvContent = createSuspectCSV(suspect, fields)
    downloadFile(csvContent, `بطاقة_بيانات_المتهم_${suspectName}.csv`, 'text/csv')
  }
}

// Create CSV content for single suspect
function createSuspectCSV(suspect: SuspectData, fields: SuspectField[]): string {
  const suspectName = getSuspectNameForExport(suspect, fields)
  let csvContent = '\uFEFF' // BOM for UTF-8
  csvContent += `بطاقة بيانات المتهم: ${suspectName}\n`
  csvContent += `رقم الملف: ${suspect.fields.fileNumber || 'غير محدد'}\n`
  csvContent += `تاريخ الإنشاء: ${formatDateForExport(suspect.createdAt)}\n\n`

  csvContent += 'الحقل,القيمة\n'

  fields.filter(field => field.isVisible).forEach(field => {
    const value = getFieldValue(suspect, field)
    csvContent += `"${field.label}","${value}"\n`
  })

  // Add status information
  csvContent += '\n\nمعلومات الحالة\n'
  csvContent += 'الحقل,القيمة\n'
  csvContent += `"الحالة","${getSuspectStatus(suspect)}"\n`

  if (suspect.fields.isReleased) {
    csvContent += `"تاريخ الإفراج","${formatDateForExport(suspect.fields.releaseDate)}"\n`
  }

  if (suspect.fields.isTransferred) {
    csvContent += `"تاريخ الإحالة","${formatDateForExport(suspect.fields.transferDate)}"\n`
  }

  if (!suspect.fields.isReleased && !suspect.fields.isTransferred) {
    csvContent += `"أيام الاعتقال","${getDaysDetained(suspect, fields)}"\n`
  }

  return csvContent
}

// Create HTML content for single suspect
function createSuspectHTML(suspect: SuspectData, fields: SuspectField[]): string {
  const suspectName = getSuspectNameForExport(suspect, fields)
  const fileNumber = suspect.fields.fileNumber || 'غير محدد'

  // Create content for suspect card
  const content = `
    <div class="suspect-card">
      <div class="suspect-info">
        <p><strong>الاسم:</strong> ${suspectName}</p>
        <p><strong>رقم الملف:</strong> ${fileNumber}</p>
        <p><strong>تاريخ الإنشاء:</strong> ${formatDateForExport(suspect.createdAt)}</p>
      </div>

      <div class="section">
        <h2>البيانات الشخصية</h2>
        <div class="field-grid">
          ${fields.filter(field => field.isVisible).map(field => {
            const value = getFieldValue(suspect, field)
            if (field.inputType === 'image' && value) {
              return `
                <div class="field">
                  <div class="field-label">${field.label}</div>
                  <div class="image-field">
                    <img src="${value}" alt="${field.label}" class="suspect-image" />
                  </div>
                </div>
              `
            }
            return `
              <div class="field">
                <div class="field-label">${field.label}</div>
                <div class="field-value">${value || 'غير محدد'}</div>
              </div>
            `
          }).join('')}
        </div>
      </div>

      <div class="section">
        <h2>معلومات الحالة</h2>
        <div class="status-section">
          <div class="field-grid">
            <div class="field">
              <div class="field-label">الحالة الحالية</div>
              <div class="field-value">${getSuspectStatus(suspect)}</div>
            </div>
            ${suspect.fields.isReleased ? `
              <div class="field">
                <div class="field-label">تاريخ الإفراج</div>
                <div class="field-value">${formatDateForExport(suspect.fields.releaseDate)}</div>
              </div>
            ` : ''}
            ${suspect.fields.isTransferred ? `
              <div class="field">
                <div class="field-label">تاريخ الإحالة</div>
                <div class="field-value">${formatDateForExport(suspect.fields.transferDate)}</div>
              </div>
            ` : ''}
            ${!suspect.fields.isReleased && !suspect.fields.isTransferred ? `
              <div class="field">
                <div class="field-label">أيام الاعتقال</div>
                <div class="field-value">${getDaysDetained(suspect, fields)} يوم</div>
              </div>
            ` : ''}
          </div>
        </div>
      </div>
    </div>

    <style>
      .suspect-card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        max-width: 800px;
        margin: 0 auto;
      }
      .suspect-info {
        text-align: center;
        border-bottom: 2px solid var(--primary-color, #007bff);
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      .suspect-info p {
        color: #666;
        margin: 10px 0;
        font-size: 16px;
      }
      .section {
        margin-bottom: 30px;
      }
      .section h2 {
        color: #333;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
        margin-bottom: 20px;
      }
      .field-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
      }
      .field {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-right: 4px solid var(--primary-color, #007bff);
      }
      .field-label {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
        font-size: 14px;
      }
      .field-value {
        color: #555;
        font-size: 16px;
        word-break: break-word;
      }
      .status-section {
        background: #e8f5e8;
        padding: 20px;
        border-radius: 8px;
        border-right: 4px solid #28a745;
      }
      .image-field {
        text-align: center;
        padding: 20px;
        background: #f0f0f0;
        border-radius: 8px;
      }
      .suspect-image {
        width: 40mm;
        height: 60mm;
        object-fit: cover;
        border: 2px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
    </style>
  `

  // Generate complete HTML document with branding
  return generateBrandedHTMLDocument(
    'بطاقة بيانات المتهم',
    content,
    `${suspectName} - ${fileNumber}`
  )
}

// Add suspect attachments to ZIP
async function addSuspectAttachments(zip: any, suspect: SuspectData, fields: SuspectField[]): Promise<void> {
  console.log('=== ATTACHMENT PROCESSING DEBUG ===')
  console.log('Processing attachments for suspect:', suspect.id)
  console.log('Suspect object keys:', Object.keys(suspect))
  console.log('Suspect attachments:', suspect.attachments)
  console.log('Suspect fields keys:', Object.keys(suspect.fields))
  console.log('All suspect fields:', suspect.fields)

  let attachmentCount = 0

  // Method 1: Check attachments array
  if (suspect.attachments && suspect.attachments.length > 0) {
    console.log(`Found ${suspect.attachments.length} attachments in attachments array`)
    for (const attachment of suspect.attachments) {
      try {
        const field = fields.find(f => f.id === attachment.fieldId)
        const fieldLabel = field ? field.label : attachment.fieldId

        console.log(`Processing attachment: ${attachment.originalName}`)
        console.log(`File path: ${attachment.filePath}`)
        console.log(`File type: ${attachment.fileType}`)

        if (attachment.filePath) {
          let blob: Blob

          if (attachment.filePath.startsWith('data:')) {
            // Data URL format
            const response = await fetch(attachment.filePath)
            blob = await response.blob()
          } else if (attachment.filePath.startsWith('blob:')) {
            // Blob URL format
            const response = await fetch(attachment.filePath)
            blob = await response.blob()
          } else {
            // Other formats - skip for now
            console.warn(`Unsupported file path format: ${attachment.filePath}`)
            continue
          }

          // Get extension from original name or mime type
          let extension = attachment.originalName.split('.').pop()?.toLowerCase()
          if (!extension && attachment.fileType) {
            extension = getFileExtension(attachment.fileType)
          }

          // Create unique filename to avoid conflicts
          const timestamp = Date.now()
          const filename = `${fieldLabel}_${timestamp}.${extension || 'bin'}`
          zip.file(filename, blob)
          attachmentCount++
          console.log(`✅ Added attachment: ${filename}`)
        }
      } catch (error) {
        console.error(`❌ Error processing attachment ${attachment.originalName}:`, error)
      }
    }
  } else {
    console.log('No attachments found in attachments array')
  }

  // Method 2: Check field data for data URLs and file references
  const attachmentFields = fields.filter(field =>
    field.inputType === 'image' || field.inputType === 'file'
  )

  console.log(`Found ${attachmentFields.length} attachment fields:`, attachmentFields.map(f => f.label))

  for (const field of attachmentFields) {
    const fieldId = field.id!
    const fileData = suspect.fields[fieldId]

    console.log(`\n--- Checking field: ${field.label} (${fieldId}) ---`)
    console.log(`Field data type: ${typeof fileData}`)
    console.log(`Field data value:`, fileData)

    if (fileData) {
      try {
        if (typeof fileData === 'string') {
          if (fileData.startsWith('data:')) {
            // Data URL format
            console.log('Found data URL, processing...')
            const mimeMatch = fileData.match(/data:([^;]+);/)
            const mimeType = mimeMatch ? mimeMatch[1] : 'application/octet-stream'
            const extension = getFileExtension(mimeType)

            const response = await fetch(fileData)
            const blob = await response.blob()

            // Create unique filename to avoid conflicts
            const timestamp = Date.now() + Math.random()
            const filename = `${field.label}_${Math.floor(timestamp)}.${extension}`
            zip.file(filename, blob)
            attachmentCount++
            console.log(`✅ Added field data URL: ${filename}`)
          } else if (fileData.startsWith('blob:')) {
            // Blob URL format
            console.log('Found blob URL, processing...')
            const response = await fetch(fileData)
            const blob = await response.blob()

            // Try to guess extension from blob type
            const extension = getFileExtension(blob.type) || 'bin'
            // Create unique filename to avoid conflicts
            const timestamp = Date.now() + Math.random()
            const filename = `${field.label}_${Math.floor(timestamp)}.${extension}`
            zip.file(filename, blob)
            attachmentCount++
            console.log(`✅ Added field blob URL: ${filename}`)
          } else if (fileData.length > 10) {
            // Might be a filename or other reference
            console.log(`Field contains string data (possibly filename): ${fileData}`)
          }
        } else if (fileData instanceof File) {
          // File object
          console.log('Found File object, processing...')
          const extension = fileData.name.split('.').pop()?.toLowerCase() || 'bin'
          // Create unique filename to avoid conflicts
          const timestamp = Date.now() + Math.random()
          const filename = `${field.label}_${Math.floor(timestamp)}.${extension}`
          zip.file(filename, fileData)
          attachmentCount++
          console.log(`✅ Added File object: ${filename}`)
        } else if (fileData instanceof Blob) {
          // Blob object
          console.log('Found Blob object, processing...')
          const extension = getFileExtension(fileData.type) || 'bin'
          // Create unique filename to avoid conflicts
          const timestamp = Date.now() + Math.random()
          const filename = `${field.label}_${Math.floor(timestamp)}.${extension}`
          zip.file(filename, fileData)
          attachmentCount++
          console.log(`✅ Added Blob object: ${filename}`)
        } else {
          console.log(`Unknown data type for field ${field.label}:`, typeof fileData, fileData)
        }
      } catch (error) {
        console.error(`❌ Error processing field ${field.label}:`, error)
      }
    } else {
      console.log(`Field ${field.label} is empty`)
    }
  }

  console.log(`\n=== ATTACHMENT PROCESSING COMPLETE ===`)
  console.log(`Total attachments added: ${attachmentCount}`)
  console.log('=====================================\n')
}

// Get file extension from MIME type
function getFileExtension(mimeType: string): string {
  const extensions: { [key: string]: string } = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'application/pdf': 'pdf',
    'text/plain': 'txt',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
  }
  return extensions[mimeType] || 'bin'
}

// Updated downloadFile function to handle blobs
function downloadFile(content: string | Blob, filename: string, mimeType?: string): void {
  let blob: Blob

  if (content instanceof Blob) {
    blob = content
  } else {
    blob = new Blob([content], { type: mimeType })
  }

  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
