<template>
  <div class="user-management">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-secondary-800 mb-2">إدارة المستخدمين</h2>
        <p class="text-secondary-600">إدارة حسابات المستخدمين وصلاحياتهم</p>
      </div>

      <button
        @click="openAddUserModal"
        class="neumorphic-button bg-primary-500 text-white px-4 py-2 hover:bg-primary-600"
        :disabled="!authStore.canManageUsers || isLoading"
      >
        <i v-if="isLoading" class="fas fa-spinner fa-spin ml-2"></i>
        <i v-else class="fas fa-plus ml-2"></i>
        إضافة مستخدم جديد
      </button>
    </div>

    <!-- Users List -->
    <div class="neumorphic-card bg-white">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">قائمة المستخدمين</h3>
        
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-secondary-200">
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">المستخدم</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الدور</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الحالة</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">آخر دخول</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="user in users"
                :key="user.id"
                class="border-b border-secondary-100 hover:bg-secondary-50"
              >
                <!-- User Info -->
                <td class="py-4 px-4">
                  <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                      <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                      <p class="font-medium text-secondary-800">{{ user.name }}</p>
                      <p class="text-sm text-secondary-600">{{ user.username }}</p>
                      <p v-if="user.email" class="text-xs text-secondary-500">{{ user.email }}</p>
                    </div>
                  </div>
                </td>

                <!-- Role -->
                <td class="py-4 px-4">
                  <span :class="getRoleClass(user.role.id)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ user.role.displayName }}
                  </span>
                </td>

                <!-- Status -->
                <td class="py-4 px-4">
                  <span :class="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                        class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ user.isActive ? 'نشط' : 'غير نشط' }}
                  </span>
                </td>

                <!-- Last Login -->
                <td class="py-4 px-4 text-sm text-secondary-600">
                  {{ user.lastLogin ? formatDate(user.lastLogin) : 'لم يسجل دخول' }}
                </td>

                <!-- Actions -->
                <td class="py-4 px-4">
                  <div class="flex items-center gap-2">
                    <button
                      @click="openEditUserModal(user)"
                      class="neumorphic-button p-2 text-blue-600 hover:bg-blue-50"
                      :disabled="!authStore.canManageUsers || isLoading"
                      title="تعديل"
                    >
                      <i class="fas fa-edit"></i>
                    </button>

                    <button
                      @click="handleToggleUserStatus(user)"
                      :class="user.isActive ? 'text-red-600 hover:bg-red-50' : 'text-green-600 hover:bg-green-50'"
                      class="neumorphic-button p-2"
                      :disabled="!authStore.canManageUsers || user.id === authStore.currentUser?.id || isLoading"
                      :title="user.isActive ? 'إلغاء التفعيل' : 'تفعيل'"
                    >
                      <i v-if="isLoading && loadingUserId === user.id" class="fas fa-spinner fa-spin"></i>
                      <i v-else :class="user.isActive ? 'fas fa-ban' : 'fas fa-check'"></i>
                    </button>

                    <button
                      @click="handleResetPassword(user)"
                      class="neumorphic-button p-2 text-orange-600 hover:bg-orange-50"
                      :disabled="!authStore.canManageUsers || isLoading"
                      title="إعادة تعيين كلمة المرور"
                    >
                      <i v-if="isLoading && loadingUserId === user.id" class="fas fa-spinner fa-spin"></i>
                      <i v-else class="fas fa-key"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Permissions Overview -->
    <div class="neumorphic-card bg-white mt-6">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">نظرة عامة على الصلاحيات</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Admin Role -->
          <div class="neumorphic-card bg-gradient-to-br from-red-50 to-red-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <i class="fas fa-crown text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-red-800">مدير النظام</h4>
            </div>
            <ul class="text-sm text-red-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>جميع الصلاحيات</li>
              <li><i class="fas fa-check ml-2"></i>إدارة المستخدمين</li>
              <li><i class="fas fa-check ml-2"></i>إدارة النظام</li>
              <li><i class="fas fa-check ml-2"></i>الوصول الكامل</li>
            </ul>
          </div>

          <!-- User Role -->
          <div class="neumorphic-card bg-gradient-to-br from-blue-50 to-blue-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <i class="fas fa-user-tie text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-blue-800">مستخدم</h4>
            </div>
            <ul class="text-sm text-blue-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>قراءة وكتابة البيانات</li>
              <li><i class="fas fa-check ml-2"></i>إنشاء التقارير</li>
              <li><i class="fas fa-check ml-2"></i>البحث والتصدير</li>
              <li><i class="fas fa-times ml-2"></i>إدارة المستخدمين</li>
            </ul>
          </div>

          <!-- Viewer Role -->
          <div class="neumorphic-card bg-gradient-to-br from-green-50 to-green-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-eye text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-green-800">مراقب</h4>
            </div>
            <ul class="text-sm text-green-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>قراءة البيانات فقط</li>
              <li><i class="fas fa-check ml-2"></i>عرض التقارير</li>
              <li><i class="fas fa-times ml-2"></i>تعديل البيانات</li>
              <li><i class="fas fa-times ml-2"></i>إدارة المستخدمين</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Current User Info -->
    <div class="neumorphic-card bg-gradient-to-r from-primary-50 to-secondary-50 mt-6">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">معلومات المستخدم الحالي</h3>
        
        <div class="flex items-center gap-4">
          <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
            <i class="fas fa-user text-white text-xl"></i>
          </div>
          
          <div class="flex-1">
            <h4 class="text-xl font-semibold text-secondary-800">{{ currentUserData?.name || authStore.currentUser?.fullName }}</h4>
            <p class="text-secondary-600">{{ currentUserData?.username || authStore.currentUser?.username }}</p>
            <p class="text-sm text-secondary-500">{{ currentUserData?.email || authStore.currentUser?.email }}</p>

            <div class="flex items-center gap-4 mt-2">
              <span :class="getRoleClass(currentUserData?.role?.id || authStore.currentUser?.role)" class="px-3 py-1 rounded-full text-sm font-medium">
                {{ currentUserData?.role?.displayName || getRoleLabel(authStore.currentUser?.role) }}
              </span>

              <span class="text-sm text-secondary-600">
                آخر دخول: {{ (currentUserData?.lastLogin || authStore.currentUser?.lastLogin) ? formatDate(currentUserData?.lastLogin || authStore.currentUser?.lastLogin) : 'الآن' }}
              </span>
            </div>
          </div>
          
          <div class="text-right">
            <button
              @click="handleChangeCurrentUserPassword"
              class="neumorphic-button bg-primary-500 text-white px-4 py-2 hover:bg-primary-600"
              :disabled="isLoading"
            >
              <i v-if="isLoading" class="fas fa-spinner fa-spin ml-2"></i>
              <i v-else class="fas fa-key ml-2"></i>
              تغيير كلمة المرور
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Simple User Modal -->
    <SimpleUserModal
      v-if="showUserModal"
      :user="editingUser"
      :is-editing="!!editingUser"
      @save="handleUserSaved"
      @cancel="closeUserModal"
    />

    <!-- Change Password Modal -->
    <div v-if="showChangePasswordModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="neumorphic-card bg-white max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-secondary-800">تغيير كلمة المرور</h3>
            <button @click="showChangePasswordModal = false" class="text-secondary-400 hover:text-secondary-600">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <form @submit.prevent="handlePasswordChange" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">كلمة المرور الحالية</label>
              <input
                v-model="passwordForm.currentPassword"
                type="password"
                required
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="أدخل كلمة المرور الحالية"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">كلمة المرور الجديدة</label>
              <input
                v-model="passwordForm.newPassword"
                type="password"
                required
                minlength="8"
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="أدخل كلمة المرور الجديدة (8 أحرف على الأقل)"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">تأكيد كلمة المرور</label>
              <input
                v-model="passwordForm.confirmPassword"
                type="password"
                required
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="أعد إدخال كلمة المرور الجديدة"
              />
            </div>

            <div class="flex gap-3 pt-4">
              <button
                type="button"
                @click="showChangePasswordModal = false"
                class="flex-1 neumorphic-button bg-secondary-500 text-white py-2 hover:bg-secondary-600"
              >
                إلغاء
              </button>
              <button
                type="submit"
                class="flex-1 neumorphic-button bg-primary-500 text-white py-2 hover:bg-primary-600"
                :disabled="isLoading || !isPasswordFormValid"
              >
                <i v-if="isLoading" class="fas fa-spinner fa-spin ml-2"></i>
                <i v-else class="fas fa-save ml-2"></i>
                حفظ
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { User } from '@/types'
import { userService } from '@/services/UserService'
import SimpleUserModal from './SimpleUserModal.vue'

const authStore = useAuthStore()

// Reactive data
const users = ref<User[]>([])
const currentUserData = ref<User | null>(null)
const isLoading = ref(false)
const loadingUserId = ref<string | null>(null)
const showUserModal = ref(false)
const showChangePasswordModal = ref(false)
const editingUser = ref<User | null>(null)

// Password form
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// Computed
const isPasswordFormValid = computed(() => {
  return passwordForm.value.currentPassword.length > 0 &&
         passwordForm.value.newPassword.length >= 8 &&
         passwordForm.value.newPassword === passwordForm.value.confirmPassword
})

// Methods
async function loadUsers() {
  try {
    isLoading.value = true

    // محاولة تحميل المستخدمين من قاعدة البيانات
    let dbUsers = await userService.getAllUsers()

    // إذا لم توجد مستخدمين في قاعدة البيانات، إنشاء المستخدمين الافتراضيين
    if (dbUsers.length === 0) {
      console.log('🔄 إنشاء المستخدمين الافتراضيين...')
      await createDefaultUsers()
      dbUsers = await userService.getAllUsers()
    }

    // تحويل بيانات المستخدمين للتنسيق المطلوب
    users.value = dbUsers.map(user => ({
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin,
      isActive: user.isActive
    }))

    // تحميل بيانات المستخدم الحالي
    if (authStore.currentUser?.id) {
      currentUserData.value = users.value.find(u => u.id === authStore.currentUser?.id) || null
    }

    console.log('✅ تم تحميل المستخدمين:', users.value.length)
  } catch (error) {
    console.error('Error loading users:', error)
    alert('فشل في تحميل قائمة المستخدمين')
  } finally {
    isLoading.value = false
  }
}

async function createDefaultUsers() {
  try {
    const defaultUsers = [
      {
        username: 'admin',
        name: 'المدير العام',
        email: '<EMAIL>',
        password: 'admin123',
        role: {
          id: 'admin',
          name: 'admin',
          displayName: 'مدير عام',
          description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
          permissions: []
        },
        isActive: true
      },
      {
        username: 'investigator',
        name: 'محقق رئيسي',
        email: '<EMAIL>',
        password: 'inv123',
        role: {
          id: 'investigator',
          name: 'investigator',
          displayName: 'محقق رئيسي',
          description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل',
          permissions: []
        },
        isActive: true
      },
      {
        username: 'viewer',
        name: 'مراقب',
        email: '<EMAIL>',
        password: 'view123',
        role: {
          id: 'viewer',
          name: 'viewer',
          displayName: 'مراقب',
          description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير',
          permissions: []
        },
        isActive: true
      }
    ]

    for (const userData of defaultUsers) {
      await userService.createUser(userData)
    }

    console.log('✅ تم إنشاء المستخدمين الافتراضيين')
  } catch (error) {
    console.error('Error creating default users:', error)
  }
}

async function syncDefaultUsers() {
  try {
    // إضافة المستخدمين الافتراضيين من AuthStore إلى قاعدة البيانات
    const availableRoles = userService.getAvailableRoles()

    for (const defaultUser of authStore.defaultUsers) {
      try {
        const userRole = availableRoles.find(role => role.id === defaultUser.role)
        if (userRole) {
          await userService.addUser({
            username: defaultUser.username,
            name: defaultUser.fullName,
            email: defaultUser.email || `${defaultUser.username}@system.local`,
            role: userRole,
            permissions: [],
            isActive: defaultUser.isActive
          }, 'admin123') // كلمة مرور افتراضية
        }
      } catch (userError) {
        console.log(`User ${defaultUser.username} already exists or error occurred:`, userError)
      }
    }
  } catch (error) {
    console.error('Error syncing default users:', error)
  }
}

function openAddUserModal() {
  editingUser.value = null
  showUserModal.value = true
}

function openEditUserModal(user: User) {
  editingUser.value = user
  showUserModal.value = true
}

function closeUserModal() {
  showUserModal.value = false
  editingUser.value = null
}

async function handleUserSaved(user: any) {
  try {
    if (editingUser.value) {
      // تحديث المستخدم الموجود
      const updatedUser = {
        ...editingUser.value,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        updatedAt: new Date()
      }

      await userService.updateUser(updatedUser.id, updatedUser)
      console.log('✅ تم تحديث المستخدم في قاعدة البيانات')
    } else {
      // إضافة مستخدم جديد
      const newUser = {
        username: user.username,
        name: user.name,
        email: user.email,
        password: user.password,
        role: user.role,
        isActive: user.isActive
      }

      await userService.createUser(newUser)
      console.log('✅ تم إضافة المستخدم الجديد في قاعدة البيانات')
    }

    // إعادة تحميل قائمة المستخدمين من قاعدة البيانات
    await loadUsers()

    // إغلاق المودال
    closeUserModal()

    // إظهار رسالة نجاح
    alert(`تم ${editingUser.value ? 'تحديث' : 'إضافة'} المستخدم بنجاح`)

  } catch (error) {
    console.error('Error saving user:', error)
    alert(`فشل في ${editingUser.value ? 'تحديث' : 'إضافة'} المستخدم: ${error.message}`)
  }
}

async function handleToggleUserStatus(user: User) {
  if (user.id === authStore.currentUser?.id) {
    alert('لا يمكنك تغيير حالة حسابك الخاص')
    return
  }

  const action = user.isActive ? 'إلغاء تفعيل' : 'تفعيل'
  const confirmed = confirm(`هل أنت متأكد من ${action} المستخدم "${user.name}"؟`)

  if (!confirmed) return

  try {
    loadingUserId.value = user.id
    await userService.toggleUserStatus(user.id)
    await loadUsers()
    alert(`تم ${action} المستخدم بنجاح`)
  } catch (error) {
    console.error('Error toggling user status:', error)
    alert(error instanceof Error ? error.message : `فشل في ${action} المستخدم`)
  } finally {
    loadingUserId.value = null
  }
}

async function handleResetPassword(user: User) {
  const confirmed = confirm(`هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم "${user.name}"؟`)

  if (!confirmed) return

  const newPassword = prompt('أدخل كلمة المرور الجديدة (8 أحرف على الأقل):')

  if (!newPassword || newPassword.length < 8) {
    alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    return
  }

  try {
    loadingUserId.value = user.id
    await userService.resetPassword(user.id, newPassword)
    alert('تم إعادة تعيين كلمة المرور بنجاح')
  } catch (error) {
    console.error('Error resetting password:', error)
    alert(error instanceof Error ? error.message : 'فشل في إعادة تعيين كلمة المرور')
  } finally {
    loadingUserId.value = null
  }
}

function handleChangeCurrentUserPassword() {
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  showChangePasswordModal.value = true
}

async function handlePasswordChange() {
  if (!isPasswordFormValid.value) return

  try {
    isLoading.value = true

    // في تطبيق حقيقي، يجب التحقق من كلمة المرور الحالية
    // هنا سنقوم بإعادة تعيين كلمة المرور مباشرة
    if (authStore.currentUser?.id) {
      await userService.resetPassword(authStore.currentUser.id, passwordForm.value.newPassword)
      showChangePasswordModal.value = false
      alert('تم تغيير كلمة المرور بنجاح')
    }
  } catch (error) {
    console.error('Error changing password:', error)
    alert(error instanceof Error ? error.message : 'فشل في تغيير كلمة المرور')
  } finally {
    isLoading.value = false
  }
}
function getRoleLabel(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'مدير عام'
    case 'supervisor': return 'مشرف'
    case 'officer': return 'ضابط'
    case 'investigator': return 'محقق رئيسي'
    case 'user': return 'مستخدم'
    case 'viewer': return 'مراقب'
    default: return 'غير محدد'
  }
}

function getRoleClass(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'bg-red-100 text-red-800'
    case 'supervisor': return 'bg-purple-100 text-purple-800'
    case 'officer': return 'bg-blue-100 text-blue-800'
    case 'investigator': return 'bg-indigo-100 text-indigo-800'
    case 'user': return 'bg-cyan-100 text-cyan-800'
    case 'viewer': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function getRoleDescription(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات'
    case 'supervisor': return 'صلاحيات إشرافية مع إمكانية إدارة البيانات والتقارير'
    case 'officer': return 'صلاحيات تشغيلية لإدارة بيانات المتهمين وإنشاء التقارير'
    case 'investigator': return 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل'
    case 'user': return 'صلاحيات مستخدم عادي'
    case 'viewer': return 'صلاحيات القراءة فقط لعرض البيانات والتقارير'
    default: return 'غير محدد'
  }
}

function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Lifecycle
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  /* Additional styles if needed */
}
</style>
