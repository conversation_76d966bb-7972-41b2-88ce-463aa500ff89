<template>
  <div class="user-management">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-secondary-800 mb-2">إدارة المستخدمين</h2>
        <p class="text-secondary-600">إدارة حسابات المستخدمين وصلاحياتهم</p>
      </div>
      
      <button
        @click="showAddUserModal = true"
        class="neumorphic-button bg-primary-500 text-white px-4 py-2 hover:bg-primary-600"
        :disabled="!authStore.canManageUsers"
      >
        <i class="fas fa-plus ml-2"></i>
        إضافة مستخدم جديد
      </button>
    </div>

    <!-- Users List -->
    <div class="neumorphic-card bg-white">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">قائمة المستخدمين</h3>
        
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-secondary-200">
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">المستخدم</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الدور</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الحالة</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">آخر دخول</th>
                <th class="text-right py-3 px-4 font-semibold text-secondary-700">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="user in authStore.defaultUsers"
                :key="user.id"
                class="border-b border-secondary-100 hover:bg-secondary-50"
              >
                <!-- User Info -->
                <td class="py-4 px-4">
                  <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                      <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                      <p class="font-medium text-secondary-800">{{ user.fullName }}</p>
                      <p class="text-sm text-secondary-600">{{ user.username }}</p>
                      <p v-if="user.email" class="text-xs text-secondary-500">{{ user.email }}</p>
                    </div>
                  </div>
                </td>

                <!-- Role -->
                <td class="py-4 px-4">
                  <span :class="getRoleClass(user.role)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ getRoleLabel(user.role) }}
                  </span>
                </td>

                <!-- Status -->
                <td class="py-4 px-4">
                  <span :class="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                        class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ user.isActive ? 'نشط' : 'غير نشط' }}
                  </span>
                </td>

                <!-- Last Login -->
                <td class="py-4 px-4 text-sm text-secondary-600">
                  {{ user.lastLogin ? formatDate(user.lastLogin) : 'لم يسجل دخول' }}
                </td>

                <!-- Actions -->
                <td class="py-4 px-4">
                  <div class="flex items-center gap-2">
                    <button
                      @click="editUser(user)"
                      class="neumorphic-button p-2 text-blue-600 hover:bg-blue-50"
                      :disabled="!authStore.canManageUsers"
                      title="تعديل"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    
                    <button
                      @click="toggleUserStatus(user)"
                      :class="user.isActive ? 'text-red-600 hover:bg-red-50' : 'text-green-600 hover:bg-green-50'"
                      class="neumorphic-button p-2"
                      :disabled="!authStore.canManageUsers || user.id === authStore.currentUser?.id"
                      :title="user.isActive ? 'إلغاء التفعيل' : 'تفعيل'"
                    >
                      <i :class="user.isActive ? 'fas fa-ban' : 'fas fa-check'"></i>
                    </button>
                    
                    <button
                      @click="resetPassword(user)"
                      class="neumorphic-button p-2 text-orange-600 hover:bg-orange-50"
                      :disabled="!authStore.canManageUsers"
                      title="إعادة تعيين كلمة المرور"
                    >
                      <i class="fas fa-key"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Permissions Overview -->
    <div class="neumorphic-card bg-white mt-6">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">نظرة عامة على الصلاحيات</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Admin Role -->
          <div class="neumorphic-card bg-gradient-to-br from-red-50 to-red-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <i class="fas fa-crown text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-red-800">مدير النظام</h4>
            </div>
            <ul class="text-sm text-red-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>جميع الصلاحيات</li>
              <li><i class="fas fa-check ml-2"></i>إدارة المستخدمين</li>
              <li><i class="fas fa-check ml-2"></i>إدارة النظام</li>
              <li><i class="fas fa-check ml-2"></i>الوصول الكامل</li>
            </ul>
          </div>

          <!-- User Role -->
          <div class="neumorphic-card bg-gradient-to-br from-blue-50 to-blue-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <i class="fas fa-user-tie text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-blue-800">مستخدم</h4>
            </div>
            <ul class="text-sm text-blue-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>قراءة وكتابة البيانات</li>
              <li><i class="fas fa-check ml-2"></i>إنشاء التقارير</li>
              <li><i class="fas fa-check ml-2"></i>البحث والتصدير</li>
              <li><i class="fas fa-times ml-2"></i>إدارة المستخدمين</li>
            </ul>
          </div>

          <!-- Viewer Role -->
          <div class="neumorphic-card bg-gradient-to-br from-green-50 to-green-100 p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-eye text-white text-sm"></i>
              </div>
              <h4 class="font-semibold text-green-800">مراقب</h4>
            </div>
            <ul class="text-sm text-green-700 space-y-1">
              <li><i class="fas fa-check ml-2"></i>قراءة البيانات فقط</li>
              <li><i class="fas fa-check ml-2"></i>عرض التقارير</li>
              <li><i class="fas fa-times ml-2"></i>تعديل البيانات</li>
              <li><i class="fas fa-times ml-2"></i>إدارة المستخدمين</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Current User Info -->
    <div class="neumorphic-card bg-gradient-to-r from-primary-50 to-secondary-50 mt-6">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">معلومات المستخدم الحالي</h3>
        
        <div class="flex items-center gap-4">
          <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
            <i class="fas fa-user text-white text-xl"></i>
          </div>
          
          <div class="flex-1">
            <h4 class="text-xl font-semibold text-secondary-800">{{ authStore.currentUser?.fullName }}</h4>
            <p class="text-secondary-600">{{ authStore.currentUser?.username }}</p>
            <p class="text-sm text-secondary-500">{{ authStore.currentUser?.email }}</p>
            
            <div class="flex items-center gap-4 mt-2">
              <span :class="getRoleClass(authStore.currentUser?.role)" class="px-3 py-1 rounded-full text-sm font-medium">
                {{ getRoleLabel(authStore.currentUser?.role) }}
              </span>
              
              <span class="text-sm text-secondary-600">
                آخر دخول: {{ authStore.currentUser?.lastLogin ? formatDate(authStore.currentUser.lastLogin) : 'الآن' }}
              </span>
            </div>
          </div>
          
          <div class="text-right">
            <button
              @click="showChangePasswordModal = true"
              class="neumorphic-button bg-primary-500 text-white px-4 py-2 hover:bg-primary-600"
            >
              <i class="fas fa-key ml-2"></i>
              تغيير كلمة المرور
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add User Modal -->
    <div v-if="showAddUserModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="neumorphic-card bg-white max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-secondary-800">إضافة مستخدم جديد</h3>
            <button @click="showAddUserModal = false" class="text-secondary-400 hover:text-secondary-600">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <p class="text-center text-secondary-600 py-8">
            <i class="fas fa-info-circle text-blue-500 text-2xl mb-2"></i><br>
            هذه الميزة ستكون متاحة في الإصدار القادم
          </p>
          
          <button
            @click="showAddUserModal = false"
            class="w-full neumorphic-button bg-secondary-500 text-white py-2 hover:bg-secondary-600"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div v-if="showChangePasswordModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="neumorphic-card bg-white max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-secondary-800">تغيير كلمة المرور</h3>
            <button @click="showChangePasswordModal = false" class="text-secondary-400 hover:text-secondary-600">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <p class="text-center text-secondary-600 py-8">
            <i class="fas fa-info-circle text-blue-500 text-2xl mb-2"></i><br>
            هذه الميزة ستكون متاحة في الإصدار القادم
          </p>
          
          <button
            @click="showChangePasswordModal = false"
            class="w-full neumorphic-button bg-secondary-500 text-white py-2 hover:bg-secondary-600"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { User } from '@/stores/auth'

const authStore = useAuthStore()

// Reactive data
const showAddUserModal = ref(false)
const showChangePasswordModal = ref(false)

// Methods
function getRoleLabel(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'مدير النظام'
    case 'user': return 'مستخدم'
    case 'viewer': return 'مراقب'
    default: return 'غير محدد'
  }
}

function getRoleClass(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'bg-red-100 text-red-800'
    case 'user': return 'bg-blue-100 text-blue-800'
    case 'viewer': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function editUser(user: User) {
  console.log('تعديل المستخدم:', user.fullName)
  // TODO: Implement edit user functionality
}

function toggleUserStatus(user: User) {
  console.log('تغيير حالة المستخدم:', user.fullName)
  // TODO: Implement toggle user status functionality
}

function resetPassword(user: User) {
  console.log('إعادة تعيين كلمة مرور المستخدم:', user.fullName)
  // TODO: Implement reset password functionality
}
</script>

<style scoped>
.user-management {
  /* Additional styles if needed */
}
</style>
