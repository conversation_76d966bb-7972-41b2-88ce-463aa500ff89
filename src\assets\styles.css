@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Variables for Theme Customization */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #64748b;
  --background-color: #f8fafc;
  --font-family: 'Cairo';
  --font-size: 14px;
  --border-radius: 20px;
}

/* Arabic RTL Support */
* {
  direction: rtl;
  text-align: right;
}

html {
  font-family: var(--font-family), 'Tajawal', sans-serif;
  font-size: var(--font-size);
  direction: rtl;
  background-color: var(--background-color);
}

body {
  direction: rtl;
  text-align: right;
  font-family: var(--font-family), '<PERSON><PERSON>wal', sans-serif;
  font-size: var(--font-size);
  background-color: var(--background-color);
}

/* Custom Neumorphic Components */
@layer components {
  .neumorphic-card {
    @apply bg-secondary-50 shadow-neumorphic p-6 transition-all duration-300;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size);
  }

  .neumorphic-card:hover {
    @apply shadow-neumorphic-hover transform -translate-y-1;
  }

  .neumorphic-button {
    @apply bg-secondary-50 shadow-neumorphic px-6 py-3 transition-all duration-300 cursor-pointer;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size);
    color: var(--primary-color);
  }

  .neumorphic-button:hover {
    @apply shadow-neumorphic-hover transform -translate-y-1;
    background-color: var(--primary-color);
    color: white;
  }

  .neumorphic-button:active {
    @apply shadow-neumorphic-inset transform translate-y-0;
  }

  .neumorphic-input {
    @apply bg-secondary-50 shadow-neumorphic-inset px-4 py-3 border-none outline-none transition-all duration-300;
    border-radius: calc(var(--border-radius) * 0.5);
    font-family: var(--font-family);
    font-size: var(--font-size);
  }

  .neumorphic-input:focus {
    ring: 2px solid var(--primary-color);
    ring-opacity: 0.5;
  }
  
  .neumorphic-select {
    @apply bg-secondary-50 shadow-neumorphic-inset px-4 py-3 border-none outline-none transition-all duration-300 cursor-pointer;
    border-radius: calc(var(--border-radius) * 0.5);
    font-family: var(--font-family);
    font-size: var(--font-size);
  }
  

  
  .neumorphic-sidebar {
    @apply bg-secondary-50 shadow-neumorphic-lg rounded-neumorphic p-4;
  }
  
  .neumorphic-header {
    @apply bg-secondary-50 shadow-neumorphic rounded-neumorphic p-4 mb-6;
  }
  
  .neumorphic-icon {
    @apply w-8 h-8 p-2 bg-secondary-100 rounded-full shadow-neumorphic-sm transition-all duration-300;
  }

  .neumorphic-icon:hover {
    @apply shadow-neumorphic-hover transform -translate-y-1;
  }

  .neumorphic-icon-sm {
    @apply w-6 h-6 p-1 bg-secondary-100 rounded-full shadow-neumorphic-sm transition-all duration-300 flex items-center justify-center;
  }

  .neumorphic-radio {
    @apply w-4 h-4 text-primary-600 bg-secondary-50 border-secondary-300 focus:ring-primary-500 focus:ring-2;
  }

  .neumorphic-checkbox {
    @apply w-4 h-4 text-primary-600 bg-secondary-50 border-secondary-300 focus:ring-primary-500 focus:ring-2 rounded;
  }

  .neumorphic-tab {
    @apply px-4 py-2 rounded-neumorphic-sm transition-all duration-300 text-secondary-700 hover:text-primary-600;
    background: linear-gradient(145deg, #f8fafc, #e2e8f0);
    box-shadow:
      4px 4px 8px #d1d9e0,
      -4px -4px 8px #ffffff;
  }

  .neumorphic-tab.active {
    @apply text-primary-600;
    background: linear-gradient(145deg, #e2e8f0, #f8fafc);
    box-shadow:
      inset 4px 4px 8px #d1d9e0,
      inset -4px -4px 8px #ffffff;
  }

  .spinner {
    @apply w-8 h-8 border-4 border-secondary-300 rounded-full animate-spin;
    border-top-color: var(--primary-color);
  }

  /* Theme-aware classes */
  .text-primary {
    color: var(--primary-color) !important;
  }

  .text-secondary {
    color: var(--secondary-color) !important;
  }

  .bg-primary {
    background-color: var(--primary-color) !important;
  }

  .bg-secondary {
    background-color: var(--secondary-color) !important;
  }

  .border-primary {
    border-color: var(--primary-color) !important;
  }

  .border-secondary {
    border-color: var(--secondary-color) !important;
  }

  /* Button variants */
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size);
    padding: 0.75rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  .btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size);
    padding: 0.75rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  /* Dark Mode Support */
  .dark-mode {
    --primary-color: #60a5fa;
    --secondary-color: #94a3b8;
    --background-color: #1e293b;
    --text-color: #f1f5f9;
    --card-background: #334155;
    --border-color: #475569;
  }

  .dark-mode body {
    background-color: var(--background-color);
    color: var(--text-color);
  }

  .dark-mode .neumorphic-card {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .dark-mode .neumorphic-button {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .dark-mode .neumorphic-input {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .dark-mode .neumorphic-select {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(145deg, #cbd5e1, #94a3b8);
  border-radius: 10px;
  box-shadow: inset 2px 2px 4px #d1d9e6, inset -2px -2px 4px #ffffff;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(145deg, #94a3b8, #64748b);
}

/* Animation Classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* Form Validation */
.form-error {
  @apply text-danger-500 text-sm mt-1;
}

.form-success {
  @apply text-success-500 text-sm mt-1;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .neumorphic-card,
  .neumorphic-button,
  .neumorphic-input {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .neumorphic-card {
    @apply p-4;
  }
  
  .neumorphic-button {
    @apply px-4 py-2 text-sm;
  }
  
  .neumorphic-input {
    @apply px-3 py-2 text-sm;
  }
}
