import Dexie, { Table } from 'dexie'
import type {
  User,
  SuspectData,
  SuspectField,
  SuspectAttachment,
  AppSettings,
  ReportTemplate,
  NotificationItem,
  BackupRecord
} from '@/types'

export class SuspectsDatabase extends Dexie {
  // Tables
  users!: Table<User>
  suspects!: Table<SuspectData>
  fields!: Table<SuspectField>
  attachments!: Table<SuspectAttachment>
  settings!: Table<AppSettings>
  reports!: Table<ReportTemplate>
  notifications!: Table<NotificationItem>
  auditLog!: Table<AuditLogEntry>
  backupRecords!: Table<BackupRecord>

  constructor() {
    super('SuspectsDatabase')
    
    // Version 1 - Original schema
    this.version(1).stores({
      users: '++id, email, role, isActive, createdAt',
      suspects: '++id, createdAt, updatedAt, createdBy, updatedBy',
      fields: '++id, label, inputType, isRequired, isVisible, order',
      attachments: '++id, fieldId, fileName, fileType, uploadedAt',
      settings: '++id, type',
      reports: '++id, name, type, createdAt',
      notifications: '++id, type, createdAt',
      auditLog: '++id, userId, action, resource, timestamp'
    })

    // Version 2 - Added backup records table
    this.version(2).stores({
      users: '++id, email, role, isActive, createdAt',
      suspects: '++id, createdAt, updatedAt, createdBy, updatedBy',
      fields: '++id, label, inputType, isRequired, isVisible, order',
      attachments: '++id, fieldId, fileName, fileType, uploadedAt',
      settings: '++id, type',
      reports: '++id, name, type, createdAt',
      notifications: '++id, type, createdAt',
      auditLog: '++id, userId, action, resource, timestamp',
      backupRecords: '++id, type, date, status'
    })

    // Hooks
    this.suspects.hook('creating', (primKey, obj, trans) => {
      obj.createdAt = new Date()
      obj.updatedAt = new Date()
    })

    this.suspects.hook('updating', (modifications, primKey, obj, trans) => {
      modifications.updatedAt = new Date()
    })

    this.users.hook('creating', (primKey, obj, trans) => {
      obj.createdAt = new Date()
      obj.updatedAt = new Date()
    })
  }

  // Initialize default data
  async initializeDefaults() {
    const fieldsCount = await this.fields.count()

    if (fieldsCount === 0) {
      await this.addDefaultFields()
    }

    const usersCount = await this.users.count()
    if (usersCount === 0) {
      await this.addDefaultUser()
    }

    const settingsCount = await this.settings.count()
    if (settingsCount === 0) {
      await this.addDefaultSettings()
    }

    // Update ID field validation (remove pattern restriction)
    await this.updateIdFieldValidation()
  }

  // Clear all data and reinitialize
  async clearAndReinitialize() {
    await this.transaction('rw', this.users, this.suspects, this.fields, this.attachments, this.settings, this.reports, this.auditLog, async () => {
      await this.users.clear()
      await this.suspects.clear()
      await this.fields.clear()
      await this.attachments.clear()
      await this.settings.clear()
      await this.reports.clear()
      await this.auditLog.clear()
    })

    await this.initializeDefaults()
  }

  // Fix existing field options format
  async fixFieldOptionsFormat() {
    try {
      const fields = await this.fields.toArray()
      const updates: any[] = []

      for (const field of fields) {
        if (field.options && typeof field.options === 'string' && !field.options.startsWith('[')) {
          // Convert comma-separated string to JSON array
          const optionsArray = field.options.split(',').map(opt => opt.trim())
          updates.push({
            id: field.id,
            options: JSON.stringify(optionsArray)
          })
        }
      }

      if (updates.length > 0) {
        await this.transaction('rw', this.fields, async () => {
          for (const update of updates) {
            await this.fields.update(update.id, { options: update.options })
          }
        })
      }

      return updates.length
    } catch (error) {
      console.error('Error fixing field options format:', error)
      throw error
    }
  }

  // Update ID field validation (remove pattern restriction)
  async updateIdFieldValidation() {
    try {
      const fields = await this.fields.toArray()
      const idField = fields.find(field => field.label === 'رقم الهوية')

      if (idField && idField.validation && idField.validation.pattern) {
        // Remove validation pattern from ID field
        const updatedValidation = { ...idField.validation }
        delete updatedValidation.pattern

        await this.fields.update(idField.id!, {
          validation: Object.keys(updatedValidation).length > 0 ? updatedValidation : undefined
        })

        console.log('ID field validation updated - pattern restriction removed')
      }
    } catch (error) {
      console.error('Error updating ID field validation:', error)
    }
  }

  private async addDefaultFields() {
    const defaultFields: Omit<SuspectField, 'id'>[] = [
      {
        label: 'رقم الملف',
        icon: 'fas fa-file-alt',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 1,
        validation: { minLength: 1, maxLength: 50 }
      },
      {
        label: 'الاسم الرباعي',
        icon: 'fas fa-user',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 2,
        validation: { minLength: 2, maxLength: 100 }
      },
      {
        label: 'رقم الهوية',
        icon: 'fas fa-id-card',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 3
      },
      {
        label: 'العنوان الكامل',
        icon: 'fas fa-map-marker-alt',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 4,
        validation: { maxLength: 500 }
      },
      {
        label: 'رقم الهاتف',
        icon: 'fas fa-phone',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 5,
        validation: { pattern: '^[0-9+\\-\\s()]{7,20}$' }
      },
      {
        label: 'صورة المتهم',
        icon: 'fas fa-camera',
        inputType: 'image',
        isRequired: false,
        isVisible: true,
        order: 6,
        validation: { fileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5242880 } // 5MB
      },
      {
        label: 'العمر',
        icon: 'fas fa-birthday-cake',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 7,
        validation: { pattern: '^[0-9]{1,3}$' }
      },
      {
        label: 'الجنسية',
        icon: 'fas fa-flag',
        inputType: 'select',
        isRequired: false,
        isVisible: true,
        order: 8,
        options: JSON.stringify(['سعودي', 'مصري', 'سوري', 'أردني', 'لبناني', 'عراقي', 'يمني', 'فلسطيني', 'أخرى'])
      },
      {
        label: 'المهنة',
        icon: 'fas fa-briefcase',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 9,
        validation: { maxLength: 100 }
      },
      {
        label: 'الحالة الاجتماعية',
        icon: 'fas fa-heart',
        inputType: 'select',
        isRequired: false,
        isVisible: true,
        order: 10,
        options: JSON.stringify(['أعزب', 'متزوج', 'مطلق', 'أرمل'])
      },
      {
        label: 'المضبوطات',
        icon: 'fas fa-box',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 11,
        validation: { maxLength: 1000 }
      },
      {
        label: 'تاريخ القبض',
        icon: 'fas fa-calendar-alt',
        inputType: 'date',
        isRequired: false,
        isVisible: true,
        order: 12
      },
      {
        label: 'الملاحظات',
        icon: 'fas fa-sticky-note',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 13,
        validation: { maxLength: 2000 }
      }
    ]

    await this.fields.bulkAdd(defaultFields)
  }

  private async addDefaultUser() {
    const defaultUser: Omit<User, 'id'> = {
      name: 'المدير العام',
      email: '<EMAIL>',
      role: {
        id: 'admin',
        name: 'admin',
        displayName: 'مدير النظام',
        permissions: [
          { id: '1', name: 'all', resource: '*', action: 'view' },
          { id: '2', name: 'all', resource: '*', action: 'create' },
          { id: '3', name: 'all', resource: '*', action: 'update' },
          { id: '4', name: 'all', resource: '*', action: 'delete' }
        ]
      },
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    }

    await this.users.add(defaultUser)
  }

  private async addDefaultSettings() {
    const defaultSettings: Omit<AppSettings, 'id'> = {
      theme: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#f8fafc',
        fontFamily: 'Cairo',
        fontSize: 14,
        borderRadius: 20,
        logoPosition: 'top-center',
        logoSize: 100
      },
      organization: {
        name: 'اسم المؤسسة',
        address: 'عنوان المؤسسة',
        phone: '+966xxxxxxxxx',
        email: '<EMAIL>'
      },
      fields: [],
      users: [],
      backup: {
        autoBackup: false,
        backupInterval: 24,
        maxBackups: 10
      },
      developerInfo: {
        title: 'تطوير وتصميم',
        developerName: 'م- محرم اليفرسي',
        supervisorName: 'ق/عبدالرحمن اليفرسي',
        icon: 'fas fa-code'
      }
    }

    await this.settings.add(defaultSettings)
  }

  // Audit Log
  async logAction(userId: string, action: string, resource: string, details?: any) {
    const logEntry: Omit<AuditLogEntry, 'id'> = {
      userId,
      action,
      resource,
      details,
      timestamp: new Date()
    }

    await this.auditLog.add(logEntry)
  }

  // Backup and Restore
  async exportData() {
    // Export IndexedDB data
    const indexedDBData: any = {
      users: await this.users.toArray(),
      suspects: await this.suspects.toArray(),
      fields: await this.fields.toArray(),
      attachments: await this.attachments.toArray(),
      settings: await this.settings.toArray(),
      reports: await this.reports.toArray()
    }

    // Try to export backup records if table exists (for version 2+)
    try {
      if (this.backupRecords) {
        indexedDBData.backupRecords = await this.backupRecords.toArray()
      }
    } catch (error) {
      console.warn('Backup records table not available (older database version)', error)
      indexedDBData.backupRecords = []
    }

    // Export localStorage data
    const localStorageData: Record<string, any> = {}

    // Get all localStorage keys and their values
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        try {
          const value = localStorage.getItem(key)
          if (value) {
            // Try to parse as JSON, if it fails store as string
            try {
              localStorageData[key] = JSON.parse(value)
            } catch {
              localStorageData[key] = value
            }
          }
        } catch (error) {
          console.warn(`Failed to export localStorage key: ${key}`, error)
        }
      }
    }

    const data = {
      // IndexedDB data
      ...indexedDBData,

      // localStorage data
      localStorage: localStorageData,

      // Metadata
      exportDate: new Date().toISOString(),
      version: '2.0.0', // Updated version to include localStorage
      exportType: 'complete' // Indicates this is a complete backup
    }

    console.log('📦 Exported data structure:', {
      indexedDBTables: Object.keys(indexedDBData),
      localStorageKeys: Object.keys(localStorageData),
      totalSize: JSON.stringify(data).length
    })

    return data
  }

  async importData(data: any) {
    console.log('🔄 Starting complete data import...')
    console.log('📦 Import data structure:', {
      version: data.version,
      exportType: data.exportType,
      hasLocalStorage: !!data.localStorage,
      indexedDBTables: Object.keys(data).filter(key => !['localStorage', 'exportDate', 'version', 'exportType'].includes(key))
    })

    // Import IndexedDB data - prepare tables list
    const tables: any[] = [this.users, this.suspects, this.fields, this.attachments, this.settings, this.reports]

    // Add backup records table if it exists (version 2+)
    try {
      if (this.backupRecords) {
        tables.push(this.backupRecords)
      }
    } catch (error) {
      console.warn('Backup records table not available for import')
    }

    await this.transaction('rw', tables, async () => {
      console.log('🗑️ Clearing existing IndexedDB data...')

      // Clear existing data
      await this.users.clear()
      await this.suspects.clear()
      await this.fields.clear()
      await this.attachments.clear()
      await this.settings.clear()
      await this.reports.clear()
      // Don't clear backup records to preserve backup history
      // await this.backupRecords.clear()

      console.log('📥 Importing IndexedDB data...')

      // Import new data
      if (data.users && data.users.length > 0) {
        await this.users.bulkAdd(data.users)
        console.log(`✅ Imported ${data.users.length} users`)
      }

      if (data.suspects && data.suspects.length > 0) {
        await this.suspects.bulkAdd(data.suspects)
        console.log(`✅ Imported ${data.suspects.length} suspects`)
      }

      if (data.fields && data.fields.length > 0) {
        await this.fields.bulkAdd(data.fields)
        console.log(`✅ Imported ${data.fields.length} fields`)
      }

      if (data.attachments && data.attachments.length > 0) {
        await this.attachments.bulkAdd(data.attachments)
        console.log(`✅ Imported ${data.attachments.length} attachments`)
      }

      if (data.settings && data.settings.length > 0) {
        await this.settings.bulkAdd(data.settings)
        console.log(`✅ Imported ${data.settings.length} settings`)
      }

      if (data.reports && data.reports.length > 0) {
        await this.reports.bulkAdd(data.reports)
        console.log(`✅ Imported ${data.reports.length} reports`)
      }

      if (data.backupRecords && data.backupRecords.length > 0) {
        try {
          if (this.backupRecords) {
            await this.backupRecords.bulkAdd(data.backupRecords)
            console.log(`✅ Imported ${data.backupRecords.length} backup records`)
          }
        } catch (error) {
          console.warn('Could not import backup records (table not available)', error)
        }
      }
    })

    // Import localStorage data
    if (data.localStorage && typeof data.localStorage === 'object') {
      console.log('🗑️ Clearing existing localStorage data...')

      // Clear existing localStorage (but preserve some system keys)
      const keysToPreserve = ['theme', 'language', 'user_preferences']
      const existingData: Record<string, string> = {}

      keysToPreserve.forEach(key => {
        const value = localStorage.getItem(key)
        if (value) {
          existingData[key] = value
        }
      })

      localStorage.clear()

      // Restore preserved keys
      Object.entries(existingData).forEach(([key, value]) => {
        localStorage.setItem(key, value)
      })

      console.log('📥 Importing localStorage data...')

      // Import new localStorage data
      let importedCount = 0
      Object.entries(data.localStorage).forEach(([key, value]) => {
        try {
          if (typeof value === 'object') {
            localStorage.setItem(key, JSON.stringify(value))
          } else {
            localStorage.setItem(key, String(value))
          }
          importedCount++
        } catch (error) {
          console.warn(`Failed to import localStorage key: ${key}`, error)
        }
      })

      console.log(`✅ Imported ${importedCount} localStorage items`)
    }

    console.log('✅ Complete data import finished successfully')

    // Trigger a page reload to ensure all stores are refreshed
    setTimeout(() => {
      console.log('🔄 Reloading page to refresh all data...')
      window.location.reload()
    }, 1000)
  }

  // Enhanced Backup Management
  async saveBackupRecord(record: BackupRecord): Promise<void> {
    await this.backupRecords.add(record)
  }

  async getBackupRecords(): Promise<BackupRecord[]> {
    return await this.backupRecords.orderBy('date').reverse().toArray()
  }

  async deleteBackupRecord(id: string): Promise<void> {
    await this.backupRecords.where('id').equals(id).delete()
  }

  async cleanupOldBackups(maxBackups: number, type?: 'auto' | 'manual'): Promise<number> {
    let query = this.backupRecords.orderBy('date').reverse()

    if (type) {
      query = query.filter(record => record.type === type)
    }

    const records = await query.toArray()

    if (records.length > maxBackups) {
      const recordsToDelete = records.slice(maxBackups)
      const idsToDelete = recordsToDelete.map(r => r.id)

      await this.backupRecords.where('id').anyOf(idsToDelete).delete()

      return recordsToDelete.length
    }

    return 0
  }
}

interface AuditLogEntry {
  id?: string
  userId: string
  action: string
  resource: string
  details?: any
  timestamp: Date
}

// Create and export database instance
export const db = new SuspectsDatabase()

// Initialize database
db.open().then(() => {
  db.initializeDefaults()
}).catch(error => {
  console.error('Failed to open database:', error)
})
