import Dexie, { Table } from 'dexie'
import type { 
  User, 
  SuspectData, 
  SuspectField, 
  SuspectAttachment, 
  AppSettings,
  ReportTemplate,
  NotificationItem
} from '@/types'

export class SuspectsDatabase extends Dexie {
  // Tables
  users!: Table<User>
  suspects!: Table<SuspectData>
  fields!: Table<SuspectField>
  attachments!: Table<SuspectAttachment>
  settings!: Table<AppSettings>
  reports!: Table<ReportTemplate>
  notifications!: Table<NotificationItem>
  auditLog!: Table<AuditLogEntry>

  constructor() {
    super('SuspectsDatabase')
    
    this.version(1).stores({
      users: '++id, email, role, isActive, createdAt',
      suspects: '++id, createdAt, updatedAt, createdBy, updatedBy',
      fields: '++id, label, inputType, isRequired, isVisible, order',
      attachments: '++id, fieldId, fileName, fileType, uploadedAt',
      settings: '++id, type',
      reports: '++id, name, type, createdAt',
      notifications: '++id, type, createdAt',
      auditLog: '++id, userId, action, resource, timestamp'
    })

    // Hooks
    this.suspects.hook('creating', (primKey, obj, trans) => {
      obj.createdAt = new Date()
      obj.updatedAt = new Date()
    })

    this.suspects.hook('updating', (modifications, primKey, obj, trans) => {
      modifications.updatedAt = new Date()
    })

    this.users.hook('creating', (primKey, obj, trans) => {
      obj.createdAt = new Date()
      obj.updatedAt = new Date()
    })
  }

  // Initialize default data
  async initializeDefaults() {
    const fieldsCount = await this.fields.count()

    if (fieldsCount === 0) {
      await this.addDefaultFields()
    }

    const usersCount = await this.users.count()
    if (usersCount === 0) {
      await this.addDefaultUser()
    }

    const settingsCount = await this.settings.count()
    if (settingsCount === 0) {
      await this.addDefaultSettings()
    }
  }

  // Clear all data and reinitialize
  async clearAndReinitialize() {
    await this.transaction('rw', this.users, this.suspects, this.fields, this.attachments, this.settings, this.reports, this.auditLog, async () => {
      await this.users.clear()
      await this.suspects.clear()
      await this.fields.clear()
      await this.attachments.clear()
      await this.settings.clear()
      await this.reports.clear()
      await this.auditLog.clear()
    })

    await this.initializeDefaults()
  }

  // Fix existing field options format
  async fixFieldOptionsFormat() {
    try {
      const fields = await this.fields.toArray()
      const updates: any[] = []

      for (const field of fields) {
        if (field.options && typeof field.options === 'string' && !field.options.startsWith('[')) {
          // Convert comma-separated string to JSON array
          const optionsArray = field.options.split(',').map(opt => opt.trim())
          updates.push({
            id: field.id,
            options: JSON.stringify(optionsArray)
          })
        }
      }

      if (updates.length > 0) {
        await this.transaction('rw', this.fields, async () => {
          for (const update of updates) {
            await this.fields.update(update.id, { options: update.options })
          }
        })
      }

      return updates.length
    } catch (error) {
      console.error('Error fixing field options format:', error)
      throw error
    }
  }

  private async addDefaultFields() {
    const defaultFields: Omit<SuspectField, 'id'>[] = [
      {
        label: 'رقم الملف',
        icon: 'fas fa-file-alt',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 1,
        validation: { minLength: 1, maxLength: 50 }
      },
      {
        label: 'الاسم الرباعي',
        icon: 'fas fa-user',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 2,
        validation: { minLength: 2, maxLength: 100 }
      },
      {
        label: 'رقم الهوية',
        icon: 'fas fa-id-card',
        inputType: 'text',
        isRequired: true,
        isVisible: true,
        order: 3,
        validation: { pattern: '^[0-9]{10}$' }
      },
      {
        label: 'العنوان الكامل',
        icon: 'fas fa-map-marker-alt',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 4,
        validation: { maxLength: 500 }
      },
      {
        label: 'رقم الهاتف',
        icon: 'fas fa-phone',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 5,
        validation: { pattern: '^[0-9+\\-\\s()]{7,20}$' }
      },
      {
        label: 'صورة المتهم',
        icon: 'fas fa-camera',
        inputType: 'image',
        isRequired: false,
        isVisible: true,
        order: 6,
        validation: { fileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5242880 } // 5MB
      },
      {
        label: 'العمر',
        icon: 'fas fa-birthday-cake',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 7,
        validation: { pattern: '^[0-9]{1,3}$' }
      },
      {
        label: 'الجنسية',
        icon: 'fas fa-flag',
        inputType: 'select',
        isRequired: false,
        isVisible: true,
        order: 8,
        options: JSON.stringify(['سعودي', 'مصري', 'سوري', 'أردني', 'لبناني', 'عراقي', 'يمني', 'فلسطيني', 'أخرى'])
      },
      {
        label: 'المهنة',
        icon: 'fas fa-briefcase',
        inputType: 'text',
        isRequired: false,
        isVisible: true,
        order: 9,
        validation: { maxLength: 100 }
      },
      {
        label: 'الحالة الاجتماعية',
        icon: 'fas fa-heart',
        inputType: 'select',
        isRequired: false,
        isVisible: true,
        order: 10,
        options: JSON.stringify(['أعزب', 'متزوج', 'مطلق', 'أرمل'])
      },
      {
        label: 'المضبوطات',
        icon: 'fas fa-box',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 11,
        validation: { maxLength: 1000 }
      },
      {
        label: 'تاريخ القبض',
        icon: 'fas fa-calendar-alt',
        inputType: 'date',
        isRequired: false,
        isVisible: true,
        order: 12
      },
      {
        label: 'الملاحظات',
        icon: 'fas fa-sticky-note',
        inputType: 'textarea',
        isRequired: false,
        isVisible: true,
        order: 13,
        validation: { maxLength: 2000 }
      }
    ]

    await this.fields.bulkAdd(defaultFields)
  }

  private async addDefaultUser() {
    const defaultUser: Omit<User, 'id'> = {
      name: 'المدير العام',
      email: '<EMAIL>',
      role: {
        id: 'admin',
        name: 'admin',
        displayName: 'مدير النظام',
        permissions: [
          { id: '1', name: 'all', resource: '*', action: 'view' },
          { id: '2', name: 'all', resource: '*', action: 'create' },
          { id: '3', name: 'all', resource: '*', action: 'update' },
          { id: '4', name: 'all', resource: '*', action: 'delete' }
        ]
      },
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    }

    await this.users.add(defaultUser)
  }

  private async addDefaultSettings() {
    const defaultSettings: Omit<AppSettings, 'id'> = {
      theme: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#f8fafc',
        fontFamily: 'Cairo',
        fontSize: 14,
        borderRadius: 20,
        logoPosition: 'top-center',
        logoSize: 100
      },
      organization: {
        name: 'اسم المؤسسة',
        address: 'عنوان المؤسسة',
        phone: '+966xxxxxxxxx',
        email: '<EMAIL>'
      },
      fields: [],
      users: [],
      backup: {
        autoBackup: false,
        backupInterval: 24,
        maxBackups: 10
      },
      developerInfo: {
        title: 'تطوير وتصميم',
        developerName: 'م- محرم اليفرسي',
        supervisorName: 'ق/عبدالرحمن اليفرسي',
        icon: 'fas fa-code'
      }
    }

    await this.settings.add(defaultSettings)
  }

  // Audit Log
  async logAction(userId: string, action: string, resource: string, details?: any) {
    const logEntry: Omit<AuditLogEntry, 'id'> = {
      userId,
      action,
      resource,
      details,
      timestamp: new Date()
    }

    await this.auditLog.add(logEntry)
  }

  // Backup and Restore
  async exportData() {
    const data = {
      users: await this.users.toArray(),
      suspects: await this.suspects.toArray(),
      fields: await this.fields.toArray(),
      attachments: await this.attachments.toArray(),
      settings: await this.settings.toArray(),
      reports: await this.reports.toArray(),
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    }

    return data
  }

  async importData(data: any) {
    await this.transaction('rw', this.users, this.suspects, this.fields, this.attachments, this.settings, this.reports, async () => {
      // Clear existing data
      await this.users.clear()
      await this.suspects.clear()
      await this.fields.clear()
      await this.attachments.clear()
      await this.settings.clear()
      await this.reports.clear()

      // Import new data
      if (data.users) await this.users.bulkAdd(data.users)
      if (data.suspects) await this.suspects.bulkAdd(data.suspects)
      if (data.fields) await this.fields.bulkAdd(data.fields)
      if (data.attachments) await this.attachments.bulkAdd(data.attachments)
      if (data.settings) await this.settings.bulkAdd(data.settings)
      if (data.reports) await this.reports.bulkAdd(data.reports)
    })
  }
}

interface AuditLogEntry {
  id?: string
  userId: string
  action: string
  resource: string
  details?: any
  timestamp: Date
}

// Create and export database instance
export const db = new SuspectsDatabase()

// Initialize database
db.open().then(() => {
  db.initializeDefaults()
}).catch(error => {
  console.error('Failed to open database:', error)
})
