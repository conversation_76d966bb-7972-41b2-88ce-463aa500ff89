<template>
  <div class="space-y-8">
    <!-- Theme Settings -->
    <div class="neumorphic-card">
      <div class="flex items-center gap-3 mb-6">
        <div class="neumorphic-icon">
          <i class="fas fa-palette text-primary-600"></i>
        </div>
        <div>
          <h2 class="text-xl font-bold text-secondary-800">تخصيص المظهر</h2>
          <p class="text-secondary-600">تخصيص الألوان والخطوط والتصميم العام</p>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Color Settings -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
            <i class="fas fa-paint-brush"></i>
            الألوان
          </h3>

          <!-- Primary Color -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              اللون الأساسي
            </label>
            <div class="flex items-center gap-3">
              <input
                v-model="themeData.primaryColor"
                type="color"
                class="w-12 h-12 rounded-neumorphic border-none cursor-pointer"
                @change="updateTheme"
              />
              <input
                v-model="themeData.primaryColor"
                type="text"
                class="neumorphic-input flex-1"
                placeholder="#3b82f6"
                @change="updateTheme"
              />
            </div>
          </div>

          <!-- Secondary Color -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              اللون الثانوي
            </label>
            <div class="flex items-center gap-3">
              <input
                v-model="themeData.secondaryColor"
                type="color"
                class="w-12 h-12 rounded-neumorphic border-none cursor-pointer"
                @change="updateTheme"
              />
              <input
                v-model="themeData.secondaryColor"
                type="text"
                class="neumorphic-input flex-1"
                placeholder="#64748b"
                @change="updateTheme"
              />
            </div>
          </div>

          <!-- Background Color -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              لون الخلفية
            </label>
            <div class="flex items-center gap-3">
              <input
                v-model="themeData.backgroundColor"
                type="color"
                class="w-12 h-12 rounded-neumorphic border-none cursor-pointer"
                @change="updateTheme"
              />
              <input
                v-model="themeData.backgroundColor"
                type="text"
                class="neumorphic-input flex-1"
                placeholder="#f8fafc"
                @change="updateTheme"
              />
            </div>
          </div>
        </div>

        <!-- Typography Settings -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
            <i class="fas fa-font"></i>
            الخطوط والنصوص
          </h3>

          <!-- Font Family -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              نوع الخط
            </label>
            <select
              v-model="themeData.fontFamily"
              class="neumorphic-select w-full"
              @change="updateTheme"
            >
              <option value="Cairo">Cairo</option>
              <option value="Tajawal">Tajawal</option>
              <option value="Amiri">Amiri</option>
              <option value="Noto Sans Arabic">Noto Sans Arabic</option>
            </select>
          </div>

          <!-- Font Size -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              حجم الخط: {{ themeData.fontSize }}px
            </label>
            <input
              v-model.number="themeData.fontSize"
              type="range"
              min="12"
              max="20"
              class="w-full"
              @input="updateTheme"
            />
          </div>

          <!-- Border Radius -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              انحناء الحواف: {{ themeData.borderRadius }}px
            </label>
            <input
              v-model.number="themeData.borderRadius"
              type="range"
              min="8"
              max="32"
              class="w-full"
              @input="updateTheme"
            />
          </div>
        </div>
      </div>

      <!-- Logo Settings -->
      <div class="mt-8 pt-8 border-t border-secondary-200">
        <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2 mb-6">
          <i class="fas fa-image"></i>
          إعدادات الشعار
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Logo Upload -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              رفع شعار جديد
            </label>
            <div class="neumorphic-card bg-white p-4">
              <div
                @click="triggerLogoUpload"
                @dragover.prevent
                @drop.prevent="handleLogoDrop"
                class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-8 text-center cursor-pointer hover:border-primary-400 transition-colors"
              >
                <div v-if="logoPreview" class="mb-4">
                  <img :src="logoPreview" alt="Logo Preview" class="max-w-32 max-h-32 mx-auto rounded-neumorphic" />
                </div>
                <div v-else class="text-secondary-500">
                  <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
                  <p>اضغط أو اسحب الشعار هنا</p>
                  <p class="text-sm">PNG, JPG, SVG (حد أقصى 2MB)</p>
                </div>
              </div>
              <input
                ref="logoInput"
                type="file"
                accept="image/*"
                @change="handleLogoUpload"
                class="hidden"
              />
            </div>
          </div>

          <!-- Logo Position & Size -->
          <div class="space-y-6">
            <!-- Logo Position -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                موضع الشعار
              </label>
              <div class="grid grid-cols-3 gap-2">
                <label
                  v-for="position in logoPositions"
                  :key="position.value"
                  :class="[
                    'neumorphic-button cursor-pointer text-center transition-all duration-300',
                    themeData.logoPosition === position.value 
                      ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' 
                      : ''
                  ]"
                >
                  <input
                    v-model="themeData.logoPosition"
                    :value="position.value"
                    type="radio"
                    class="hidden"
                    @change="updateTheme"
                  />
                  <div class="flex flex-col items-center gap-1 p-2">
                    <i :class="position.icon"></i>
                    <span class="text-xs">{{ position.label }}</span>
                  </div>
                </label>
              </div>
            </div>

            <!-- Logo Size -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                حجم الشعار: {{ themeData.logoSize }}px
              </label>
              <input
                v-model.number="themeData.logoSize"
                type="range"
                min="50"
                max="200"
                class="w-full"
                @input="updateTheme"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Preview Section -->
      <div class="mt-8 pt-8 border-t border-secondary-200">
        <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2 mb-6">
          <i class="fas fa-eye"></i>
          معاينة المظهر
        </h3>
        
        <div class="neumorphic-card bg-white p-6" :style="previewStyles">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-primary-500 rounded-full"></div>
              <h4 class="font-semibold">عنوان تجريبي</h4>
            </div>
            <button class="px-4 py-2 bg-primary-500 text-white rounded-lg">زر تجريبي</button>
          </div>
          <p class="text-secondary-600 mb-4">هذا نص تجريبي لمعاينة الخط والألوان المختارة.</p>
          <div class="grid grid-cols-3 gap-4">
            <div class="p-3 bg-secondary-100 rounded-lg text-center">عنصر 1</div>
            <div class="p-3 bg-secondary-100 rounded-lg text-center">عنصر 2</div>
            <div class="p-3 bg-secondary-100 rounded-lg text-center">عنصر 3</div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3 mt-8 pt-8 border-t border-secondary-200">
        <button
          @click="resetToDefaults"
          class="neumorphic-button text-warning-600 hover:text-warning-700"
        >
          <i class="fas fa-undo ml-2"></i>
          استعادة الافتراضي
        </button>
        <button
          @click="saveTheme"
          class="neumorphic-button text-success-600 hover:text-success-700"
          :disabled="loading"
        >
          <i class="fas fa-save ml-2"></i>
          حفظ التغييرات
        </button>
      </div>
    </div>

    <!-- Organization Settings -->
    <div class="neumorphic-card">
      <div class="flex items-center gap-3 mb-6">
        <div class="neumorphic-icon">
          <i class="fas fa-building text-primary-600"></i>
        </div>
        <div>
          <h2 class="text-xl font-bold text-secondary-800">بيانات المؤسسة</h2>
          <p class="text-secondary-600">تحديث معلومات المؤسسة للتقارير والمراسلات</p>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Basic Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
            <i class="fas fa-info-circle"></i>
            المعلومات الأساسية
          </h3>

          <!-- Organization Name -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              اسم المؤسسة *
            </label>
            <input
              v-model="organizationData.name"
              type="text"
              class="neumorphic-input w-full"
              placeholder="اسم المؤسسة"
              @input="updateOrganization"
            />
          </div>

          <!-- Organization Name (English) -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              اسم المؤسسة (بالإنجليزية)
            </label>
            <input
              v-model="organizationData.nameEn"
              type="text"
              class="neumorphic-input w-full ltr"
              placeholder="Organization Name"
              @input="updateOrganization"
            />
          </div>

          <!-- Address -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              العنوان *
            </label>
            <textarea
              v-model="organizationData.address"
              class="neumorphic-input w-full h-24 resize-none"
              placeholder="العنوان الكامل للمؤسسة"
              @input="updateOrganization"
            ></textarea>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
            <i class="fas fa-address-book"></i>
            معلومات الاتصال
          </h3>

          <!-- Phone -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              رقم الهاتف *
            </label>
            <input
              v-model="organizationData.phone"
              type="tel"
              class="neumorphic-input w-full"
              placeholder="+966xxxxxxxxx"
              @input="updateOrganization"
            />
          </div>

          <!-- Email -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              البريد الإلكتروني *
            </label>
            <input
              v-model="organizationData.email"
              type="email"
              class="neumorphic-input w-full ltr"
              placeholder="<EMAIL>"
              @input="updateOrganization"
            />
          </div>

          <!-- Website -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              الموقع الإلكتروني
            </label>
            <input
              v-model="organizationData.website"
              type="url"
              class="neumorphic-input w-full ltr"
              placeholder="https://www.organization.com"
              @input="updateOrganization"
            />
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3 mt-8 pt-8 border-t border-secondary-200">
        <button
          @click="saveOrganization"
          class="neumorphic-button text-success-600 hover:text-success-700"
          :disabled="loading"
        >
          <i class="fas fa-save ml-2"></i>
          حفظ بيانات المؤسسة
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ThemeSettings, OrganizationSettings } from '@/types'

// Props
interface Props {
  theme: ThemeSettings
  organization: OrganizationSettings
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  updateTheme: [theme: Partial<ThemeSettings>]
  updateOrganization: [organization: Partial<OrganizationSettings>]
}>()

// Reactive data
const themeData = ref<ThemeSettings>({ ...props.theme })
const organizationData = ref<OrganizationSettings>({ ...props.organization })
const logoPreview = ref<string>('')
const logoInput = ref<HTMLInputElement>()

// Logo positions
const logoPositions = [
  { value: 'top-left', label: 'يسار أعلى', icon: 'fas fa-arrow-up' },
  { value: 'top-center', label: 'وسط أعلى', icon: 'fas fa-arrow-up' },
  { value: 'top-right', label: 'يمين أعلى', icon: 'fas fa-arrow-up' }
]

// Computed
const previewStyles = computed(() => ({
  fontFamily: themeData.value.fontFamily,
  fontSize: `${themeData.value.fontSize}px`,
  borderRadius: `${themeData.value.borderRadius}px`,
  '--primary-color': themeData.value.primaryColor,
  '--secondary-color': themeData.value.secondaryColor,
  '--background-color': themeData.value.backgroundColor
}))

// Methods
function updateTheme() {
  emit('updateTheme', { ...themeData.value })
}

function updateOrganization() {
  emit('updateOrganization', { ...organizationData.value })
}

function triggerLogoUpload() {
  logoInput.value?.click()
}

function handleLogoUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processLogoFile(file)
  }
}

function handleLogoDrop(event: DragEvent) {
  const file = event.dataTransfer?.files[0]
  if (file && file.type.startsWith('image/')) {
    processLogoFile(file)
  }
}

function processLogoFile(file: File) {
  if (file.size > 2 * 1024 * 1024) {
    alert('حجم الملف كبير جداً. الحد الأقصى 2MB')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    logoPreview.value = e.target?.result as string
    themeData.value.customLogo = logoPreview.value
    updateTheme()
  }
  reader.readAsDataURL(file)
}

function resetToDefaults() {
  themeData.value = {
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    backgroundColor: '#f8fafc',
    fontFamily: 'Cairo',
    fontSize: 14,
    borderRadius: 20,
    logoPosition: 'top-center',
    logoSize: 100
  }
  logoPreview.value = ''
  updateTheme()
}

function saveTheme() {
  updateTheme()
}

function saveOrganization() {
  updateOrganization()
}

// Initialize
onMounted(() => {
  if (props.theme.customLogo) {
    logoPreview.value = props.theme.customLogo
  }
})
</script>
