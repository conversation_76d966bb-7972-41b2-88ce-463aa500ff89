import { ref } from 'vue'
import type { BackupRecord, BackupProgress, BackupSettings } from '@/types'
import { db } from '@/utils/database'
import CryptoJS from 'crypto-js'
import pako from 'pako'

/**
 * Enhanced Backup Service
 * Provides advanced backup functionality while maintaining compatibility with existing system
 */
export class BackupService {
  private static instance: BackupService
  private progress = ref<BackupProgress | null>(null)
  private isProcessing = ref(false)

  static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService()
    }
    return BackupService.instance
  }

  /**
   * Get current backup progress
   */
  getProgress() {
    return {
      progress: this.progress,
      isProcessing: this.isProcessing
    }
  }

  /**
   * Create a new backup with enhanced features
   */
  async createBackup(
    type: 'auto' | 'manual',
    settings?: Partial<BackupSettings>,
    description?: string
  ): Promise<BackupRecord | null> {
    if (this.isProcessing.value) {
      throw new Error('عملية نسخ احتياطي أخرى قيد التنفيذ')
    }

    this.isProcessing.value = true
    
    try {
      // Stage 1: Preparing
      this.updateProgress('preparing', 10, 'جاري تحضير البيانات...')
      
      const timestamp = new Date()
      const backupId = this.generateBackupId(type, timestamp)
      
      // Stage 2: Exporting data
      this.updateProgress('exporting', 30, 'جاري تصدير البيانات...')
      const data = await this.exportData()
      
      let processedData = JSON.stringify(data, null, 2)
      let isCompressed = false
      let isEncrypted = false
      
      // Stage 3: Compression (if enabled)
      if (settings?.compressionEnabled) {
        this.updateProgress('compressing', 50, 'جاري ضغط البيانات...')
        processedData = this.compressData(processedData)
        isCompressed = true
      }
      
      // Stage 4: Encryption (if enabled)
      if (settings?.encryptBackups && settings?.backupPassword) {
        this.updateProgress('encrypting', 70, 'جاري تشفير البيانات...')
        processedData = this.encryptData(processedData, settings.backupPassword)
        isEncrypted = true
      }
      
      // Stage 5: Calculate checksum
      const checksum = this.calculateChecksum(processedData)
      
      // Stage 6: Save backup
      this.updateProgress('saving', 90, 'جاري حفظ النسخة الاحتياطية...')
      
      const fileName = this.generateFileName(type, timestamp, isEncrypted, isCompressed)
      const size = new Blob([processedData]).size
      
      // Create backup record
      const backupRecord: BackupRecord = {
        id: backupId,
        type,
        date: timestamp,
        size,
        fileName,
        checksum,
        status: 'completed',
        encrypted: isEncrypted,
        compressed: isCompressed,
        description: description || `${type === 'auto' ? 'نسخة تلقائية' : 'نسخة يدوية'} - ${timestamp.toLocaleDateString('ar-SA')}`
      }
      
      // Save to IndexedDB
      await this.saveBackupRecord(backupRecord)
      
      // Download the backup file
      this.downloadBackupFile(fileName, processedData)
      
      this.updateProgress('completed', 100, 'تم إنشاء النسخة الاحتياطية بنجاح!')
      
      // Clear progress after 2 seconds
      setTimeout(() => {
        this.progress.value = null
      }, 2000)
      
      return backupRecord
      
    } catch (error) {
      console.error('Backup creation failed:', error)
      this.updateProgress('error', 0, 'فشل في إنشاء النسخة الاحتياطية', error instanceof Error ? error.message : 'خطأ غير معروف')
      
      setTimeout(() => {
        this.progress.value = null
      }, 5000)
      
      return null
    } finally {
      this.isProcessing.value = false
    }
  }

  /**
   * Restore from backup with validation
   */
  async restoreFromBackup(
    file: File,
    password?: string
  ): Promise<boolean> {
    if (this.isProcessing.value) {
      throw new Error('عملية أخرى قيد التنفيذ')
    }

    this.isProcessing.value = true
    
    try {
      this.updateProgress('preparing', 10, 'جاري قراءة ملف النسخة الاحتياطية...')
      
      let fileContent = await file.text()
      
      // Check if file is encrypted
      if (this.isEncrypted(fileContent)) {
        if (!password) {
          throw new Error('هذا الملف مشفر ويتطلب كلمة مرور')
        }
        
        this.updateProgress('preparing', 30, 'جاري فك التشفير...')
        fileContent = this.decryptData(fileContent, password)
      }
      
      // Check if file is compressed
      if (this.isCompressed(fileContent)) {
        this.updateProgress('preparing', 50, 'جاري إلغاء الضغط...')
        fileContent = this.decompressData(fileContent)
      }
      
      this.updateProgress('preparing', 70, 'جاري التحقق من سلامة البيانات...')
      
      // Parse and validate data
      const data = JSON.parse(fileContent)
      if (!this.validateBackupData(data)) {
        throw new Error('ملف النسخة الاحتياطية تالف أو غير صالح')
      }
      
      this.updateProgress('preparing', 90, 'جاري استعادة البيانات...')
      
      // Import data using existing function to maintain compatibility
      await db.importData(data)
      
      this.updateProgress('completed', 100, 'تم استعادة البيانات بنجاح!')
      
      setTimeout(() => {
        this.progress.value = null
      }, 2000)
      
      return true
      
    } catch (error) {
      console.error('Restore failed:', error)
      this.updateProgress('error', 0, 'فشل في استعادة البيانات', error instanceof Error ? error.message : 'خطأ غير معروف')
      
      setTimeout(() => {
        this.progress.value = null
      }, 5000)
      
      return false
    } finally {
      this.isProcessing.value = false
    }
  }

  /**
   * Get backup history from IndexedDB
   */
  async getBackupHistory(): Promise<BackupRecord[]> {
    try {
      // Try to get from a dedicated backup records table
      // For now, we'll store in localStorage as a simple solution
      const stored = localStorage.getItem('backup_history')
      if (stored) {
        const records = JSON.parse(stored) as BackupRecord[]
        // Convert date strings back to Date objects
        return records.map(record => ({
          ...record,
          date: new Date(record.date)
        }))
      }
      return []
    } catch (error) {
      console.error('Failed to load backup history:', error)
      return []
    }
  }

  /**
   * Delete a backup record
   */
  async deleteBackupRecord(backupId: string): Promise<boolean> {
    try {
      const history = await this.getBackupHistory()
      const updatedHistory = history.filter(record => record.id !== backupId)
      localStorage.setItem('backup_history', JSON.stringify(updatedHistory))
      return true
    } catch (error) {
      console.error('Failed to delete backup record:', error)
      return false
    }
  }

  // Private helper methods
  private updateProgress(stage: BackupProgress['stage'], progress: number, message: string, details?: string) {
    this.progress.value = {
      stage,
      progress,
      message,
      details
    }
  }

  private async exportData() {
    // Use existing export function to maintain compatibility
    return await db.exportData()
  }

  private generateBackupId(type: string, timestamp: Date): string {
    return `backup_${type}_${timestamp.getTime()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateFileName(type: string, timestamp: Date, encrypted: boolean, compressed: boolean): string {
    const dateStr = timestamp.toISOString().split('T')[0]
    const timeStr = timestamp.toTimeString().split(' ')[0].replace(/:/g, '-')
    let extension = '.json'
    
    if (compressed) extension = '.gz' + extension
    if (encrypted) extension = '.enc' + extension
    
    return `backup-${type}-${dateStr}-${timeStr}${extension}`
  }

  private compressData(data: string): string {
    try {
      const compressed = pako.gzip(data, { to: 'string' })
      return btoa(compressed) // Base64 encode for safe storage
    } catch (error) {
      console.error('Compression failed:', error)
      throw new Error('فشل في ضغط البيانات')
    }
  }

  private decompressData(data: string): string {
    try {
      const decoded = atob(data) // Base64 decode
      return pako.ungzip(decoded, { to: 'string' })
    } catch (error) {
      console.error('Decompression failed:', error)
      throw new Error('فشل في إلغاء ضغط البيانات')
    }
  }

  private encryptData(data: string, password: string): string {
    try {
      return CryptoJS.AES.encrypt(data, password).toString()
    } catch (error) {
      console.error('Encryption failed:', error)
      throw new Error('فشل في تشفير البيانات')
    }
  }

  private decryptData(encryptedData: string, password: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, password)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      if (!decrypted) {
        throw new Error('كلمة المرور غير صحيحة')
      }
      return decrypted
    } catch (error) {
      console.error('Decryption failed:', error)
      throw new Error('فشل في فك التشفير - تأكد من كلمة المرور')
    }
  }

  private calculateChecksum(data: string): string {
    return CryptoJS.SHA256(data).toString()
  }

  private isEncrypted(data: string): boolean {
    // Simple check for AES encrypted data format
    return data.startsWith('U2FsdGVkX1') || /^[A-Za-z0-9+/]+=*$/.test(data.substring(0, 100))
  }

  private isCompressed(data: string): boolean {
    // Check if data is base64 encoded (compressed data)
    try {
      const decoded = atob(data.substring(0, 100))
      return decoded.includes('\x1f\x8b') // gzip magic number
    } catch {
      return false
    }
  }

  private validateBackupData(data: any): boolean {
    // Basic validation of backup data structure
    return data && 
           typeof data === 'object' && 
           data.version && 
           data.exportDate &&
           (data.users || data.suspects || data.settings)
  }

  private async saveBackupRecord(record: BackupRecord): Promise<void> {
    try {
      const history = await this.getBackupHistory()
      history.unshift(record) // Add to beginning
      
      // Keep only the latest records based on maxBackups setting
      // For now, we'll keep the last 50 records
      const maxRecords = 50
      if (history.length > maxRecords) {
        history.splice(maxRecords)
      }
      
      localStorage.setItem('backup_history', JSON.stringify(history))
    } catch (error) {
      console.error('Failed to save backup record:', error)
      throw new Error('فشل في حفظ سجل النسخة الاحتياطية')
    }
  }

  private downloadBackupFile(fileName: string, content: string): void {
    try {
      const blob = new Blob([content], { type: 'application/octet-stream' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Download failed:', error)
      throw new Error('فشل في تحميل ملف النسخة الاحتياطية')
    }
  }
}
