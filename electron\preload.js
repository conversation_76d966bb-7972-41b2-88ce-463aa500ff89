const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (data) => ipcRenderer.invoke('dialog:saveFile', data),

  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  getUserDataPath: () => ipcRenderer.invoke('app:getUserDataPath'),

  // Window controls
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),

  // Database operations (for future SQLite integration)
  dbQuery: (sql, params) => ipcRenderer.invoke('db:query', sql, params),
  dbExecute: (sql, params) => ipcRenderer.invoke('db:execute', sql, params),

  // Activation operations
  getActivationStatus: () => ipcRenderer.invoke('activation:getStatus'),
  saveActivationStatus: (data) => ipcRenderer.invoke('activation:saveStatus', data),
  clearActivationStatus: () => ipcRenderer.invoke('activation:clearStatus'),

  // Dialog operations
  showMessageBox: (options) => ipcRenderer.invoke('dialog:showMessageBox', options),

  // System info
  getPlatform: () => process.platform,
  getArch: () => process.arch,

  // Event listeners
  onMenuAction: (callback) => ipcRenderer.on('menu:action', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})

// Prevent the renderer process from accessing Node.js
delete window.require
delete window.exports
delete window.module
