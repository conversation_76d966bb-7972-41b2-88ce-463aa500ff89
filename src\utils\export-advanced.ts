// Advanced export utilities for database tables

import type { DatabaseTab, DatabaseColumn, DatabaseRow } from '@/types/database'
import { downloadBlob } from '@/utils/helpers'
import { generateBrandedHTMLDocument, generateReportStyles } from '@/utils/report-template'
import { useSettingsStore } from '@/stores/settings'

/**
 * Export table data to CSV format with Arabic support
 */
export async function exportToCSV(
  tab: DatabaseTab,
  filteredRows?: DatabaseRow[],
  searchQuery?: string
): Promise<void> {
  try {
    const visibleColumns = tab.columns.filter(col => col.isVisible).sort((a, b) => a.order - b.order)
    const rows = filteredRows || tab.rows.filter(row => !row.isGroupHeader || tab.settings.exportSettings.includeGroupHeaders)
    
    // Create CSV content with BOM for Arabic support
    let csvContent = '\uFEFF' // BOM for UTF-8
    
    // Add headers if enabled
    if (tab.settings.exportSettings.includeHeaders) {
      const headers = visibleColumns.map(col => `"${col.name}"`)
      csvContent += headers.join(',') + '\n'
    }
    
    // Add data rows
    for (const row of rows) {
      if (row.isGroupHeader && tab.settings.exportSettings.includeGroupHeaders) {
        // Group header row
        const groupHeaderRow = Array(visibleColumns.length).fill('')
        groupHeaderRow[0] = `"${row.groupHeaderText || ''}"`
        csvContent += groupHeaderRow.join(',') + '\n'
      } else if (!row.isGroupHeader) {
        // Data row
        const rowData = visibleColumns.map(col => {
          const value = row.data[col.id] || ''
          // Escape quotes and wrap in quotes if contains comma or quote
          const stringValue = String(value)
          if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`
          }
          return stringValue
        })
        csvContent += rowData.join(',') + '\n'
      }
    }
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
    const filename = generateExportFilename(tab.name, 'csv', searchQuery)
    downloadBlob(blob, filename)
    
  } catch (error) {
    console.error('Error exporting to CSV:', error)
    throw new Error('فشل في تصدير ملف CSV')
  }
}

/**
 * Export table data to HTML format for printing/PDF conversion
 */
export async function exportToHTML(
  tab: DatabaseTab,
  filteredRows?: DatabaseRow[],
  searchQuery?: string
): Promise<void> {
  try {
    const visibleColumns = tab.columns.filter(col => col.isVisible).sort((a, b) => a.order - b.order)
    const rows = filteredRows || tab.rows.filter(row => !row.isGroupHeader || tab.settings.exportSettings.includeGroupHeaders)

    // Generate table content
    let tableContent = '<table>'

    // Add table headers
    if (tab.settings.exportSettings.includeHeaders) {
      tableContent += '<thead><tr>'
      if (tab.settings.showRowNumbers) {
        tableContent += '<th style="width: 50px;">#</th>'
      }
      for (const col of visibleColumns) {
        const style = getColumnHeaderStyle(col)
        const width = getColumnWidthPercentage(col, visibleColumns)
        tableContent += `<th style="${style}; width: ${width}%">${col.name}</th>`
      }
      tableContent += '</tr></thead>'
    }

    // Add table body
    tableContent += '<tbody>'
    let rowNumber = 1

    for (const row of rows) {
      if (row.isGroupHeader && tab.settings.exportSettings.includeGroupHeaders) {
        // Group header row
        const colspan = visibleColumns.length + (tab.settings.showRowNumbers ? 1 : 0)
        const style = getGroupHeaderStyle(tab.groupHeaders)
        tableContent += `<tr><td colspan="${colspan}" class="group-header" style="${style}">${row.groupHeaderText || ''}</td></tr>`
      } else if (!row.isGroupHeader) {
        // Data row
        const rowClass = tab.settings.alternateRowColors && rowNumber % 2 === 0 ? 'alternate-row' : ''
        tableContent += `<tr class="${rowClass}">`

        if (tab.settings.showRowNumbers) {
          tableContent += `<td style="text-align: center; color: #666;">${rowNumber}</td>`
        }

        for (const col of visibleColumns) {
          const value = row.data[col.id] || ''
          const style = getColumnCellStyle(col)
          tableContent += `<td style="${style}">${escapeHtml(String(value))}</td>`
        }

        tableContent += '</tr>'
        rowNumber++
      }
    }

    tableContent += '</tbody></table>'

    // Add search info if applicable
    if (searchQuery) {
      tableContent += `
        <div style="margin-top: 20px; padding: 10px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #2196f3;">
          <strong>معلومات البحث:</strong><br>
          كلمة البحث: "${searchQuery}"<br>
          عدد النتائج: ${rows.filter(r => !r.isGroupHeader).length} صف
        </div>
      `
    }

    // Generate complete HTML document with branding
    const title = tab.name
    const subtitle = searchQuery ? `نتائج البحث: "${searchQuery}"` : undefined
    const htmlContent = generateBrandedHTMLDocument(title, tableContent, subtitle)
    
    // Create and download file
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
    const filename = generateExportFilename(tab.name, 'html', searchQuery)
    downloadBlob(blob, filename)
    
  } catch (error) {
    console.error('Error exporting to HTML:', error)
    throw new Error('فشل في تصدير ملف HTML')
  }
}

/**
 * Helper function to calculate column width percentage
 */
function getColumnWidthPercentage(column: DatabaseColumn, visibleColumns: DatabaseColumn[]): number {
  const totalWidth = visibleColumns.reduce((sum, col) => sum + (col.width || 150), 0)
  const columnWidth = column.width || 150
  return Math.round((columnWidth / totalWidth) * 100)
}

/**
 * Helper function to get column header style
 */
function getColumnHeaderStyle(column: DatabaseColumn): string {
  const formatting = column.formatting
  return [
    `background-color: ${formatting.headerBackgroundColor}`,
    `color: ${formatting.headerTextColor}`,
    `font-size: ${formatting.headerFontSize}px`,
    `font-weight: ${formatting.headerFontWeight}`,
    `text-align: ${formatting.headerTextAlign}`,
    `border: ${formatting.borderWidth}px ${formatting.borderStyle} ${formatting.borderColor}`
  ].join('; ')
}

/**
 * Helper function to get column cell style
 */
function getColumnCellStyle(column: DatabaseColumn): string {
  const formatting = column.formatting
  return [
    `background-color: ${formatting.cellBackgroundColor}`,
    `color: ${formatting.cellTextColor}`,
    `font-size: ${formatting.cellFontSize}px`,
    `font-weight: ${formatting.cellFontWeight}`,
    `text-align: ${formatting.cellTextAlign}`,
    `white-space: ${formatting.cellTextWrap ? 'normal' : 'nowrap'}`,
    `border: ${formatting.borderWidth}px ${formatting.borderStyle} ${formatting.borderColor}`
  ].join('; ')
}

/**
 * Helper function to get group header style
 */
function getGroupHeaderStyle(groupHeaders: any): string {
  return [
    `background-color: ${groupHeaders.backgroundColor}`,
    `color: ${groupHeaders.textColor}`,
    `font-size: ${groupHeaders.fontSize}px`,
    `font-weight: ${groupHeaders.fontWeight}`,
    `text-align: ${groupHeaders.textAlign}`
  ].join('; ')
}

/**
 * Helper function to escape HTML
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * Generate export filename based on search query and current time
 */
function generateExportFilename(tabName: string, format: string, searchQuery?: string): string {
  const now = new Date()
  const date = now.toISOString().split('T')[0] // YYYY-MM-DD
  const time = now.toTimeString().split(' ')[0].replace(/:/g, '-') // HH-MM-SS

  if (searchQuery && searchQuery.trim()) {
    // Clean search query for filename (remove special characters)
    const cleanQuery = searchQuery.trim()
      .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .substring(0, 50) // Limit length

    return `${tabName}_بحث_${cleanQuery}_${date}_${time}.${format}`
  } else {
    return `${tabName}_${date}.${format}`
  }
}

/**
 * Export table data with custom format options and filtering support
 */
export async function exportWithOptions(
  tab: DatabaseTab,
  format: 'csv' | 'html',
  options?: {
    includeHeaders?: boolean
    includeGroupHeaders?: boolean
    selectedRowsOnly?: boolean
    filteredRows?: DatabaseRow[]
    searchQuery?: string
  }
): Promise<void> {
  // Temporarily override settings if options provided
  const originalSettings = { ...tab.settings.exportSettings }

  if (options) {
    if (options.includeHeaders !== undefined) {
      tab.settings.exportSettings.includeHeaders = options.includeHeaders
    }
    if (options.includeGroupHeaders !== undefined) {
      tab.settings.exportSettings.includeGroupHeaders = options.includeGroupHeaders
    }
  }

  try {
    if (format === 'csv') {
      await exportToCSV(tab, options?.filteredRows, options?.searchQuery)
    } else if (format === 'html') {
      await exportToHTML(tab, options?.filteredRows, options?.searchQuery)
    }
  } finally {
    // Restore original settings
    tab.settings.exportSettings = originalSettings
  }
}
