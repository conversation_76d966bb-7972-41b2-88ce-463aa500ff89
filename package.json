{"name": "suspects-data-management", "version": "1.0.0", "description": "برنامج متكامل لتنظيم بيانات المتهمين", "type": "module", "scripts": {"dev": "vite --port 5175", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5175 && electron .\"", "build-electron": "npm run build && electron-builder", "electron-build-win": "npm run build && electron-builder --win", "electron-build-win-x64": "npm run build && electron-builder --win --x64", "electron-build-win-ia32": "npm run build && electron-builder --win --ia32", "electron-build-portable": "npm run build && electron-builder --win portable", "electron-dist": "npm run build && electron-builder --publish=never", "electron-publish": "npm run build && electron-builder --publish=always", "postinstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild", "pack": "electron-builder --dir", "package": "npm run build && electron-packager . --platform=win32 --arch=x64 --out=dist-packager --overwrite", "dist": "npm run build && electron-builder --publish=never", "release": "npm run build && electron-builder --publish=always", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@vueuse/core": "^10.7.0", "bcryptjs": "^2.4.3", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "date-fns": "^2.30.0", "dexie": "^3.2.4", "electron-log": "^5.4.1", "electron-settings": "^4.0.4", "electron-updater": "^6.6.2", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "pako": "^2.1.0", "papaparse": "^5.5.3", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/jszip": "^3.4.1", "@types/node": "^20.10.0", "@types/pako": "^2.0.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-packager": "^17.1.2", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "main": "electron/main.js", "homepage": "./", "author": {"name": "م. محر<PERSON> اليفرسي", "email": "<EMAIL>"}, "build": {"appId": "com.suspects.data.management", "productName": "برنامج بيانات المتهمين", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis", "requestedExecutionLevel": "asInvoker", "sign": false}, "linux": {"target": "AppImage"}}}