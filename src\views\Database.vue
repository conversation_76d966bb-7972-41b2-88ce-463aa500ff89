<template>
  <div class="min-h-screen bg-secondary-50 p-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <i class="fas fa-database text-blue-600 text-xl"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-800">قاعدة بيانات وسجلات المتهمين</h1>
            <p class="text-gray-600">إدارة وتحليل البيانات المتقدمة</p>
          </div>
        </div>
        
        <!-- Header Actions -->
        <div class="flex items-center gap-3">
          <button
            @click="showAddTabModal = true"
            class="bg-white px-6 py-3 rounded-lg shadow-lg border border-gray-200 text-green-600 hover:text-green-700 hover:shadow-xl transition-all duration-300 flex items-center gap-2"
          >
            <i class="fas fa-plus"></i>
            إضافة تبويب
          </button>

          <button
            @click="showImportModal = true"
            class="bg-white px-6 py-3 rounded-lg shadow-lg border border-gray-200 text-blue-600 hover:text-blue-700 hover:shadow-xl transition-all duration-300 flex items-center gap-2"
          >
            <i class="fas fa-file-excel"></i>
            استيراد Excel
          </button>

          <button
            @click="openSearchMemoModal"
            class="bg-white px-6 py-3 rounded-lg shadow-lg border border-gray-200 text-purple-600 hover:text-purple-700 hover:shadow-xl transition-all duration-300 flex items-center gap-2"
          >
            <i class="fas fa-search-plus"></i>
            إعداد مذكرة بحث ومطابقة
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="bg-white rounded-lg shadow-lg p-4 mb-6" v-if="tabs.length > 0">
      <div class="flex items-center gap-2 overflow-x-auto">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'px-4 py-2 rounded-lg transition-all duration-300 whitespace-nowrap flex items-center gap-2',
            activeTab === tab.id
              ? 'bg-blue-100 text-blue-700 shadow-md'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
          <button
            v-if="tabs.length > 1 && permissions.database?.delete"
            @click.stop="removeTab(tab.id)"
            class="mr-2 text-red-500 hover:text-red-700"
          >
            <i class="fas fa-times text-xs"></i>
          </button>
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div v-if="activeTabData" class="space-y-6">
      <!-- Top Actions Bar -->
      <div class="bg-white rounded-lg shadow-lg border border-secondary-200 p-4 mb-4">
        <div class="flex items-center justify-between gap-4">
          <!-- Search Section -->
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="البحث في البيانات..."
                class="w-full pl-10 pr-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-secondary-400"></i>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <!-- Important Content Tools -->
            <div class="flex items-center gap-2 border-r border-secondary-200 pr-4">
              <span class="text-xs font-medium text-secondary-600 hidden lg:block">المحتوى المهم</span>

              <!-- Color Brush Tool -->
              <div class="relative">
                <button
                  @click="toggleColorBrush"
                  :class="[
                    'neumorphic-button flex items-center gap-1 px-3 py-2 text-sm transition-all',
                    isColorBrushActive ? 'bg-warning-100 text-warning-700 shadow-inner' : 'text-warning-600 hover:text-warning-700'
                  ]"
                  title="فرشاة تحديد المحتوى المهم"
                >
                  <i class="fas fa-paint-brush"></i>
                  <span class="hidden sm:inline">فرشاة</span>
                </button>

                <!-- Color Picker -->
                <div v-if="showColorPicker" class="absolute top-full left-0 mt-2 z-50">
                  <div class="bg-white rounded-lg shadow-xl border border-secondary-200 p-4 min-w-48">
                    <div class="mb-3">
                      <label class="block text-sm font-medium text-secondary-700 mb-2">اختر لون المحتوى المهم</label>
                      <input
                        v-model="selectedImportantColor"
                        type="color"
                        class="w-full h-8 rounded border border-secondary-300"
                      />
                    </div>
                    <div class="flex gap-2">
                      <button
                        @click="applyColorBrush"
                        class="bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700"
                      >
                        تطبيق
                      </button>
                      <button
                        @click="showColorPicker = false"
                        class="bg-secondary-300 text-secondary-700 px-3 py-1 rounded text-sm hover:bg-secondary-400"
                      >
                        إلغاء
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Export Important Content -->
              <button
                @click="handleExportImportantContent(selectedImportantColor)"
                class="neumorphic-button flex items-center gap-1 px-3 py-2 text-sm text-amber-600 hover:text-amber-700"
                title="تصدير المحتوى المهم"
                :disabled="!hasImportantContent"
              >
                <i class="fas fa-star"></i>
                <span class="hidden sm:inline">تصدير المهم</span>
              </button>

              <!-- Stop Important Content Mode -->
              <div class="relative">
                <button
                  @click="showStopImportantMenu = !showStopImportantMenu"
                  class="neumorphic-button flex items-center gap-1 px-3 py-2 text-sm text-secondary-600 hover:text-secondary-700"
                  title="إيقاف تحديد المحتوى المهم"
                >
                  <i class="fas fa-list"></i>
                  <span class="hidden sm:inline">إيقاف</span>
                </button>

                <!-- Stop Important Menu -->
                <div v-if="showStopImportantMenu" class="absolute top-full left-0 mt-2 z-50">
                  <div class="bg-white rounded-lg shadow-xl border border-secondary-200 p-4 min-w-64">
                    <h4 class="font-medium text-secondary-800 mb-3">إيقاف تحديد المحتوى المهم</h4>

                    <div class="space-y-3">
                      <label class="flex items-center gap-2">
                        <input
                          type="checkbox"
                          @change="handleStopImportantMode(false)"
                          class="neumorphic-checkbox"
                        />
                        <span class="text-sm">إيقاف مع الحفاظ على التنسيق</span>
                      </label>

                      <label class="flex items-center gap-2">
                        <input
                          type="checkbox"
                          @change="handleStopImportantMode(true)"
                          class="neumorphic-checkbox"
                        />
                        <span class="text-sm">إيقاف مع استعادة التنسيق الأصلي</span>
                      </label>
                    </div>

                    <button
                      @click="showStopImportantMenu = false"
                      class="mt-3 w-full bg-secondary-300 text-secondary-700 px-3 py-1 rounded text-sm hover:bg-secondary-400"
                    >
                      إغلاق
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Export/Import Actions -->
            <div class="flex items-center gap-2">
              <!-- Export Menu -->
              <div class="relative">
                <button
                  @click="showExportMenu = !showExportMenu"
                  class="neumorphic-button flex items-center gap-1 px-3 py-2 text-sm text-primary-600 hover:text-primary-700"
                  title="تصدير البيانات"
                >
                  <i class="fas fa-download"></i>
                  <span class="hidden sm:inline">تصدير</span>
                </button>

                <!-- Export Menu -->
                <div v-if="showExportMenu" class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-secondary-200 z-50">
                  <div class="p-2">
                    <button
                      @click="exportData('csv')"
                      class="w-full text-right p-2 hover:bg-secondary-50 rounded flex items-center gap-2"
                    >
                      <i class="fas fa-file-csv text-success-600"></i>
                      <span>تصدير CSV</span>
                    </button>
                    <button
                      @click="exportData('html')"
                      class="w-full text-right p-2 hover:bg-secondary-50 rounded flex items-center gap-2"
                    >
                      <i class="fas fa-file-code text-info-600"></i>
                      <span>تصدير HTML</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Import Button -->
              <button
                @click="handleImportData"
                class="neumorphic-button flex items-center gap-1 px-3 py-2 text-sm text-info-600 hover:text-info-700"
                title="استيراد البيانات"
              >
                <i class="fas fa-upload"></i>
                <span class="hidden sm:inline">استيراد</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Database Toolbar -->
      <DatabaseToolbar
        :tab-data="activeTabData"
        @search="handleSearch"
        @filter="handleFilter"
        @toggle-column="handleToggleColumn"
        @save-format="handleSaveFormat"
        @apply-format="handleApplyFormat"
        @group-header-settings="handleGroupHeaderSettings"
        @add-row="handleAddRow"
        @delete-selected-rows="handleDeleteSelectedRows"
        @add-column="handleAddColumn"
      />

      <!-- Data Table -->
      <DatabaseTable
        ref="databaseTableRef"
        :tab-data="activeTabData"
        :search-query="searchQuery"
        :filters="filters"
        :important-mode="importantMode"
        :important-color="importantColor"
        @update-data="handleUpdateData"
        @update-column-settings="handleUpdateColumnSettings"
        @update-group-headers="handleUpdateGroupHeaders"
        @add-row="handleAddRow"
        @import-data="handleImportData"
      />
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <div class="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-table text-gray-400 text-2xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-gray-700 mb-2">لا توجد تبويبات</h3>
        <p class="text-gray-500 mb-4">ابدأ بإضافة تبويب جديد أو استيراد بيانات من Excel</p>
        <div class="flex items-center justify-center gap-3">
          <button
            @click="showAddTabModal = true"
            class="bg-white px-6 py-3 rounded-lg shadow-lg border border-gray-200 text-green-600 hover:text-green-700 hover:shadow-xl transition-all duration-300 flex items-center gap-2"
          >
            <i class="fas fa-plus"></i>
            إضافة تبويب
          </button>
          <button
            @click="showImportModal = true"
            class="bg-white px-6 py-3 rounded-lg shadow-lg border border-gray-200 text-blue-600 hover:text-blue-700 hover:shadow-xl transition-all duration-300 flex items-center gap-2"
          >
            <i class="fas fa-file-excel"></i>
            استيراد Excel
          </button>
        </div>
      </div>
    </div>

    <!-- Add Tab Modal -->
    <AddTabModal
      v-if="showAddTabModal"
      @close="showAddTabModal = false"
      @add-tab="handleAddTab"
    />

    <!-- Import Modal -->
    <ImportExcelModal
      v-if="showImportModal"
      :existing-tabs="databaseStore.tabs"
      @close="showImportModal = false"
      @import="handleImportExcel"
    />

    <!-- Search Memo Modal -->
    <SearchMemoModal
      ref="searchMemoModalRef"
      v-if="showSearchMemoModal"
      :initial-data="lastSearchData || undefined"
      @close="showSearchMemoModal = false"
      @start-search="handleStartSearch"
      @generate-memo="handleGenerateMemo"
      @export-results="handleExportResults"
      @reset="handleResetSearch"
    />

    <!-- Group Header Settings Modal -->
    <GroupHeaderModal
      v-if="showGroupHeaderModal"
      :settings="activeTabData?.groupHeaders || getDefaultGroupHeaders()"
      @close="showGroupHeaderModal = false"
      @save="handleSaveGroupHeaderSettings"
    />

    <!-- Loading Overlay with Progress Bar -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <ProgressBar
          :progress="loadingProgress.progress"
          :status="loadingProgress.status"
          :title="loadingProgress.title"
          :current-message="loadingProgress.message"
          :show-details="true"
          :details="loadingProgress.details"
          :error-message="loadingProgress.errorMessage"
          :success-message="loadingProgress.successMessage"
          @close="isLoading = false"
        />
      </div>
    </div>

    <!-- Success/Error Messages -->
    <Transition name="fade">
      <div v-if="message" :class="[
        'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50',
        messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
      ]">
        <div class="flex items-center gap-3">
          <i :class="messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
          <span>{{ message }}</span>
          <button @click="message = ''" class="mr-auto hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useDatabaseStore } from '@/stores/database'
import { usePermissions } from '@/composables/usePermissions'
import DatabaseToolbar from '@/components/database/DatabaseToolbar.vue'
import DatabaseTable from '@/components/database/DatabaseTable.vue'
import AddTabModal from '@/components/database/AddTabModal.vue'
import ImportExcelModal from '@/components/database/ImportExcelModal.vue'
import SearchMemoModal from '@/components/database/SearchMemoModal.vue'
import GroupHeaderModal from '@/components/database/GroupHeaderModal.vue'
import ProgressBar from '@/components/common/ProgressBar.vue'
import type { DatabaseTab, DatabaseColumn, DatabaseRow, GroupHeaderSettings } from '@/types/database'
import { generateId } from '@/utils/helpers'
import { generateBrandedHTMLDocument } from '@/utils/report-template'

// Store
const databaseStore = useDatabaseStore()

// Permissions
const { permissions, requirePermission } = usePermissions()

// Reactive data
const activeTab = ref<string>('')
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const showAddTabModal = ref(false)
const showImportModal = ref(false)
const showSearchMemoModal = ref(false)
const searchMemoModalRef = ref<any>(null)

// بيانات البحث المحفوظة
const lastSearchData = ref<{
  phoneNumber: string
  suspectName: string
  hasResults: boolean
  searchResults?: any
} | null>(null)
const showGroupHeaderModal = ref(false)
const isLoading = ref(false)
const loadingMessage = ref('')
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const databaseTableRef = ref()

// Important content state
const importantMode = ref(false)
const importantColor = ref('#ff6b35')
const isColorBrushActive = ref(false)
const showColorPicker = ref(false)
const selectedImportantColor = ref('#ff6b35')
const showStopImportantMenu = ref(false)
const showExportMenu = ref(false)

// Loading progress tracking
const loadingProgress = reactive({
  progress: 0,
  status: 'idle' as 'idle' | 'loading' | 'success' | 'error',
  title: 'جاري المعالجة...',
  message: 'يرجى الانتظار...',
  details: {
    currentOperation: '',
    processingSpeed: '',
    estimatedTime: ''
  },
  errorMessage: '',
  successMessage: ''
})

// Computed
const tabs = computed(() => databaseStore.tabs)
const activeTabData = computed(() =>
  tabs.value.find(tab => tab.id === activeTab.value)
)

const hasImportantContent = computed(() =>
  activeTabData.value?.rows.some(row => row.isImportant) || false
)

// Methods
function handleAddTab(tabData: Omit<DatabaseTab, 'id'>) {
  // فحص الصلاحيات
  if (!requirePermission('database', 'write', 'إضافة التبويبات')) {
    return
  }

  try {
    const newTab = databaseStore.addTab(tabData)
    activeTab.value = newTab.id
    showAddTabModal.value = false
    showMessage('تم إضافة التبويب بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في إضافة التبويب', 'error')
  }
}

function removeTab(tabId: string) {
  // فحص الصلاحيات
  if (!requirePermission('database', 'delete', 'حذف التبويبات')) {
    return
  }

  if (confirm('هل أنت متأكد من حذف هذا التبويب؟')) {
    try {
      databaseStore.removeTab(tabId)
      if (activeTab.value === tabId) {
        activeTab.value = tabs.value[0]?.id || ''
      }
      showMessage('تم حذف التبويب بنجاح', 'success')
    } catch (error) {
      showMessage('فشل في حذف التبويب', 'error')
    }
  }
}

async function handleImportExcel(data: { file: File, tabName?: string, tabId?: string }) {
  // فحص الصلاحيات
  if (!requirePermission('database', 'import', 'استيراد البيانات')) {
    return
  }

  try {
    // Initialize loading progress
    isLoading.value = true
    loadingProgress.status = 'loading'
    loadingProgress.progress = 0
    loadingProgress.title = 'استيراد ملف Excel'
    loadingProgress.message = 'بدء عملية الاستيراد...'
    loadingProgress.details.currentOperation = 'معالجة الملف'
    loadingProgress.errorMessage = ''
    loadingProgress.successMessage = ''

    // Simulate progress steps
    loadingProgress.progress = 20
    loadingProgress.message = 'جاري قراءة ملف Excel...'

    const result = await databaseStore.importExcelFile(data.file, data.tabName, data.tabId)

    loadingProgress.progress = 80
    loadingProgress.message = 'جاري حفظ البيانات...'

    if (result.success) {
      loadingProgress.progress = 100
      loadingProgress.status = 'success'
      loadingProgress.message = 'تم استيراد البيانات بنجاح'
      loadingProgress.successMessage = `تم استيراد ${result.rowsImported} صف بنجاح`

      // Show success for a moment
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Close modal and switch to new tab
      showImportModal.value = false
      activeTab.value = result.tabId!
      showMessage(`تم استيراد ${result.rowsImported} صف بنجاح`, 'success')
    } else {
      throw new Error(result.errors?.[0]?.message || 'فشل في استيراد البيانات')
    }
  } catch (error: any) {
    loadingProgress.status = 'error'
    loadingProgress.errorMessage = error?.message || 'حدث خطأ أثناء الاستيراد'
    loadingProgress.message = 'فشل في استيراد البيانات'

    // Show error for a moment
    setTimeout(() => {
      showMessage('فشل في استيراد البيانات', 'error')
      isLoading.value = false
    }, 2000)
  } finally {
    // Always reset loading state after operations complete
    if (loadingProgress.status === 'success') {
      // Reset after showing success
      setTimeout(() => {
        isLoading.value = false
      }, 500)
    } else if (loadingProgress.status !== 'error') {
      isLoading.value = false
    }
  }
}

function handleSearch(query: string) {
  searchQuery.value = query
}

function handleFilter(newFilters: Record<string, any>) {
  filters.value = newFilters
}

function handleToggleColumn(columnId: string) {
  if (activeTabData.value) {
    databaseStore.toggleColumnVisibility(activeTabData.value.id, columnId)
  }
}

function handleSaveFormat() {
  if (activeTabData.value) {
    try {
      databaseStore.saveTabFormat(activeTabData.value.id)
      showMessage('تم حفظ التنسيقات بنجاح', 'success')
    } catch (error) {
      showMessage('فشل في حفظ التنسيقات', 'error')
    }
  }
}

function handleApplyFormat() {
  if (activeTabData.value) {
    try {
      databaseStore.applyTabFormat(activeTabData.value.id)
      showMessage('تم تطبيق التنسيقات بنجاح', 'success')
    } catch (error) {
      showMessage('فشل في تطبيق التنسيقات', 'error')
    }
  }
}

async function handleExportData(
  format: 'csv' | 'html',
  options?: { searchQuery?: string, hasFilters?: boolean }
) {
  if (activeTabData.value) {
    try {
      // Initialize export progress
      isLoading.value = true
      loadingProgress.status = 'loading'
      loadingProgress.progress = 0
      loadingProgress.title = `تصدير البيانات (${format.toUpperCase()})`
      loadingProgress.details.currentOperation = 'تحضير البيانات'
      loadingProgress.errorMessage = ''
      loadingProgress.successMessage = ''

      if (options?.hasFilters) {
        loadingProgress.message = `جاري تصدير نتائج البحث...`
      } else {
        loadingProgress.message = `جاري تصدير جميع البيانات...`
      }

      loadingProgress.progress = 30
      loadingProgress.details.currentOperation = 'معالجة البيانات'

      // Get filtered rows from DatabaseTable component
      let exportOptions = undefined
      if (options?.hasFilters && databaseTableRef.value) {
        const filteredRows = databaseTableRef.value.getFilteredRows()
        exportOptions = {
          searchQuery: options.searchQuery,
          filteredRows: filteredRows
        }
      }

      loadingProgress.progress = 60
      loadingProgress.details.currentOperation = 'إنشاء الملف'

      await databaseStore.exportTabData(activeTabData.value.id, format, exportOptions)

      loadingProgress.progress = 100
      loadingProgress.status = 'success'
      loadingProgress.details.currentOperation = 'اكتمل'

      if (options?.hasFilters) {
        loadingProgress.successMessage = `تم تصدير نتائج البحث بنجاح`
        showMessage(`تم تصدير نتائج البحث بنجاح (${format.toUpperCase()})`, 'success')
      } else {
        loadingProgress.successMessage = `تم تصدير جميع البيانات بنجاح`
        showMessage(`تم تصدير جميع البيانات بنجاح (${format.toUpperCase()})`, 'success')
      }

      // Show success for a moment then hide progress bar
      setTimeout(() => {
        isLoading.value = false
      }, 1500)

    } catch (error: any) {
      loadingProgress.status = 'error'
      loadingProgress.errorMessage = error?.message || 'حدث خطأ أثناء التصدير'
      loadingProgress.message = 'فشل في تصدير البيانات'

      setTimeout(() => {
        showMessage('فشل في تصدير البيانات', 'error')
        isLoading.value = false
      }, 2000)
    } finally {
      // Ensure loading state is reset if not already handled
      if (loadingProgress.status === 'error') {
        setTimeout(() => {
          isLoading.value = false
        }, 2000)
      }
    }
  }
}

function handleImportData() {
  console.log('🔍 handleImportData تم استدعاؤها - فتح نافذة الاستيراد')
  console.trace('📍 مسار الاستدعاء:')
  showImportModal.value = true
}

function handleUpdateData(data: { rows: DatabaseRow[], columns: DatabaseColumn[] }) {
  // تم تعطيل هذه الدالة لمنع تضاعف البيانات
  // البيانات يتم تحديثها مباشرة في المتجر من خلال العمليات المحددة
  console.log('⚠️ handleUpdateData تم تجاهلها لمنع تضاعف البيانات')

  // if (activeTabData.value) {
  //   databaseStore.updateTabData(activeTabData.value.id, data.rows, data.columns)
  // }
}

function handleUpdateColumnSettings(columnId: string, settings: any) {
  if (activeTabData.value) {
    databaseStore.updateColumnSettings(activeTabData.value.id, columnId, settings)
  }
}

function handleUpdateGroupHeaders(settings: any) {
  if (activeTabData.value) {
    databaseStore.updateGroupHeaderSettings(activeTabData.value.id, settings)
  }
}

function handleGroupHeaderSettings() {
  showGroupHeaderModal.value = true
}

function handleSaveGroupHeaderSettings(settings: GroupHeaderSettings) {
  if (activeTabData.value) {
    databaseStore.updateGroupHeaderSettings(activeTabData.value.id, settings)
    showGroupHeaderModal.value = false
    showMessage('تم حفظ إعدادات عناوين المجموعات بنجاح', 'success')
  }
}

function getDefaultGroupHeaders(): GroupHeaderSettings {
  return {
    enabled: true,
    identifier: 'سجل حركة',
    textColor: '#1e40af',
    backgroundColor: '#dbeafe',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'right',
    colSpan: true
  }
}

function handleAddRow() {
  // فحص الصلاحيات
  if (!requirePermission('database', 'write', 'إضافة الصفوف')) {
    return
  }

  if (activeTabData.value) {
    // Create empty row data based on visible columns
    const emptyRowData: Record<string, any> = {}
    activeTabData.value.columns.forEach(column => {
      emptyRowData[column.id] = ''
    })

    const newRow = databaseStore.addRow(activeTabData.value.id, emptyRowData)
    if (newRow) {
      showMessage('تم إضافة صف جديد بنجاح', 'success')
    } else {
      showMessage('فشل في إضافة الصف الجديد', 'error')
    }
  }
}

function handleDeleteSelectedRows() {
  // فحص الصلاحيات
  if (!requirePermission('database', 'delete', 'حذف الصفوف')) {
    return
  }

  if (activeTabData.value) {
    const deletedCount = databaseStore.removeSelectedRows(activeTabData.value.id)
    if (deletedCount > 0) {
      showMessage(`تم حذف ${deletedCount} صف بنجاح`, 'success')
    } else {
      showMessage('لم يتم حذف أي صف', 'error')
    }
  }
}

function handleAddColumn(columnName: string) {
  if (activeTabData.value) {
    const newColumn = databaseStore.addColumn(activeTabData.value.id, {
      name: columnName,
      type: 'text',
      width: 150,
      isVisible: true,
      isResizable: true,
      order: activeTabData.value.columns.length + 1,
      formatting: {
        // Header formatting
        headerTextColor: '#334155',
        headerBackgroundColor: '#f1f5f9',
        headerFontSize: 14,
        headerFontWeight: 'bold',
        headerTextAlign: 'center',

        // Cell formatting
        cellTextColor: '#475569',
        cellBackgroundColor: 'transparent',
        cellFontSize: 14,
        cellFontWeight: 'normal',
        cellTextAlign: 'right',
        cellTextWrap: false,
        cellFitContent: false,

        // Border formatting
        borderColor: '#e2e8f0',
        borderWidth: 1,
        borderStyle: 'solid',

        // Sorting
        sortable: false,

        // Number formatting
        numberFormat: {
          decimals: 0,
          thousandsSeparator: false
        },

        // Date formatting
        dateFormat: 'YYYY-MM-DD'
      }
    })

    if (newColumn) {
      showMessage('تم إضافة العمود الجديد بنجاح', 'success')
    } else {
      showMessage('فشل في إضافة العمود الجديد', 'error')
    }
  }
}

function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

// Important content functions
function toggleColorBrush() {
  if (isColorBrushActive.value) {
    isColorBrushActive.value = false
    showColorPicker.value = false
    handleToggleImportantMode(false)
  } else {
    showColorPicker.value = true
  }
}

function applyColorBrush() {
  isColorBrushActive.value = true
  showColorPicker.value = false
  handleToggleImportantMode(true, selectedImportantColor.value)
}

function handleToggleImportantMode(active: boolean, color?: string) {
  importantMode.value = active
  if (color) {
    importantColor.value = color
  }

  if (active) {
    showMessage('تم تفعيل وضع تحديد المحتوى المهم - اضغط على أي خلية لتمييز الصف', 'success')
  } else {
    showMessage('تم إيقاف وضع تحديد المحتوى المهم', 'success')
  }
}

async function handleExportImportantContent(_color: string) {
  if (activeTabData.value) {
    try {
      isLoading.value = true
      loadingMessage.value = 'جاري تصدير المحتوى المهم...'

      // Get important rows and their group headers
      const importantRows = activeTabData.value.rows.filter(row => {
        if (row.isImportant) return true

        // Check if this is a group header for important content
        if (row.isGroupHeader) {
          // Find if any rows in this group are important
          const groupRows = getRowsInGroup(row, activeTabData.value!.rows)
          return groupRows.some(r => r.isImportant)
        }

        return false
      })

      if (importantRows.length === 0) {
        showMessage('لا يوجد محتوى مهم للتصدير', 'error')
        return
      }

      // Export important content as HTML
      await databaseStore.exportTabData(activeTabData.value.id, 'html', {
        filteredRows: importantRows,
        searchQuery: 'محتوى يرجح انه ذو محتوى مهم'
      })

      showMessage(`تم تصدير ${importantRows.filter(r => !r.isGroupHeader).length} صف من المحتوى المهم`, 'success')
    } catch (error) {
      showMessage('فشل في تصدير المحتوى المهم', 'error')
    } finally {
      isLoading.value = false
    }
  }
}

function handleStopImportantMode(restoreOriginal: boolean) {
  importantMode.value = false
  isColorBrushActive.value = false
  showStopImportantMenu.value = false

  if (activeTabData.value) {
    if (restoreOriginal) {
      // Restore original colors
      activeTabData.value.rows.forEach(row => {
        if (row.isImportant) {
          row.isImportant = false
          row.importantColor = undefined
          row.originalTextColor = undefined
        }
      })
      showMessage('تم إيقاف وضع المحتوى المهم واستعادة التنسيق الأصلي', 'success')
    } else {
      // Keep the formatting but disable the mode
      showMessage('تم إيقاف وضع المحتوى المهم مع الحفاظ على التنسيق', 'success')
    }
  }
}

// Export/Import functions
function exportData(format: 'csv' | 'html') {
  // Check if there's an active search/filter
  const hasActiveSearch = searchQuery.value && searchQuery.value.trim() !== ''

  handleExportData(format, {
    searchQuery: hasActiveSearch ? searchQuery.value : undefined,
    hasFilters: hasActiveSearch ? true : undefined
  })
  showExportMenu.value = false
}

// Helper function to get rows in a group
function getRowsInGroup(groupHeader: DatabaseRow, allRows: DatabaseRow[]): DatabaseRow[] {
  const groupRows: DatabaseRow[] = []
  const headerIndex = allRows.indexOf(groupHeader)

  for (let i = headerIndex + 1; i < allRows.length; i++) {
    const row = allRows[i]
    if (row.isGroupHeader) break // Next group started
    groupRows.push(row)
  }

  return groupRows
}

// دالة للتحقق من التبويبات الموجودة
function debugExistingTabs() {
  console.log('🔍 التحقق من التبويبات الموجودة:')
  console.log('📊 عدد التبويبات:', tabs.value.length)

  // التحقق من localStorage
  const savedTabs = localStorage.getItem('database_tabs')
  if (savedTabs) {
    try {
      const parsedTabs = JSON.parse(savedTabs)
      console.log('💾 التبويبات في localStorage:', parsedTabs.length)
      parsedTabs.forEach((tab: any, index: number) => {
        console.log(`   ${index + 1}. "${tab.name}" (${tab.rows?.length || 0} صفوف)`)
      })
    } catch (error) {
      console.error('❌ خطأ في قراءة localStorage:', error)
    }
  } else {
    console.log('💾 لا توجد بيانات في localStorage')
  }

  tabs.value.forEach((tab, index) => {
    console.log(`📋 التبويب ${index + 1}:`)
    console.log(`   الاسم: "${tab.name}"`)
    console.log(`   الأيقونة: ${tab.icon}`)
    console.log(`   عدد الأعمدة: ${tab.columns.length}`)
    console.log(`   عدد الصفوف: ${tab.rows.length}`)

    // طباعة أسماء الأعمدة
    console.log('   الأعمدة:')
    tab.columns.forEach((col, colIndex) => {
      console.log(`     ${colIndex + 1}. "${col.name}" (${col.type})`)
    })

    // طباعة عينة من البيانات
    if (tab.rows.length > 0) {
      console.log('   عينة من البيانات:')
      tab.rows.slice(0, 3).forEach((row, rowIndex) => {
        console.log(`     الصف ${rowIndex + 1}:`, row.data)
      })
    }
    console.log('---')
  })
}

// Search Memo Functions

// دالة التحقق من وجود التبويبات المؤقتة السابقة
function checkExistingTempTabs() {
  const tempTabNames = [
    'البيانات المطابقة لسجلات الرسائل',
    'البيانات المطابقة لسجلات المكالمات',
    'البيانات المطابقة لجهات الاتصال'
  ]

  const existingTabs: any[] = []

  tempTabNames.forEach(name => {
    const tab = databaseStore.getTabByName(name)
    if (tab) {
      existingTabs.push(tab)
    }
  })

  return existingTabs
}

async function handleStartSearch(data: { phoneNumber: string, suspectName: string }) {
  console.log('🔍 بدء البحث والمطابقة:', data)

  // حفظ بيانات البحث
  lastSearchData.value = {
    phoneNumber: data.phoneNumber,
    suspectName: data.suspectName,
    hasResults: false
  }

  // أولاً: التحقق من التبويبات الموجودة
  debugExistingTabs()

  if (!searchMemoModalRef.value) return

  try {
    // التحقق من وجود التبويبات المؤقتة السابقة
    const existingTempTabs = checkExistingTempTabs()

    if (existingTempTabs.length > 0) {
      console.log('⚠️ تم العثور على تبويبات مؤقتة سابقة:', existingTempTabs.map(tab => tab.name))

      // عرض تأكيد للمستخدم
      const userConfirmed = confirm(
        `تم العثور على ${existingTempTabs.length} تبويب مؤقت من عملية بحث سابقة:\n\n` +
        existingTempTabs.map(tab => `• ${tab.name}`).join('\n') + '\n\n' +
        'سيتم حذف البيانات المؤقتة السابقة والبدء بعملية بحث جديدة.\n\n' +
        'هل تريد المتابعة؟'
      )

      if (!userConfirmed) {
        console.log('❌ المستخدم ألغى العملية')
        return
      }

      // حذف التبويبات المؤقتة السابقة
      console.log('🗑️ حذف التبويبات المؤقتة السابقة...')
      existingTempTabs.forEach(tab => {
        databaseStore.removeTab(tab.id)
        console.log(`✅ تم حذف التبويب: ${tab.name}`)
      })

      // تحديث التبويب النشط إذا كان أحد التبويبات المحذوفة
      if (existingTempTabs.some(tab => tab.id === activeTab.value)) {
        activeTab.value = tabs.value[0]?.id || ''
      }

      showMessage(`تم حذف ${existingTempTabs.length} تبويب مؤقت سابق`, 'success')
    }
    // أسماء التبويبات المطلوب البحث فيها (مع مراعاة الأسماء المختلفة)
    const targetTabNames = [
      'سجل حركة الرسائل النصية المستخرجة من هواتف المتهمين',
      'سجل حركة الاتصالات المستخرجة من هواتف المتهمين',
      'سجل حركةجهات الاتصال المستخرجة من هواتف المتهمين'
    ]

    // البحث عن التبويبات في قاعدة البيانات
    console.log('🔍 البحث عن التبويبات المطلوبة:')
    targetTabNames.forEach(name => {
      console.log(`   البحث عن: "${name}"`)
    })

    const searchTabs = targetTabNames.map(name => {
      const foundTab = databaseStore.getTabByName(name)
      console.log(`   النتيجة لـ "${name}":`, foundTab ? 'موجود ✅' : 'غير موجود ❌')
      return foundTab
    }).filter(tab => tab !== undefined)

    console.log(`📊 تم العثور على ${searchTabs.length} من أصل ${targetTabNames.length} تبويبات`)

    if (searchTabs.length === 0) {
      console.error('❌ لم يتم العثور على أي من التبويبات المطلوبة')
      searchMemoModalRef.value.setSearchError('لم يتم العثور على التبويبات المطلوبة للبحث')
      return
    }

    // بدء عملية البحث
    searchMemoModalRef.value.setSearchProgress(0)
    searchMemoModalRef.value.updateSearchMessage('بدء البحث في التبويبات...')

    const searchResults = {
      smsCount: 0,
      callsCount: 0,
      contactsCount: 0,
      totalMatches: 0,
      details: [] as any[]
    }

    // البحث في كل تبويب
    for (let i = 0; i < searchTabs.length; i++) {
      const tab = searchTabs[i]
      const progress = Math.round(((i + 1) / searchTabs.length) * 100)

      searchMemoModalRef.value.setSearchProgress(progress)
      searchMemoModalRef.value.updateSearchMessage(`البحث في ${tab.name}...`)

      // تأخير قصير لإظهار التقدم
      await new Promise(resolve => setTimeout(resolve, 500))

      // البحث في التبويب
      const matches = await searchInTab(tab, data.phoneNumber)

      // تحديث النتائج حسب نوع التبويب
      if (tab.name.includes('الرسائل النصية')) {
        searchResults.smsCount = matches.length
      } else if (tab.name.includes('الاتصالات')) {
        searchResults.callsCount = matches.length
      } else if (tab.name.includes('جهات الاتصال')) {
        searchResults.contactsCount = matches.length
      }

      searchResults.details.push({
        tabName: tab.name,
        matches: matches
      })
    }

    searchResults.totalMatches = searchResults.smsCount + searchResults.callsCount + searchResults.contactsCount

    // إنشاء التبويبات المؤقتة للنتائج
    await createTemporaryTabs(searchResults.details, data)

    // عرض النتائج
    searchMemoModalRef.value.setSearchResults(searchResults)

    // تحديث حالة البيانات المحفوظة مع النتائج الحقيقية
    if (lastSearchData.value) {
      lastSearchData.value.hasResults = true
      lastSearchData.value.searchResults = searchResults
    }

  } catch (error) {
    console.error('خطأ في البحث:', error)
    searchMemoModalRef.value.setSearchError('حدث خطأ أثناء عملية البحث')

    // إعادة تعيين حالة النتائج في حالة الخطأ
    if (lastSearchData.value) {
      lastSearchData.value.hasResults = false
    }
  }
}

// دالة البحث في تبويب واحد
async function searchInTab(tab: any, phoneNumber: string) {
  console.log(`🔍 البحث في التبويب: "${tab.name}"`)
  console.log(`📱 البحث عن الرقم: "${phoneNumber}"`)

  const matches: any[] = []

  // طباعة أسماء جميع الأعمدة
  console.log('📋 الأعمدة الموجودة في التبويب:')
  tab.columns.forEach((col: any, index: number) => {
    console.log(`   ${index + 1}. "${col.name}"`)
  })

  // البحث عن عمود "رقم_جهة_الاتصال"
  const phoneColumn = tab.columns.find((col: any) =>
    col.name === 'رقم_جهة_الاتصال' ||
    col.name.includes('رقم') && col.name.includes('اتصال')
  )

  if (!phoneColumn) {
    console.warn(`❌ لم يتم العثور على عمود رقم_جهة_الاتصال في ${tab.name}`)
    console.warn('💡 الأعمدة المتاحة:', tab.columns.map((c: any) => c.name))
    return matches
  }

  console.log(`✅ تم العثور على العمود: "${phoneColumn.name}"`)

  // البحث في الصفوف
  console.log(`📊 عدد الصفوف في التبويب: ${tab.rows.length}`)

  for (let i = 0; i < tab.rows.length; i++) {
    const row = tab.rows[i]

    // محاولة الوصول للبيانات بطرق مختلفة
    let cellValue = row.data[phoneColumn.name]

    // إذا كانت البيانات undefined، جرب الوصول المباشر
    if (cellValue === undefined) {
      // جرب جميع الطرق الممكنة للوصول للبيانات
      const rowData = row.data || row
      cellValue = rowData[phoneColumn.name] ||
                  rowData[phoneColumn.id] ||
                  rowData['رقم_جهة_الاتصال']
    }

    // طباعة تفاصيل البحث للصفوف الأولى
    if (i < 5) {
      console.log(`   الصف ${i + 1}: "${cellValue}" (البحث عن: "${phoneNumber}")`)
      console.log(`   بيانات الصف الكاملة:`, row.data)
      console.log(`   مفاتيح البيانات:`, Object.keys(row.data || {}))
    }

    // تحقق من التطابق
    if (cellValue && cellValue.toString().includes(phoneNumber)) {
      console.log(`✅ تطابق في الصف ${i + 1}: "${cellValue}"`)

      // البحث عن عنوان المجموعة
      const groupHeader = findGroupHeader(tab.rows, i)

      matches.push({
        row: row,
        groupHeader: groupHeader,
        rowIndex: i
      })
    }
  }

  console.log(`📈 تم العثور على ${matches.length} مطابقة في "${tab.name}"`)
  return matches
}

// دالة البحث عن عنوان المجموعة
function findGroupHeader(rows: any[], currentIndex: number) {
  // البحث للخلف عن أقرب عنوان مجموعة
  for (let i = currentIndex - 1; i >= 0; i--) {
    const row = rows[i]
    if (row.isGroupHeader) {
      return row
    }
  }
  return null
}

// دالة إنشاء التبويبات المؤقتة
async function createTemporaryTabs(searchDetails: any[], searchData: any) {
  const tempTabNames = [
    'البيانات المطابقة لسجلات الرسائل',
    'البيانات المطابقة لسجلات المكالمات',
    'البيانات المطابقة لجهات الاتصال'
  ]

  // إنشاء جميع التبويبات المؤقتة الثلاثة
  for (let i = 0; i < tempTabNames.length; i++) {
    const tempTabName = tempTabNames[i]

    // حذف التبويب المؤقت إذا كان موجوداً
    const existingTab = databaseStore.getTabByName(tempTabName)
    if (existingTab) {
      databaseStore.removeTab(existingTab.id)
    }

    // البحث عن التفاصيل المطابقة لهذا التبويب
    const detail = searchDetails.find(d => {
      if (i === 0) return d.tabName.includes('الرسائل النصية')
      if (i === 1) return d.tabName.includes('الاتصالات')
      if (i === 2) return d.tabName.includes('جهات الاتصال')
      return false
    })

    if (detail && detail.matches.length > 0) {
      await createTempTabWithMatches(detail, tempTabName, searchData)
    } else {
      // إنشاء تبويب فارغ مع اسم التبويب الأصلي المناسب
      const originalTabName = i === 0 ? 'سجل حركة الرسائل النصية المستخرجة من هواتف المتهمين' :
                             i === 1 ? 'سجل حركة الاتصالات المستخرجة من هواتف المتهمين' :
                             'سجل حركةجهات الاتصال المستخرجة من هواتف المتهمين'
      await createEmptyTempTab(tempTabName, originalTabName)
    }
  }
}

// دالة إنشاء تبويب مؤقت مع النتائج
async function createTempTabWithMatches(detail: any, tempTabName: string, _searchData: any) {
  const originalTab = databaseStore.getTabByName(detail.tabName)
  if (!originalTab) return

  // نسخ بنية التبويب الأصلي
  const tempTab = databaseStore.addTab({
    name: tempTabName,
    icon: 'fas fa-search',
    columns: JSON.parse(JSON.stringify(originalTab.columns)),
    rows: [],
    settings: JSON.parse(JSON.stringify(originalTab.settings)),
    groupHeaders: JSON.parse(JSON.stringify(originalTab.groupHeaders)),
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // إضافة الصفوف المطابقة مع عناوين المجموعات
  const addedGroupHeaders = new Set()

  for (const match of detail.matches) {
    // إضافة عنوان المجموعة إذا لم يتم إضافته من قبل
    if (match.groupHeader && !addedGroupHeaders.has(match.groupHeader.id)) {
      databaseStore.addRow(tempTab.id, match.groupHeader.data)
      const addedRow = tempTab.rows[tempTab.rows.length - 1]
      addedRow.isGroupHeader = true
      addedRow.groupHeaderText = match.groupHeader.groupHeaderText
      addedGroupHeaders.add(match.groupHeader.id)
    }

    // إضافة الصف المطابق
    databaseStore.addRow(tempTab.id, match.row.data)
  }
}

// دالة إنشاء تبويب مؤقت فارغ
async function createEmptyTempTab(tempTabName: string, originalTabName: string) {
  const originalTab = databaseStore.getTabByName(originalTabName)
  if (!originalTab) return

  const tempTab = databaseStore.addTab({
    name: tempTabName,
    icon: 'fas fa-search',
    columns: JSON.parse(JSON.stringify(originalTab.columns)),
    rows: [],
    settings: JSON.parse(JSON.stringify(originalTab.settings)),
    groupHeaders: JSON.parse(JSON.stringify(originalTab.groupHeaders)),
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // إضافة رسالة "لا توجد سجلات مطابقة"
  const emptyMessage: Record<string, any> = {}
  tempTab.columns.forEach((col: any, index: number) => {
    if (index === 0) {
      emptyMessage[col.name] = 'لا توجد سجلات مطابقة لهذا الرقم في هذا القسم.'
    } else {
      emptyMessage[col.name] = ''
    }
  })

  databaseStore.addRow(tempTab.id, emptyMessage)
}

// دالة تحليل تبويب الرسائل النصية (الجزء الأول)
async function analyzeMessagesTab(data: { phoneNumber: string, suspectName: string }) {
  console.log('📱 بدء تحليل تبويب الرسائل النصية')

  // طباعة جميع التبويبات للتحقق
  console.log('🔍 البحث عن تبويب الرسائل المؤقت...')
  console.log('📋 التبويبات المتاحة:')
  tabs.value.forEach((tab, index) => {
    console.log(`   ${index + 1}. "${tab.name}" (${tab.rows.length} صفوف)`)
  })

  // العثور على تبويب الرسائل المؤقت
  let messagesTab = databaseStore.getTabByName('البيانات المطابقة لسجلات الرسائل')

  if (!messagesTab) {
    // جرب أسماء مختلفة محتملة
    const possibleNames = [
      'البيانات المطابقة لسجلات الرسائل',
      'البيانات المطابقة لسجلات الرسائل ',
      ' البيانات المطابقة لسجلات الرسائل'
    ]

    for (const name of possibleNames) {
      messagesTab = databaseStore.getTabByName(name)
      if (messagesTab) {
        console.log(`✅ تم العثور على التبويب باسم: "${name}"`)
        break
      }
    }
  }

  if (!messagesTab) {
    console.error('❌ لم يتم العثور على تبويب الرسائل المؤقت')
    throw new Error('لم يتم العثور على تبويب الرسائل المؤقت')
  }

  console.log(`📊 تم العثور على تبويب الرسائل: "${messagesTab.name}"`)
  console.log(`📊 عدد الصفوف: ${messagesTab.rows.length}`)
  console.log(`📊 الأعمدة:`, messagesTab.columns.map(c => c.name))

  // تحميل قائمة الكلمات المهمة
  const importantWords = await loadImportantWords()

  // حساب الإحصائيات العامة
  const generalStats = calculateGeneralMessageStats(messagesTab, data.phoneNumber)

  // تحليل المجموعات
  const groupsAnalysis = await analyzeMessageGroups(messagesTab, data.phoneNumber, importantWords)

  return {
    generalStats,
    groupsAnalysis,
    importantWords: importantWords
  }
}

// دالة تحليل تبويب المكالمات (الجزء الثاني)
async function analyzeCallsTab(data: { phoneNumber: string, suspectName: string }) {
  console.log('📞 بدء تحليل تبويب المكالمات')

  // العثور على تبويب المكالمات المؤقت
  let callsTab = databaseStore.getTabByName('البيانات المطابقة لسجلات المكالمات')

  if (!callsTab) {
    console.error('❌ لم يتم العثور على تبويب المكالمات المؤقت')
    return {
      generalStats: { totalCalls: 0, totalSuspects: 0 },
      groupsAnalysis: [],
      hasData: false
    }
  }

  console.log(`📊 تم العثور على تبويب المكالمات: "${callsTab.name}"`)
  console.log(`📊 عدد الصفوف: ${callsTab.rows.length}`)
  console.log(`📊 الأعمدة:`, callsTab.columns.map(c => c.name))

  // حساب الإحصائيات العامة للمكالمات
  const generalStats = calculateGeneralCallStats(callsTab, data.phoneNumber)

  // تحليل مجموعات المكالمات
  const groupsAnalysis = await analyzeCallGroups(callsTab, data.phoneNumber)

  return {
    generalStats,
    groupsAnalysis,
    hasData: generalStats.totalCalls > 0
  }
}

// دالة حساب الإحصائيات العامة للمكالمات
function calculateGeneralCallStats(callsTab: any, phoneNumber: string) {
  console.log('📊 حساب الإحصائيات العامة للمكالمات')

  // البحث عن العمود الصحيح
  const phoneCol = callsTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = callsTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')

  console.log(`🔍 العمود المستخدم للهاتف: ${phoneCol ? phoneCol.name + ' (' + phoneCol.id + ')' : 'غير موجود'}`)
  console.log(`🔍 العمود المستخدم للمتهم: ${suspectCol ? suspectCol.name + ' (' + suspectCol.id + ')' : 'غير موجود'}`)

  let totalCalls = 0
  const uniqueSuspects = new Set<string>()

  // حساب عدد المكالمات المطابقة والمتهمين
  callsTab.rows.forEach((row: any, index: number) => {
    if (phoneCol) {
      const cellValue = row.data[phoneCol.id]

      // طباعة تفاصيل أول 3 صفوف للتشخيص
      if (index < 3) {
        console.log(`   الصف ${index + 1}: الهاتف="${cellValue}", البحث عن="${phoneNumber}"`)
      }

      if (cellValue && cellValue.toString() === phoneNumber) {
        totalCalls++

        // إضافة اسم المتهم إذا وُجد
        if (suspectCol) {
          const suspectName = row.data[suspectCol.id]
          if (suspectName && suspectName.trim()) {
            uniqueSuspects.add(suspectName.trim())
          }
        }
      }
    }
  })

  const stats = {
    totalCalls,
    totalSuspects: uniqueSuspects.size,
    suspectNames: Array.from(uniqueSuspects)
  }

  console.log('📈 إحصائيات المكالمات العامة:', stats)
  return stats
}

// دالة تحليل مجموعات المكالمات
async function analyzeCallGroups(callsTab: any, phoneNumber: string) {
  console.log('👥 بدء تحليل مجموعات المكالمات')

  const groupsAnalysis: any[] = []
  const rows = callsTab.rows

  // البحث عن الأعمدة المطلوبة
  const phoneCol = callsTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = callsTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')
  const ownerCol = callsTab.columns.find((col: any) => col.name === 'تبعية_البيانات')
  const typeCol = callsTab.columns.find((col: any) => col.name === 'النوع')
  const savedAsCol = callsTab.columns.find((col: any) => col.name === 'محفوظ_بالإسم')
  const durationCol = callsTab.columns.find((col: any) => col.name === 'المدة')

  if (!phoneCol || !suspectCol) {
    console.error('❌ لم يتم العثور على الأعمدة المطلوبة في تبويب المكالمات')
    return groupsAnalysis
  }

  // تجميع المكالمات حسب المتهم (اسم_صاحب_البيانات)
  const groupedBySuspect = new Map<string, any[]>()

  rows.forEach((row: any) => {
    const cellPhone = row.data[phoneCol.id]
    const suspectName = row.data[suspectCol.id]

    if (cellPhone && cellPhone.toString() === phoneNumber && suspectName && suspectName.trim()) {
      const key = suspectName.trim()
      if (!groupedBySuspect.has(key)) {
        groupedBySuspect.set(key, [])
      }
      groupedBySuspect.get(key)!.push(row)
    }
  })

  console.log(`📊 تم العثور على ${groupedBySuspect.size} مجموعة مكالمات (متهم)`)

  // تحليل كل مجموعة (متهم)
  let groupIndex = 0
  for (const [suspectName, suspectRows] of groupedBySuspect) {
    groupIndex++
    console.log(`🔍 تحليل مجموعة المكالمات ${groupIndex}: ${suspectName} (${suspectRows.length} مكالمات)`)

    // الحصول على بيانات المجموعة
    const firstRow = suspectRows[0]
    const ownerNumber = ownerCol ? firstRow.data[ownerCol.id] : 'غير محدد'
    const savedAs = savedAsCol ? firstRow.data[savedAsCol.id] : 'غير محدد'

    // حساب المكالمات حسب النوع
    let missedCount = 0
    let outgoingCount = 0
    let incomingCount = 0

    // حساب مدة المكالمات
    let outgoingDuration = 0
    let incomingDuration = 0

    if (typeCol) {
      suspectRows.forEach(row => {
        const callType = row.data[typeCol.id]
        const duration = durationCol ? (parseFloat(row.data[durationCol.id]) || 0) : 0

        if (callType === 'مفقودة') {
          missedCount++
        } else if (callType === 'صادرة') {
          outgoingCount++
          outgoingDuration += duration
        } else if (callType === 'واردة') {
          incomingCount++
          incomingDuration += duration
        }
      })
    }

    // تحويل المدة من ثوانٍ إلى دقائق
    const outgoingMinutes = Math.round((outgoingDuration / 60) * 10) / 10
    const incomingMinutes = Math.round((incomingDuration / 60) * 10) / 10

    groupsAnalysis.push({
      groupIndex,
      suspectName,
      ownerNumber,
      savedAs,
      missedCount,
      outgoingCount,
      incomingCount,
      outgoingMinutes,
      incomingMinutes,
      totalCalls: suspectRows.length
    })

    console.log(`📝 تم تحليل مجموعة المكالمات ${groupIndex}`)
  }

  console.log(`📊 تم تحليل ${groupsAnalysis.length} مجموعة مكالمات`)
  return groupsAnalysis
}

// دالة تحليل تبويب جهات الاتصال (الجزء الثالث)
async function analyzeContactsTab(data: { phoneNumber: string, suspectName: string }) {
  console.log('📇 بدء تحليل تبويب جهات الاتصال')

  // العثور على تبويب جهات الاتصال المؤقت
  let contactsTab = databaseStore.getTabByName('البيانات المطابقة لجهات الاتصال')

  if (!contactsTab) {
    console.error('❌ لم يتم العثور على تبويب جهات الاتصال المؤقت')
    return {
      generalStats: { totalContacts: 0, totalSuspects: 0 },
      groupsAnalysis: [],
      hasData: false
    }
  }

  console.log(`📊 تم العثور على تبويب جهات الاتصال: "${contactsTab.name}"`)
  console.log(`📊 عدد الصفوف: ${contactsTab.rows.length}`)
  console.log(`📊 الأعمدة:`, contactsTab.columns.map(c => c.name))

  // حساب الإحصائيات العامة لجهات الاتصال
  const generalStats = calculateGeneralContactStats(contactsTab, data.phoneNumber)

  // تحليل مجموعات جهات الاتصال
  const groupsAnalysis = await analyzeContactGroups(contactsTab, data.phoneNumber)

  return {
    generalStats,
    groupsAnalysis,
    hasData: generalStats.totalContacts > 0
  }
}

// دالة حساب الإحصائيات العامة لجهات الاتصال
function calculateGeneralContactStats(contactsTab: any, phoneNumber: string) {
  console.log('📊 حساب الإحصائيات العامة لجهات الاتصال')

  // البحث عن العمود الصحيح
  const phoneCol = contactsTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = contactsTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')

  console.log(`🔍 العمود المستخدم للهاتف: ${phoneCol ? phoneCol.name + ' (' + phoneCol.id + ')' : 'غير موجود'}`)
  console.log(`🔍 العمود المستخدم للمتهم: ${suspectCol ? suspectCol.name + ' (' + suspectCol.id + ')' : 'غير موجود'}`)

  let totalContacts = 0
  const uniqueSuspects = new Set<string>()

  // حساب عدد جهات الاتصال المطابقة والمتهمين
  contactsTab.rows.forEach((row: any, index: number) => {
    if (phoneCol) {
      const cellValue = row.data[phoneCol.id]

      // طباعة تفاصيل أول 3 صفوف للتشخيص
      if (index < 3) {
        console.log(`   الصف ${index + 1}: الهاتف="${cellValue}", البحث عن="${phoneNumber}"`)
      }

      if (cellValue && cellValue.toString() === phoneNumber) {
        totalContacts++

        // إضافة اسم المتهم إذا وُجد
        if (suspectCol) {
          const suspectName = row.data[suspectCol.id]
          if (suspectName && suspectName.trim()) {
            uniqueSuspects.add(suspectName.trim())
          }
        }
      }
    }
  })

  const stats = {
    totalContacts,
    totalSuspects: uniqueSuspects.size,
    suspectNames: Array.from(uniqueSuspects)
  }

  console.log('📈 إحصائيات جهات الاتصال العامة:', stats)
  return stats
}

// دالة تحليل مجموعات جهات الاتصال
async function analyzeContactGroups(contactsTab: any, phoneNumber: string) {
  console.log('👥 بدء تحليل مجموعات جهات الاتصال')

  const groupsAnalysis: any[] = []
  const rows = contactsTab.rows

  // البحث عن الأعمدة المطلوبة
  const phoneCol = contactsTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = contactsTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')
  const savedAsCol = contactsTab.columns.find((col: any) => col.name === 'محفوظ_بالإسم')

  if (!phoneCol || !suspectCol) {
    console.error('❌ لم يتم العثور على الأعمدة المطلوبة في تبويب جهات الاتصال')
    return groupsAnalysis
  }

  // تجميع جهات الاتصال حسب المتهم (اسم_صاحب_البيانات)
  const groupedBySuspect = new Map<string, any[]>()

  rows.forEach((row: any) => {
    const cellPhone = row.data[phoneCol.id]
    const suspectName = row.data[suspectCol.id]

    if (cellPhone && cellPhone.toString() === phoneNumber && suspectName && suspectName.trim()) {
      const key = suspectName.trim()
      if (!groupedBySuspect.has(key)) {
        groupedBySuspect.set(key, [])
      }
      groupedBySuspect.get(key)!.push(row)
    }
  })

  console.log(`📊 تم العثور على ${groupedBySuspect.size} مجموعة جهات اتصال (متهم)`)

  // تحليل كل مجموعة (متهم)
  let groupIndex = 0
  for (const [suspectName, suspectRows] of groupedBySuspect) {
    groupIndex++
    console.log(`🔍 تحليل مجموعة جهات الاتصال ${groupIndex}: ${suspectName} (${suspectRows.length} جهات اتصال)`)

    // الحصول على بيانات المجموعة
    const firstRow = suspectRows[0]
    const savedAs = savedAsCol ? firstRow.data[savedAsCol.id] : 'غير محدد'

    groupsAnalysis.push({
      groupIndex,
      suspectName,
      savedAs,
      totalContacts: suspectRows.length
    })

    console.log(`📝 تم تحليل مجموعة جهات الاتصال ${groupIndex}`)
  }

  console.log(`📊 تم تحليل ${groupsAnalysis.length} مجموعة جهات اتصال`)
  return groupsAnalysis
}

// دالة تحميل قائمة الكلمات المهمة
async function loadImportantWords(): Promise<string[]> {
  console.log('📋 تحميل قائمة الكلمات المهمة')

  // طباعة جميع التبويبات المتاحة للتحقق
  console.log('🔍 التبويبات المتاحة:')
  tabs.value.forEach((tab, index) => {
    console.log(`   ${index + 1}. "${tab.name}"`)
  })

  // البحث عن التبويب بطرق مختلفة
  let importantWordsTab = databaseStore.getTabByName('قائمة المحتوى المهم')

  if (!importantWordsTab) {
    // جرب البحث بأسماء مختلفة محتملة
    const possibleNames = [
      'قائمة المحتوى المهم',
      'قائمة المحتوى المهم ',
      ' قائمة المحتوى المهم',
      'قائمة المحتوي المهم',
      'قائمة المحتوى المهم'
    ]

    for (const name of possibleNames) {
      importantWordsTab = databaseStore.getTabByName(name)
      if (importantWordsTab) {
        console.log(`✅ تم العثور على التبويب باسم: "${name}"`)
        break
      }
    }
  }

  if (!importantWordsTab) {
    console.warn('⚠️ لم يتم العثور على تبويب الكلمات المهمة')
    console.warn('💡 الأسماء المتاحة:', tabs.value.map(t => t.name))
    return []
  }

  console.log(`📊 تم العثور على التبويب: "${importantWordsTab.name}"`)
  console.log(`📊 عدد الصفوف: ${importantWordsTab.rows.length}`)
  console.log(`📊 الأعمدة:`, importantWordsTab.columns.map(c => c.name))

  const words: string[] = []
  importantWordsTab.rows.forEach((row, index) => {
    // طباعة تفاصيل الصف للتشخيص
    if (index < 3) {
      console.log(`   الصف ${index + 1}:`, row.data)
    }

    // البحث عن العمود الصحيح للكلمات المهمة
    const wordColumn = importantWordsTab.columns.find(col =>
      col.name === 'الكلمات_المهمة' ||
      col.name === 'الكلمات المهمة' ||
      col.name === 'الكلمات_المهمه' ||
      col.name === 'الكلمات-المهمة'
    )

    let word: string | undefined

    if (wordColumn) {
      // استخدم مفتاح العمود الصحيح
      word = row.data[wordColumn.id]
      if (index < 3) {
        console.log(`   العمود المستخدم: "${wordColumn.name}" (${wordColumn.id})`)
        console.log(`   الكلمة: "${word}"`)
      }
    } else {
      // إذا لم نجد العمود، جرب البحث في جميع القيم
      const values = Object.values(row.data)
      word = values.find(val =>
        typeof val === 'string' &&
        val.trim() &&
        val !== (index + 1).toString() && // تجاهل رقم الصف
        val.length > 1
      ) as string

      if (index < 3) {
        console.log(`   لم يتم العثور على عمود الكلمات، البحث في القيم:`, values)
        console.log(`   الكلمة المختارة: "${word}"`)
      }
    }

    if (word && typeof word === 'string' && word.trim()) {
      words.push(word.trim())
    }
  })

  console.log(`📝 تم تحميل ${words.length} كلمة مهمة:`, words)
  return words
}

// دالة حساب الإحصائيات العامة للرسائل
function calculateGeneralMessageStats(messagesTab: any, phoneNumber: string) {
  console.log('📊 حساب الإحصائيات العامة للرسائل')

  // البحث عن العمود الصحيح
  const phoneCol = messagesTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = messagesTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')

  console.log(`🔍 العمود المستخدم للهاتف: ${phoneCol ? phoneCol.name + ' (' + phoneCol.id + ')' : 'غير موجود'}`)
  console.log(`🔍 العمود المستخدم للمتهم: ${suspectCol ? suspectCol.name + ' (' + suspectCol.id + ')' : 'غير موجود'}`)

  let totalMessages = 0
  const uniqueSuspects = new Set<string>()

  // حساب عدد الرسائل المطابقة والمتهمين
  messagesTab.rows.forEach((row: any, index: number) => {
    if (phoneCol) {
      const cellValue = row.data[phoneCol.id]

      // طباعة تفاصيل أول 3 صفوف للتشخيص
      if (index < 3) {
        console.log(`   الصف ${index + 1}: الهاتف="${cellValue}", البحث عن="${phoneNumber}"`)
      }

      if (cellValue && cellValue.toString() === phoneNumber) {
        totalMessages++

        // إضافة اسم المتهم إذا وُجد
        if (suspectCol) {
          const suspectName = row.data[suspectCol.id]
          if (suspectName && suspectName.trim()) {
            uniqueSuspects.add(suspectName.trim())
          }
        }
      }
    }
  })

  const stats = {
    totalMessages,
    totalSuspects: uniqueSuspects.size,
    suspectNames: Array.from(uniqueSuspects)
  }

  console.log('📈 الإحصائيات العامة:', stats)
  return stats
}

// دالة تحليل المجموعات في تبويب الرسائل
async function analyzeMessageGroups(messagesTab: any, phoneNumber: string, importantWords: string[]) {
  console.log('👥 بدء تحليل المجموعات في تبويب الرسائل')

  const groupsAnalysis: any[] = []
  const rows = messagesTab.rows

  // البحث عن الأعمدة المطلوبة
  const phoneCol = messagesTab.columns.find((col: any) => col.name === 'رقم_جهة_الاتصال')
  const suspectCol = messagesTab.columns.find((col: any) => col.name === 'اسم_صاحب_البيانات')
  const ownerCol = messagesTab.columns.find((col: any) => col.name === 'تبعية_البيانات')
  const typeCol = messagesTab.columns.find((col: any) => col.name === 'النوع')
  const savedAsCol = messagesTab.columns.find((col: any) => col.name === 'محفوظ_بالإسم')
  const contentCol = messagesTab.columns.find((col: any) => col.name === '*محتوى_الرسالة')

  if (!phoneCol || !suspectCol) {
    console.error('❌ لم يتم العثور على الأعمدة المطلوبة')
    return groupsAnalysis
  }

  // تجميع الرسائل حسب المتهم (اسم_صاحب_البيانات)
  const groupedBySuspect = new Map<string, any[]>()

  rows.forEach((row: any) => {
    const cellPhone = row.data[phoneCol.id]
    const suspectName = row.data[suspectCol.id]

    if (cellPhone && cellPhone.toString() === phoneNumber && suspectName && suspectName.trim()) {
      const key = suspectName.trim()
      if (!groupedBySuspect.has(key)) {
        groupedBySuspect.set(key, [])
      }
      groupedBySuspect.get(key)!.push(row)
    }
  })

  console.log(`📊 تم العثور على ${groupedBySuspect.size} مجموعة (متهم)`)

  // تحليل كل مجموعة (متهم) - نأخذ فقط المجموعة الأولى وفقاً للسيناريو
  let groupIndex = 0
  for (const [suspectName, suspectRows] of groupedBySuspect) {
    groupIndex++
    console.log(`🔍 تحليل المجموعة ${groupIndex}: ${suspectName} (${suspectRows.length} رسائل)`)

    // الحصول على بيانات المجموعة
    const firstRow = suspectRows[0]
    const ownerNumber = ownerCol ? firstRow.data[ownerCol.id] : 'غير محدد'
    const savedAs = savedAsCol ? firstRow.data[savedAsCol.id] : 'غير محدد'

    // حساب الرسائل الصادرة والواردة
    let outgoingCount = 0
    let incomingCount = 0

    if (typeCol) {
      suspectRows.forEach(row => {
        const messageType = row.data[typeCol.id]
        if (messageType === 'صادرة') outgoingCount++
        else if (messageType === 'واردة') incomingCount++
      })
    }

    // تحليل محتوى الرسائل
    const messageAnalysis = analyzeMessageContent(suspectRows, importantWords, contentCol)

    groupsAnalysis.push({
      groupIndex,
      suspectName,
      ownerNumber,
      savedAs,
      outgoingCount,
      incomingCount,
      totalMessages: suspectRows.length,
      messageAnalysis
    })

    // نحلل جميع المجموعات
    console.log(`📝 تم تحليل المجموعة ${groupIndex}`)
  }

  console.log(`📊 تم تحليل ${groupsAnalysis.length} مجموعة تحتوي على رسائل مطابقة`)
  return groupsAnalysis
}



// دالة تحليل محتوى الرسائل والكلمات المهمة
function analyzeMessageContent(messageRows: any[], importantWords: string[], contentCol?: any) {
  console.log('📝 تحليل محتوى الرسائل والكلمات المهمة')

  const analysis = {
    messagesWithImportantWords: [] as any[],
    messagesForReview: [] as any[],
    foundWords: new Set<string>()
  }

  messageRows.forEach((row, index) => {
    // الحصول على محتوى الرسالة
    let messageContent = ''
    if (contentCol) {
      messageContent = row.data[contentCol.id] || ''
    } else {
      messageContent = row.data['*محتوى_الرسالة'] || row.data['محتوى_الرسالة'] || ''
    }

    if (!messageContent || typeof messageContent !== 'string' || !messageContent.trim()) {
      // رسالة فارغة - تجاهل
      return
    }

    // البحث عن الكلمات المهمة (case insensitive)
    const foundWordsInMessage: string[] = []
    const messageContentLower = messageContent.toLowerCase()

    importantWords.forEach(word => {
      if (messageContentLower.includes(word.toLowerCase())) {
        foundWordsInMessage.push(word)
        analysis.foundWords.add(word)
      }
    })

    const messageInfo = {
      index: index + 1,
      content: messageContent,
      type: row.data['النوع'] || 'غير محدد',
      date: row.data['التاريخ'] || 'غير محدد',
      time: row.data['الوقت'] || 'غير محدد'
    }

    if (foundWordsInMessage.length > 0) {
      // رسالة تحتوي على كلمات مهمة
      analysis.messagesWithImportantWords.push({
        ...messageInfo,
        foundWords: foundWordsInMessage
      })

      // تلوين الصف بالكامل باللون الأحمر
      row.isImportant = true
      row.style = {
        color: '#dc2626', // أحمر
        fontWeight: 'bold'
      }

    } else {
      // رسالة للمراجعة
      analysis.messagesForReview.push(messageInfo)
    }
  })

  console.log(`📊 تحليل المحتوى: ${analysis.messagesWithImportantWords.length} رسائل مهمة، ${analysis.messagesForReview.length} للمراجعة`)
  console.log(`🔍 الكلمات المكتشفة:`, Array.from(analysis.foundWords))

  return {
    messagesWithImportantWords: analysis.messagesWithImportantWords,
    messagesForReview: analysis.messagesForReview,
    foundWords: Array.from(analysis.foundWords),
    totalImportantMessages: analysis.messagesWithImportantWords.length,
    totalReviewMessages: analysis.messagesForReview.length
  }
}

async function handleGenerateMemo(data: { phoneNumber: string, suspectName: string, results: any }) {
  console.log('📝 إنشاء مذكرة ملخص:', data)

  if (!searchMemoModalRef.value) return

  try {
    // بدء عملية التحليل
    searchMemoModalRef.value.setSearchLoading('بدء تحليل البيانات لإنشاء المذكرة...')
    searchMemoModalRef.value.setSearchProgress(0)

    // تحليل الجزء الأول: تبويب الرسائل النصية
    const part1Analysis = await analyzeMessagesTab(data)

    // تحديث التقدم
    searchMemoModalRef.value.setSearchProgress(33)

    // تحليل الجزء الثاني: تبويب المكالمات
    searchMemoModalRef.value.setSearchLoading('تحليل سجلات المكالمات...')
    const part2Analysis = await analyzeCallsTab(data)

    // تحديث التقدم
    searchMemoModalRef.value.setSearchProgress(66)

    // تحليل الجزء الثالث: تبويب جهات الاتصال
    searchMemoModalRef.value.setSearchLoading('تحليل سجلات جهات الاتصال...')
    const part3Analysis = await analyzeContactsTab(data)

    // تحديث التقدم إلى 100%
    searchMemoModalRef.value.setSearchProgress(100)

    // إنشاء بنية بيانات المذكرة
    const memoData = {
      suspectInfo: {
        name: data.suspectName,
        phoneNumber: data.phoneNumber
      },
      part1: part1Analysis,
      part2: part2Analysis, // تم إضافة الجزء الثاني
      part3: part3Analysis, // تم إضافة الجزء الثالث
      generatedAt: new Date()
    }

    console.log('📊 نتائج تحليل الجزء الأول:', part1Analysis)

    // تطبيق التلوين على التبويب المؤقت
    applyHighlightingToTempTab()

    // تحديث الحالة
    searchMemoModalRef.value.setSearchProgress(100)
    searchMemoModalRef.value.setSearchResults({
      ...data.results,
      memoData: memoData
    })

    // عرض المذكرة في نافذة جديدة
    displayMemoInNewWindow(generateMemoContent(memoData, data))

    showMessage('تم إنشاء الجزء الأول من المذكرة بنجاح', 'success')

  } catch (error) {
    console.error('خطأ في إنشاء المذكرة:', error)
    searchMemoModalRef.value.setSearchError('حدث خطأ أثناء تحليل البيانات')
  }
}

async function handleExportResults(data: { phoneNumber: string, suspectName: string, results: any }) {
  console.log('📤 تصدير نتائج المطابقة:', data)

  // عرض خيارات التصدير للمستخدم
  const exportOptions = [
    'التصدير الشامل',
    'تصدير بصيغة HTML',
    'تصدير المذكرة مع التفاصيل'
  ]

  const selectedOption = await showExportOptionsDialog(exportOptions)

  if (!selectedOption) {
    console.log('❌ المستخدم ألغى عملية التصدير')
    return
  }

  console.log(`📤 بدء التصدير: ${selectedOption}`)

  try {
    // إظهار شريط التقدم
    searchMemoModalRef.value?.setSearchLoading(`جاري ${selectedOption}...`)
    searchMemoModalRef.value?.setSearchProgress(0)

    switch (selectedOption) {
      case 'التصدير الشامل':
        await performComprehensiveExport(data)
        break
      case 'تصدير بصيغة HTML':
        await performHTMLExport(data)
        break
      case 'تصدير المذكرة مع التفاصيل':
        await performMemoWithDetailsExport(data)
        break
    }

    searchMemoModalRef.value?.setSearchProgress(100)

    // إخفاء شريط التقدم بعد ثانيتين
    setTimeout(() => {
      searchMemoModalRef.value?.setSearchLoading('')
      searchMemoModalRef.value?.setSearchProgress(0)
    }, 2000)

    showMessage(`تم ${selectedOption} بنجاح`, 'success')

  } catch (error) {
    console.error('خطأ في التصدير:', error)

    // إخفاء شريط التقدم في حالة الخطأ
    searchMemoModalRef.value?.setSearchLoading('')
    searchMemoModalRef.value?.setSearchProgress(0)

    showMessage('حدث خطأ أثناء عملية التصدير', 'error')
  }
}

// دالة عرض خيارات التصدير
async function showExportOptionsDialog(options: string[]): Promise<string | null> {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      direction: rtl;
    `

    const content = document.createElement('div')
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      max-width: 400px;
      width: 90%;
      text-align: center;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `

    content.innerHTML = `
      <h3 style="margin-bottom: 20px; color: #2563eb;">اختر نوع التصدير</h3>
      <div style="margin-bottom: 20px;">
        ${options.map((option) => `
          <button
            data-option="${option}"
            style="
              display: block;
              width: 100%;
              margin: 10px 0;
              padding: 12px;
              background: #f3f4f6;
              border: 2px solid #e5e7eb;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#e5e7eb'; this.style.borderColor='#2563eb';"
            onmouseout="this.style.background='#f3f4f6'; this.style.borderColor='#e5e7eb';"
          >
            📤 ${option}
          </button>
        `).join('')}
      </div>
      <button
        id="cancelExport"
        style="
          padding: 10px 20px;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
        "
      >
        إلغاء
      </button>
    `

    dialog.appendChild(content)
    document.body.appendChild(dialog)

    // إضافة مستمعي الأحداث
    content.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (target.dataset.option) {
        document.body.removeChild(dialog)
        resolve(target.dataset.option)
      } else if (target.id === 'cancelExport') {
        document.body.removeChild(dialog)
        resolve(null)
      }
    })
  })
}

// دالة تطبيق التلوين على التبويب المؤقت
function applyHighlightingToTempTab() {
  console.log('🎨 تطبيق التلوين على الصفوف المهمة في التبويب المؤقت')

  // البحث عن التبويب المؤقت الأول (الرسائل)
  const tempMessagesTab = tabs.value.find(tab =>
    tab.name === 'البيانات المطابقة لسجلات الرسائل'
  )

  if (!tempMessagesTab) {
    console.log('❌ لم يتم العثور على التبويب المؤقت للرسائل')
    return
  }

  // تطبيق التلوين على الصفوف المهمة
  let highlightedCount = 0
  tempMessagesTab.rows.forEach((row, index) => {
    if (row.isImportant) {
      // تطبيق التلوين الأحمر باستخدام importantColor (الطريقة الصحيحة)
      row.importantColor = '#dc2626'

      // حفظ اللون الأصلي للاستعادة لاحقاً
      if (!row.originalTextColor) {
        row.originalTextColor = '#475569'
      }

      console.log(`🎨 تم تلوين الصف ${index + 1}:`, {
        isImportant: row.isImportant,
        importantColor: row.importantColor,
        originalTextColor: row.originalTextColor,
        data: row.data
      })

      highlightedCount++
    }
  })

  console.log(`🎨 تم تلوين ${highlightedCount} صف يحتوي على كلمات مهمة`)
  console.log('📋 التبويب المؤقت بعد التلوين:', tempMessagesTab)

  // إجبار إعادة الرسم
  console.log('🔄 إجبار إعادة رسم التبويب المؤقت')

  // حفظ التبويب المحدث في المتجر
  databaseStore.updateTab(tempMessagesTab.id, tempMessagesTab)

  // إجبار إعادة الرسم
  nextTick(() => {
    // تحديث التبويب النشط لإعادة الرسم
    if (activeTab.value === tempMessagesTab.id) {
      console.log('🔄 تبديل التبويب لإعادة الرسم')
      const currentTab = activeTab.value
      activeTab.value = ''
      nextTick(() => {
        activeTab.value = currentTab
        console.log('✅ تم إعادة تحديد التبويب المؤقت')
      })
    } else {
      console.log('🔄 التبويب المؤقت ليس نشطاً، تحديد التبويب المؤقت')
      activeTab.value = tempMessagesTab.id
    }
  })
}

// دالة إنشاء محتوى المذكرة
function generateMemoContent(memoData: any, data: any): string {
  const currentDate = new Date().toLocaleDateString('ar-SA')
  const currentTime = new Date().toLocaleTimeString('ar-SA')

  // إنشاء محتوى جميع مجموعات الرسائل (الجزء الأول)
  let messagesGroupsContent = ''
  if (memoData.part1 && memoData.part1.groupsAnalysis && memoData.part1.groupsAnalysis.length > 0) {
    messagesGroupsContent = memoData.part1.groupsAnalysis.map((group: any, index: number) => {
      const messageAnalysis = group.messageAnalysis

      return `
        <div class="section">
          <div class="section-title">تفصيل المجموعة ${index + 1} - الرسائل النصية</div>
          <div class="info-row">
            <span class="info-label">المتهم:</span>
            <span class="info-value">${group.suspectName}</span>
          </div>
          <div class="info-row">
            <span class="info-label">رقم المتهم:</span>
            <span class="info-value">${group.ownerNumber}</span>
          </div>
          <div class="info-row">
            <span class="info-label">أرسل المتهم:</span>
            <span class="info-value">${group.outgoingCount} رسائل صادرة، واستقبل ${group.incomingCount} رسائل واردة</span>
          </div>
          <div class="info-row">
            <span class="info-label">مسجل:</span>
            <span class="info-value">${group.savedAs}</span>
          </div>

          <div style="margin-top: 15px;">
            <div class="section-title" style="font-size: 16px;">محتوى الرسائل وتقييم الأهمية</div>
            ${messageAnalysis.messagesWithImportantWords.length > 0 ? `
              <div style="margin-bottom: 15px;">
                <strong>الرسائل التي احتوت كلمات مهمة:</strong>
                <div style="margin-top: 10px;">
                  ${messageAnalysis.foundWords.map((word: string) =>
                    `<span class="important-word">${word}</span>`
                  ).join(' ')}
                </div>
                <p style="margin-top: 10px;">عدد الرسائل المهمة: ${messageAnalysis.messagesWithImportantWords.length}</p>
              </div>
            ` : ''}
            ${messageAnalysis.messagesForReview.length > 0 ? `
              <div>
                <strong>الرسائل الأخرى:</strong> ترك التقييم للجهة المختصة
                <p>عدد الرسائل للمراجعة: ${messageAnalysis.messagesForReview.length}</p>
              </div>
            ` : ''}
          </div>
        </div>
      `
    }).join('')
  }

  // إنشاء محتوى جميع مجموعات المكالمات (الجزء الثاني)
  let callsGroupsContent = ''
  if (memoData.part2 && memoData.part2.hasData && memoData.part2.groupsAnalysis && memoData.part2.groupsAnalysis.length > 0) {
    callsGroupsContent = memoData.part2.groupsAnalysis.map((group: any, index: number) => {
      return `
        <div class="section">
          <div class="section-title">المجموعة ${index + 1} - المكالمات</div>
          <div class="info-row">
            <span class="info-label">المتهم:</span>
            <span class="info-value">${group.suspectName} (رقم: ${group.ownerNumber})</span>
          </div>
          <div class="info-row">
            <span class="info-label">المفقودة:</span>
            <span class="info-value">${group.missedCount}</span>
          </div>
          <div class="info-row">
            <span class="info-label">الصادرة:</span>
            <span class="info-value">${group.outgoingCount}</span>
          </div>
          <div class="info-row">
            <span class="info-label">الواردة:</span>
            <span class="info-value">${group.incomingCount}</span>
          </div>
          <div class="info-row">
            <span class="info-label">مسجل:</span>
            <span class="info-value">${group.savedAs}</span>
          </div>
          <div class="info-row">
            <span class="info-label">إجمالي دقائق المكالمات الصادرة:</span>
            <span class="info-value">${group.outgoingMinutes} دقيقة</span>
          </div>
          <div class="info-row">
            <span class="info-label">إجمالي دقائق المكالمات الواردة:</span>
            <span class="info-value">${group.incomingMinutes} دقيقة</span>
          </div>
        </div>
      `
    }).join('')
  }

  return `
    <div style="text-align: center; margin-bottom: 30px;">
      <h2 style="margin-bottom: 10px;">إلى من يهمه الأمر،</h2>
    </div>

    <div class="section">
      <p style="line-height: 1.8;">
        بناءً على البحث عن ارتباطات واتصالات للمشتبه به:<br>
        - الاسم: <strong>${data.suspectName}</strong><br>
        - رقم الهاتف: <strong>${data.phoneNumber}</strong><br>
        في قاعدة بيانات سجلات المتهمين، تبيّن الآتي:
      </p>
    </div>

    <div class="section">
      <div class="section-title">إحصائيات عامة عن الرسائل النصية</div>
      <div class="info-row">
        <span class="info-label">• عدد الرسائل النصية المطابقة:</span>
        <span class="info-value highlight">${memoData.part1?.generalStats?.totalMessages || 0} رسالة</span>
      </div>
      <div class="info-row">
        <span class="info-label">• هذه الرسائل شملت:</span>
        <span class="info-value highlight">${memoData.part1?.generalStats?.totalSuspects || 0} متهمين</span>
      </div>
    </div>

    ${messagesGroupsContent}

    <div class="section">
      <div class="section-title">سجلات حركة الاتصالات</div>
      ${memoData.part2?.hasData ? `
        <p>وبالنسبة لسجلات حركة الاتصالات، ظهر رقم المشتبه به <strong>${data.suspectName}</strong> في عدد <strong>${memoData.part2.generalStats.totalCalls}</strong> مرات.</p>
        <p>وتمت هذه المكالمات مع <strong>${memoData.part2.generalStats.totalSuspects}</strong> متهمين مختلفين.</p>
      ` : `
        <p>لا توجد سجلات مكالمات مطابقة لهذا الرقم.</p>
      `}
    </div>

    ${callsGroupsContent}

    <div class="section">
      <div class="section-title">سجلات جهات الاتصال</div>
      ${memoData.part3?.hasData ? `
        <p>وبالنسبة لسجلات جهات الاتصال، ظهر رقم المشتبه به <strong>${data.phoneNumber}</strong> في عدد <strong>${memoData.part3.generalStats.totalContacts}</strong> مرات.</p>
        <p>وقد ورد هذا الرقم في سجل جهات اتصال <strong>${memoData.part3.generalStats.totalSuspects}</strong> متهمين مختلفين.</p>
      ` : `
        <p>لا توجد جهات اتصال مطابقة لهذا الرقم.</p>
      `}
    </div>

    ${generateContactsGroupsContent(memoData)}

    <div class="section">
      <div class="section-title">ملاحظات</div>
      <p>تم إكمال تحليل جميع البيانات المطابقة للمشتبه به في قاعدة بيانات سجلات المتهمين.</p>
      <p>يشمل هذا التحليل: الرسائل النصية، المكالمات، وجهات الاتصال.</p>
      <p style="margin-top: 20px; text-align: center;">
        <strong>تاريخ إنشاء المذكرة:</strong> ${currentDate} - <strong>الوقت:</strong> ${currentTime}
      </p>
    </div>
  `
}

// دالة إنشاء محتوى مجموعات جهات الاتصال
function generateContactsGroupsContent(memoData: any): string {
  if (!memoData.part3?.hasData || !memoData.part3.groupsAnalysis || memoData.part3.groupsAnalysis.length === 0) {
    return ''
  }

  return memoData.part3.groupsAnalysis.map((group: any, index: number) => {
    return `
      <div class="section">
        <div class="section-title">المجموعة ${index + 1} - جهات الاتصال</div>
        <div class="info-row">
          <span class="info-label">المتهم:</span>
          <span class="info-value">${group.suspectName}</span>
        </div>
        <div class="info-row">
          <span class="info-label">المشتبه به مسجلاً في قائمة جهات اتصال المتهم مسجل:</span>
          <span class="info-value">${group.savedAs}</span>
        </div>
      </div>
    `
  }).join('')
}

// دوال التصدير الثلاثة

// 1. التصدير الشامل
async function performComprehensiveExport(data: { phoneNumber: string, suspectName: string, results: any }) {
  console.log('📦 بدء التصدير الشامل')

  const timestamp = new Date().toLocaleString('ar-SA').replace(/[/:]/g, '-')
  const folderName = `كل ملفات نتيجة المطابقة والبحث ${data.suspectName} ${timestamp}`

  // إنشاء مجلد مضغوط (محاكاة)
  const files: { name: string, content: string, type: string }[] = []

  // 1. مذكرة البحث HTML
  searchMemoModalRef.value?.setSearchProgress(10)
  const memoHTML = await generateMemoHTML(data)
  files.push({
    name: `مذكرة ملخص البحث والمطابقة ${data.suspectName}.html`,
    content: memoHTML,
    type: 'text/html'
  })

  // 2. التبويبات المؤقتة HTML
  searchMemoModalRef.value?.setSearchProgress(30)
  const tempTabs = getTempTabs()
  for (const tab of tempTabs) {
    const tabHTML = generateTabHTML(tab)
    files.push({
      name: `${tab.name}.html`,
      content: tabHTML,
      type: 'text/html'
    })
  }

  // 3. التبويبات المؤقتة CSV
  searchMemoModalRef.value?.setSearchProgress(60)
  for (const tab of tempTabs) {
    const tabCSV = generateTabCSV(tab)
    files.push({
      name: `${tab.name}.csv`,
      content: tabCSV,
      type: 'text/csv'
    })
  }

  // 4. مذكرة مع التفاصيل HTML
  searchMemoModalRef.value?.setSearchProgress(80)
  const combinedHTML = await generateCombinedHTML(data)
  files.push({
    name: `مذكرة ملخص المطابقة والبحث الخاصة ب${data.suspectName} مع البيانات التفصيلية ${timestamp}.html`,
    content: combinedHTML,
    type: 'text/html'
  })

  // تحميل الملفات
  searchMemoModalRef.value?.setSearchProgress(90)
  await downloadFiles(files, folderName)

  console.log('✅ تم إكمال التصدير الشامل')
}

// 2. تصدير بصيغة HTML
async function performHTMLExport(data: { phoneNumber: string, suspectName: string, results: any }) {
  console.log('📄 بدء تصدير HTML')

  const timestamp = new Date().toLocaleString('ar-SA').replace(/[/:]/g, '-')
  const folderName = `نسخة لملفات المطابقة والبحث ${data.suspectName} ${timestamp}`

  const files: { name: string, content: string, type: string }[] = []

  // 1. مذكرة البحث HTML
  searchMemoModalRef.value?.setSearchProgress(20)
  const memoHTML = await generateMemoHTML(data)
  files.push({
    name: `مذكرة ملخص البحث والمطابقة ${data.suspectName}.html`,
    content: memoHTML,
    type: 'text/html'
  })

  // 2. التبويبات المؤقتة HTML
  searchMemoModalRef.value?.setSearchProgress(60)
  const tempTabs = getTempTabs()
  for (const tab of tempTabs) {
    const tabHTML = generateTabHTML(tab)
    files.push({
      name: `${tab.name}.html`,
      content: tabHTML,
      type: 'text/html'
    })
  }

  // تحميل الملفات
  searchMemoModalRef.value?.setSearchProgress(90)
  await downloadFiles(files, folderName)

  console.log('✅ تم إكمال تصدير HTML')
}

// 3. تصدير المذكرة مع التفاصيل
async function performMemoWithDetailsExport(data: { phoneNumber: string, suspectName: string, results: any }) {
  console.log('📋 بدء تصدير المذكرة مع التفاصيل')

  const timestamp = new Date().toLocaleString('ar-SA').replace(/[/:]/g, '-')
  const fileName = `مذكرة ملخص المطابقة والبحث الخاصة ب${data.suspectName} مع البيانات التفصيلية ${timestamp}.html`

  searchMemoModalRef.value?.setSearchProgress(50)
  const combinedHTML = await generateCombinedHTML(data)

  searchMemoModalRef.value?.setSearchProgress(90)
  downloadSingleFile(combinedHTML, fileName, 'text/html')

  console.log('✅ تم إكمال تصدير المذكرة مع التفاصيل')
}

// دوال مساعدة للتصدير

// دالة الحصول على التبويبات المؤقتة
function getTempTabs() {
  const tempTabNames = [
    'البيانات المطابقة لسجلات الرسائل',
    'البيانات المطابقة لسجلات المكالمات',
    'البيانات المطابقة لجهات الاتصال'
  ]

  return tempTabNames.map(name => databaseStore.getTabByName(name)).filter(tab => tab !== null) as any[]
}

// دالة التحقق من وجود التبويبات المؤقتة
function checkTempTabsExist(): boolean {
  const tempTabs = getTempTabs()
  return tempTabs.length === 3 // يجب أن تكون جميع التبويبات الثلاثة موجودة
}

// دالة فتح نافذة البحث مع التحقق من البيانات السابقة
function openSearchMemoModal() {
  // التحقق من وجود التبويبات المؤقتة
  const hasResults = checkTempTabsExist()

  if (hasResults && lastSearchData.value) {
    // إذا كانت هناك نتائج سابقة، استخدم البيانات المحفوظة
    lastSearchData.value.hasResults = true

    // إذا كانت هناك نتائج محفوظة، استعدها في النافذة
    if (lastSearchData.value.searchResults) {
      // سنرسل النتائج للنافذة بعد فتحها
      setTimeout(() => {
        if (searchMemoModalRef.value && lastSearchData.value?.searchResults) {
          searchMemoModalRef.value.setSearchResults(lastSearchData.value.searchResults)
        }
      }, 100)
    }
  } else if (hasResults) {
    // إذا كانت هناك تبويبات مؤقتة لكن لا توجد بيانات محفوظة
    // حاول استخراج البيانات من التبويبات المؤقتة
    const extractedData = extractDataFromTempTabs()
    if (extractedData) {
      lastSearchData.value = {
        ...extractedData,
        hasResults: true
      }
    }
  } else {
    // لا توجد نتائج سابقة
    lastSearchData.value = null
  }

  showSearchMemoModal.value = true
}

// دالة استخراج البيانات من التبويبات المؤقتة
function extractDataFromTempTabs(): { phoneNumber: string, suspectName: string } | null {
  try {
    const tempTabs = getTempTabs()
    if (tempTabs.length === 0) return null

    // البحث في أول تبويب مؤقت عن بيانات المشتبه به
    const firstTab = tempTabs[0]
    if (!firstTab || !firstTab.rows || firstTab.rows.length === 0) return null

    // البحث عن أول صف يحتوي على بيانات (ليس عنوان مجموعة)
    const dataRow = firstTab.rows.find((row: any) => !row.isGroupHeader && row.data)
    if (!dataRow) return null

    // محاولة استخراج رقم الهاتف واسم المشتبه به
    const phoneCol = firstTab.columns.find((col: any) =>
      col.name.includes('رقم') || col.name.includes('هاتف') || col.name.includes('جوال')
    )
    const nameCol = firstTab.columns.find((col: any) =>
      col.name.includes('اسم') || col.name.includes('صاحب')
    )

    if (!phoneCol || !nameCol) return null

    const phoneNumber = dataRow.data[phoneCol.id]
    const suspectName = dataRow.data[nameCol.id]

    if (!phoneNumber || !suspectName) return null

    return {
      phoneNumber: String(phoneNumber).trim(),
      suspectName: String(suspectName).trim()
    }
  } catch (error) {
    console.error('خطأ في استخراج البيانات من التبويبات المؤقتة:', error)
    return null
  }
}

// دالة إعادة تعيين البحث
function handleResetSearch() {
  console.log('🔄 إعادة تعيين البحث')

  // حذف التبويبات المؤقتة
  const tempTabNames = [
    'البيانات المطابقة لسجلات الرسائل',
    'البيانات المطابقة لسجلات المكالمات',
    'البيانات المطابقة لجهات الاتصال'
  ]

  tempTabNames.forEach(name => {
    const tab = databaseStore.getTabByName(name)
    if (tab) {
      databaseStore.removeTab(tab.id)
      console.log(`🗑️ تم حذف التبويب المؤقت: ${name}`)
    }
  })

  // مسح البيانات المحفوظة
  lastSearchData.value = null

  showMessage('تم إعادة تعيين البحث وحذف النتائج السابقة', 'success')
}

// دالة إنشاء HTML للمذكرة
async function generateMemoHTML(data: { phoneNumber: string, suspectName: string, results: any }) {
  // إعادة تحليل البيانات للحصول على المذكرة الكاملة
  const part1Analysis = await analyzeMessagesTab(data)
  const part2Analysis = await analyzeCallsTab(data)
  const part3Analysis = await analyzeContactsTab(data)

  const memoData = {
    suspectInfo: {
      name: data.suspectName,
      phoneNumber: data.phoneNumber
    },
    part1: part1Analysis,
    part2: part2Analysis,
    part3: part3Analysis,
    generatedAt: new Date()
  }

  return generateFullHTMLDocument(generateMemoContent(memoData, data), 'مذكرة ملخص البحث والمطابقة')
}

// دالة إنشاء HTML للتبويب (استخدام نظام التصدير الأصلي)
function generateTabHTML(tab: any): string {
  if (!tab) return ''

  try {
    console.log('🎨 إنشاء HTML للتبويب باستخدام النظام الأصلي:', tab.name)

    // استخدام نفس منطق دالة exportToHTML الأصلية
    const visibleColumns = tab.columns.filter((col: any) => col.isVisible !== false).sort((a: any, b: any) => (a.order || 0) - (b.order || 0))
    const rows = tab.rows.filter((row: any) => !row.isGroupHeader || tab.settings?.exportSettings?.includeGroupHeaders !== false)

    // إنشاء محتوى HTML بنفس تنسيق النظام الأصلي
    let htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${tab.name}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .header .date {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 11px;
            table-layout: fixed;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: right;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }
        .group-header {
            background-color: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
        }
        .alternate-row {
            background-color: #f9f9f9;
        }
        @media print {
            body {
                margin: 0;
                font-size: 10px;
            }
            .no-print { display: none; }
            table {
                font-size: 9px;
                page-break-inside: auto;
            }
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            th, td {
                padding: 4px;
            }
        }
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${tab.name}</h1>
        <div class="date">تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')}</div>
    </div>

    <table>
`

    // إضافة عناوين الجدول
    if (tab.settings?.exportSettings?.includeHeaders !== false) {
      htmlContent += '<thead><tr>'
      if (tab.settings?.showRowNumbers !== false) {
        htmlContent += '<th style="width: 50px;">#</th>'
      }
      for (const col of visibleColumns) {
        const style = getColumnHeaderStyle(col)
        const width = getColumnWidthPercentage(col, visibleColumns)
        htmlContent += `<th style="${style}; width: ${width}%">${col.name}</th>`
      }
      htmlContent += '</tr></thead>'
    }

    // إضافة محتوى الجدول
    htmlContent += '<tbody>'
    let rowNumber = 1

    for (const row of rows) {
      if (row.isGroupHeader && tab.settings?.exportSettings?.includeGroupHeaders !== false) {
        // صف عنوان المجموعة
        const colspan = visibleColumns.length + (tab.settings?.showRowNumbers !== false ? 1 : 0)
        const style = getGroupHeaderStyle(tab.groupHeaders)
        htmlContent += `<tr><td colspan="${colspan}" class="group-header" style="${style}">${row.groupHeaderText || ''}</td></tr>`
      } else if (!row.isGroupHeader) {
        // صف البيانات
        const rowClass = tab.settings?.alternateRowColors !== false && rowNumber % 2 === 0 ? 'alternate-row' : ''
        htmlContent += `<tr class="${rowClass}">`

        if (tab.settings?.showRowNumbers !== false) {
          htmlContent += `<td style="text-align: center; color: #666;">${rowNumber}</td>`
        }

        for (const col of visibleColumns) {
          const value = row.data[col.id] || ''
          const style = getColumnCellStyle(col)
          htmlContent += `<td style="${style}">${escapeHtml(String(value))}</td>`
        }

        htmlContent += '</tr>'
        rowNumber++
      }
    }

    htmlContent += `
        </tbody>
    </table>

    <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
        تم إنشاء هذا التقرير بواسطة نظام إدارة بيانات المتهمين
    </div>
</body>
</html>`

    console.log('✅ تم إنشاء HTML للتبويب بنجاح:', tab.name)
    return htmlContent

  } catch (error) {
    console.error('خطأ في إنشاء HTML للتبويب:', error)
    console.log('🔄 التبديل إلى التصدير الاحتياطي للتبويب:', tab.name)
    return generateFallbackHTML(tab)
  }
}

// دالة احتياطية في حالة فشل التصدير الأصلي
function generateFallbackHTML(tab: any) {
  console.log('🔄 استخدام التصدير الاحتياطي للتبويب:', tab.name)

  const visibleColumns = tab.columns.filter((col: any) => col.isVisible !== false).sort((a: any, b: any) => (a.order || 0) - (b.order || 0))

  let htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${tab.name}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .header .date {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 11px;
            table-layout: fixed;
        }
        th {
            background-color: #8B4513;
            color: white;
            border: 1px solid #654321;
            padding: 8px 4px;
            text-align: center;
            font-weight: bold;
            word-wrap: break-word;
            vertical-align: top;
        }
        td {
            border: 1px solid #dee2e6;
            padding: 6px 4px;
            text-align: right;
            word-wrap: break-word;
            vertical-align: top;
            line-height: 1.3;
        }
        .group-header {
            background-color: #8B4513 !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 14px !important;
            text-align: center !important;
            padding: 10px !important;
        }
        .alternate-row {
            background-color: #E8F5E8;
        }
        .important-row {
            color: #dc2626 !important;
            font-weight: bold !important;
        }
        @media print {
            body { margin: 0; font-size: 10px; }
            table { font-size: 9px; }
            th, td { padding: 4px 2px; }
        }
        @page {
            size: A4;
            margin: 1cm;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${tab.name}</h1>
        <div class="date">تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')}</div>
        <div style="color: #666; font-size: 12px; margin-top: 5px;">عدد الصفوف: ${tab.rows.filter((r: any) => !r.isGroupHeader).length} صف</div>
    </div>

    <table>
        <thead>
            <tr>
                <th style="width: 50px;">#</th>
                ${visibleColumns.map((col: any) => `<th>${col.name}</th>`).join('')}
            </tr>
        </thead>
        <tbody>
`

  let rowNumber = 1
  for (const row of tab.rows) {
    if (row.isGroupHeader) {
      const colspan = visibleColumns.length + 1
      htmlContent += `<tr><td colspan="${colspan}" class="group-header">${row.groupHeaderText || ''}</td></tr>`
    } else {
      const isAlternate = rowNumber % 2 === 0
      const isImportant = row.isImportant && row.importantColor
      const rowClass = isImportant ? 'important-row' : (isAlternate ? 'alternate-row' : '')

      htmlContent += `<tr class="${rowClass}">`
      htmlContent += `<td style="text-align: center; color: #666;">${rowNumber}</td>`

      for (const col of visibleColumns) {
        const value = row.data[col.id] || ''
        const cellStyle = isImportant ? `color: ${row.importantColor}; font-weight: bold;` : ''
        htmlContent += `<td style="${cellStyle}">${escapeHtml(String(value))}</td>`
      }

      htmlContent += '</tr>'
      rowNumber++
    }
  }

  htmlContent += `
        </tbody>
    </table>

    <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
        تم إنشاء هذا التقرير بواسطة نظام إدارة بيانات المتهمين
    </div>
</body>
</html>`

  return htmlContent
}

// دوال مساعدة للتنسيق (مطابقة للنظام الأصلي)

// دالة حساب عرض العمود كنسبة مئوية
function getColumnWidthPercentage(column: any, visibleColumns: any[]) {
  const totalWidth = visibleColumns.reduce((sum: number, col: any) => sum + (col.width || 150), 0)
  const columnWidth = column.width || 150
  return Math.round((columnWidth / totalWidth) * 100)
}

// دالة الحصول على تنسيق عنوان العمود
function getColumnHeaderStyle(column: any): string {
  const formatting = column.formatting || {}
  return [
    `background-color: ${formatting.headerBackgroundColor || '#f5f5f5'}`,
    `color: ${formatting.headerTextColor || '#333'}`,
    `font-size: ${formatting.headerFontSize || 11}px`,
    `font-weight: ${formatting.headerFontWeight || 'bold'}`,
    `text-align: ${formatting.headerTextAlign || 'center'}`,
    `border: ${formatting.borderWidth || 1}px ${formatting.borderStyle || 'solid'} ${formatting.borderColor || '#ddd'}`
  ].join('; ')
}

// دالة الحصول على تنسيق خلية العمود
function getColumnCellStyle(column: any): string {
  const formatting = column.formatting || {}
  return [
    `background-color: ${formatting.cellBackgroundColor || 'transparent'}`,
    `color: ${formatting.cellTextColor || '#333'}`,
    `font-size: ${formatting.cellFontSize || 11}px`,
    `font-weight: ${formatting.cellFontWeight || 'normal'}`,
    `text-align: ${formatting.cellTextAlign || 'right'}`,
    `white-space: ${formatting.cellTextWrap ? 'normal' : 'nowrap'}`,
    `border: ${formatting.borderWidth || 1}px ${formatting.borderStyle || 'solid'} ${formatting.borderColor || '#ddd'}`
  ].join('; ')
}

// دالة الحصول على تنسيق عنوان المجموعة
function getGroupHeaderStyle(groupHeaders: any): string {
  const headers = groupHeaders || {}
  return [
    `background-color: ${headers.backgroundColor || '#e3f2fd'}`,
    `color: ${headers.textColor || '#1976d2'}`,
    `font-size: ${headers.fontSize || 14}px`,
    `font-weight: ${headers.fontWeight || 'bold'}`,
    `text-align: ${headers.textAlign || 'center'}`
  ].join('; ')
}

// دالة تنظيف HTML
function escapeHtml(text: string) {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// دالة إنشاء CSV للتبويب
function generateTabCSV(tab: any) {
  if (!tab) return ''

  // إضافة BOM للدعم العربي
  let csv = '\uFEFF'

  // إضافة العناوين
  const headers = tab.columns.map((col: any) => `"${col.name}"`).join(',')
  csv += headers + '\n'

  // إضافة الصفوف
  tab.rows.forEach((row: any) => {
    const cells = tab.columns.map((col: any) => {
      const cellValue = row.data[col.id] || ''
      return `"${cellValue.toString().replace(/"/g, '""')}"`
    })
    csv += cells.join(',') + '\n'
  })

  return csv
}

// دالة إنشاء HTML مدمج (مذكرة + تبويبات)
async function generateCombinedHTML(data: { phoneNumber: string, suspectName: string, results: any }) {
  // 1. المذكرة
  const memoHTML = await generateMemoHTML(data)

  // 2. التبويبات المؤقتة
  const tempTabs = getTempTabs()
  const tabsHTML = tempTabs.map(tab => {
    if (!tab) return ''

    // استخراج محتوى الجدول فقط من HTML الكامل
    const fullTabHTML = generateTabHTML(tab)
    const tableMatch = fullTabHTML.match(/<table[\s\S]*?<\/table>/i)
    const tableHTML = tableMatch ? tableMatch[0] : ''

    return `
      <div style="page-break-before: always; margin-top: 40px;">
        <div class="header" style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
          <h2 style="color: #2563eb; margin: 0; font-size: 20px;">${tab.name}</h2>
          <div style="color: #666; font-size: 12px; margin-top: 10px;">عدد الصفوف: ${tab.rows.filter((r: any) => !r.isGroupHeader).length} صف</div>
        </div>
        ${tableHTML}
      </div>
    `
  }).join('')

  // دمج المحتوى مع تحسين التنسيق
  const combinedContent = memoHTML.replace(/<\/body>[\s\S]*?<\/html>/, '') + tabsHTML + `
    <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
      تم إنشاء هذا التقرير المدمج بواسطة نظام إدارة بيانات المتهمين
    </div>
  </body>
  </html>`

  return combinedContent
}

// دالة إنشاء مستند HTML كامل
function generateFullHTMLDocument(content: string, title: string) {
  // إضافة تنسيق خاص للمذكرات
  const styledContent = `
    <div class="memo-container">
      ${content}
    </div>

    <style>
      .memo-container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        max-width: 800px;
        margin: 0 auto;
      }
      .section {
        margin-bottom: 25px;
      }
        .section-title {
          font-size: 18px;
          font-weight: bold;
          color: #2563eb;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
          margin-bottom: 15px;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          padding: 5px 0;
        }
        .info-label {
          font-weight: bold;
          color: #555;
        }
        .info-value {
          color: #333;
        }
        .highlight {
          background-color: #fff3cd;
          padding: 2px 4px;
          border-radius: 3px;
        }
        .important-word {
          background-color: #ffebee;
          color: #c62828;
          padding: 2px 6px;
          border-radius: 3px;
          font-weight: bold;
          margin: 2px;
          display: inline-block;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: right;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        @media print {
          body { background-color: white; }
          .memo-container { box-shadow: none; }
        }
      </style>
    `

  // استخدام نظام القوالب الجديد مع الشعار وبيانات المؤسسة
  try {
    return generateBrandedHTMLDocument(title, styledContent)
  } catch (error) {
    console.error('خطأ في استخدام نظام القوالب الجديد، استخدام النظام القديم:', error)

    // النظام القديم كبديل
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        ${styledContent}
      </head>
      <body>
        <div class="memo-container">
          ${content}
        </div>
      </body>
      </html>
    `
  }
}

// دوال التحميل

// دالة تحميل ملفات متعددة في مجلد مضغوط
async function downloadFiles(files: { name: string, content: string, type: string }[], folderName: string) {
  console.log(`📦 إنشاء مجلد مضغوط: ${folderName}`)

  try {
    // استيراد مكتبة JSZip
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()

    // إضافة الملفات إلى المجلد المضغوط
    for (const file of files) {
      console.log(`📄 إضافة ملف: ${file.name}`)
      zip.file(file.name, file.content)
    }

    // إنشاء المجلد المضغوط
    console.log('🔄 إنشاء المجلد المضغوط...')
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: { level: 6 }
    })

    // تحميل المجلد المضغوط
    const url = URL.createObjectURL(zipBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${folderName}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    console.log(`✅ تم تحميل المجلد المضغوط: ${folderName}.zip`)

  } catch (error) {
    console.error('❌ خطأ في إنشاء المجلد المضغوط:', error)

    // في حالة الفشل، تحميل الملفات منفصلة
    console.log('🔄 تحميل الملفات منفصلة كبديل...')
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      await new Promise(resolve => setTimeout(resolve, 300))

      const blob = new Blob([file.content], { type: file.type + ';charset=utf-8' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `${folderName} - ${file.name}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      console.log(`✅ تم تحميل: ${file.name}`)
    }
  }
}

// دالة تحميل ملف واحد
function downloadSingleFile(content: string, fileName: string, mimeType: string) {
  console.log(`📄 تحميل ملف: ${fileName}`)

  const blob = new Blob([content], { type: mimeType + ';charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  URL.revokeObjectURL(url)
  console.log(`✅ تم تحميل: ${fileName}`)
}

// دالة عرض المذكرة في نافذة جديدة
function displayMemoInNewWindow(content: string) {
  const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')

  if (newWindow) {
    newWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>مذكرة ملخص البحث والمطابقة</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
            color: #333;
          }
          .memo-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
          }
          .memo-header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .memo-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
          }
          .memo-subtitle {
            font-size: 16px;
            color: #666;
          }
          .section {
            margin-bottom: 25px;
          }
          .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
          }
          .info-label {
            font-weight: bold;
            color: #555;
          }
          .info-value {
            color: #333;
          }
          .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
          }
          .important-word {
            background-color: #ffebee;
            color: #c62828;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            margin: 2px;
            display: inline-block;
          }
          .print-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 10px;
          }
          .print-button:hover {
            background-color: #0056b3;
          }
          @media print {
            .print-button { display: none; }
            body { background-color: white; }
            .memo-container { box-shadow: none; }
          }
        </style>
      </head>
      <body>
        <div class="memo-container">
          ${content}
          <div style="text-align: center; margin-top: 30px;">
            <button class="print-button" onclick="window.print()">🖨️ طباعة المذكرة</button>
            <button class="print-button" onclick="window.close()">❌ إغلاق النافذة</button>
          </div>
        </div>
      </body>
      </html>
    `)
    newWindow.document.close()
  } else {
    showMessage('لا يمكن فتح نافذة جديدة. يرجى السماح بالنوافذ المنبثقة.', 'error')
  }
}

// دالة إنشاء بيانات تجريبية للاختبار
function createTestData() {
  const testTabs = [
    {
      name: 'سجل حركة الرسائل النصية المستخرجة من هواتف المتهمين',
      icon: 'fas fa-sms',
      columns: [
        { name: 'م', type: 'number' },
        { name: 'تبعية_البيانات', type: 'number' },
        { name: 'اسم_صاحب_البيانات', type: 'text' },
        { name: 'النوع', type: 'text' },
        { name: 'رقم_جهة_الاتصال', type: 'number' },
        { name: 'محفوظ_بالإسم', type: 'text' },
        { name: '*محتوى_الرسالة', type: 'text' },
        { name: 'التاريخ', type: 'date' },
        { name: 'الوقت', type: 'time' },
        { name: 'الملاحضات', type: 'text' }
      ],
      rows: [
        { data: { 'م': 1, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'النوع': 'صادرة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'صديق العمل', '*محتوى_الرسالة': 'مرحبا كيف الحال؟', 'التاريخ': '2024-01-01', 'الوقت': '10:30', 'الملاحضات': '' } },
        { data: { 'م': 2, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'النوع': 'واردة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'صديق العمل', '*محتوى_الرسالة': 'أهلا وسهلا، الحمد لله بخير', 'التاريخ': '2024-01-01', 'الوقت': '10:35', 'الملاحضات': '' } },
        { data: { 'م': 3, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'النوع': 'صادرة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'صديق العمل', '*محتوى_الرسالة': 'هل يمكن أن نلتقي اليوم؟', 'التاريخ': '2024-01-01', 'الوقت': '11:00', 'الملاحضات': '' } },
        { data: { 'م': 4, 'تبعية_البيانات': 102, 'اسم_صاحب_البيانات': 'محمد أحمد سالم', 'النوع': 'واردة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'الأخ الكبير', '*محتوى_الرسالة': 'نعم، سأكون متاحاً بعد الظهر', 'التاريخ': '2024-01-01', 'الوقت': '11:05', 'الملاحضات': '' } },
        { data: { 'م': 5, 'تبعية_البيانات': 102, 'اسم_صاحب_البيانات': 'محمد أحمد سالم', 'النوع': 'صادرة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'الأخ الكبير', '*محتوى_الرسالة': 'ممتاز، سأراك في المكان المعتاد', 'التاريخ': '2024-01-01', 'الوقت': '11:10', 'الملاحضات': '' } }
      ]
    },
    {
      name: 'سجل حركة الاتصالات المستخرجة من هواتف المتهمين',
      icon: 'fas fa-phone',
      columns: [
        { name: 'م', type: 'number' },
        { name: 'تبعية_البيانات', type: 'number' },
        { name: 'اسم_صاحب_البيانات', type: 'text' },
        { name: 'النوع', type: 'text' },
        { name: 'رقم_جهة_الاتصال', type: 'number' },
        { name: 'محفوظ_بالإسم', type: 'text' },
        { name: 'المدة', type: 'number' },
        { name: 'التاريخ', type: 'date' },
        { name: 'الوقت', type: 'time' },
        { name: 'الملاحضات', type: 'text' }
      ],
      rows: [
        { data: { 'م': 1, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'النوع': 'صادرة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'صديق العمل', 'المدة': 120, 'التاريخ': '2024-01-01', 'الوقت': '09:30', 'الملاحضات': '' } },
        { data: { 'م': 2, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'النوع': 'واردة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'صديق العمل', 'المدة': 85, 'التاريخ': '2024-01-01', 'الوقت': '14:20', 'الملاحضات': '' } },
        { data: { 'م': 3, 'تبعية_البيانات': 102, 'اسم_صاحب_البيانات': 'محمد أحمد سالم', 'النوع': 'صادرة', 'رقم_جهة_الاتصال': '773926647', 'محفوظ_بالإسم': 'الأخ الكبير', 'المدة': 200, 'التاريخ': '2024-01-01', 'الوقت': '16:45', 'الملاحضات': '' } }
      ]
    },
    {
      name: 'سجل حركةجهات الاتصال المستخرجة من هواتف المتهمين',
      icon: 'fas fa-address-book',
      columns: [
        { name: 'م', type: 'number' },
        { name: 'تبعية_البيانات', type: 'number' },
        { name: 'اسم_صاحب_البيانات', type: 'text' },
        { name: 'محفوظ_بالإسم', type: 'text' },
        { name: 'رقم_جهة_الاتصال', type: 'number' }
      ],
      rows: [
        { data: { 'م': 1, 'تبعية_البيانات': 101, 'اسم_صاحب_البيانات': 'أحمد محمد علي', 'محفوظ_بالإسم': 'صديق العمل', 'رقم_جهة_الاتصال': '773926647' } },
        { data: { 'م': 2, 'تبعية_البيانات': 102, 'اسم_صاحب_البيانات': 'محمد أحمد سالم', 'محفوظ_بالإسم': 'الأخ الكبير', 'رقم_جهة_الاتصال': '773926647' } },
        { data: { 'م': 3, 'تبعية_البيانات': 103, 'اسم_صاحب_البيانات': 'سالم محمد أحمد', 'محفوظ_بالإسم': 'زميل الدراسة', 'رقم_جهة_الاتصال': '0501234567' } }
      ]
    },
    {
      name: 'قائمة المحتوى المهم',
      icon: 'fas fa-table',
      columns: [
        { name: 'م', type: 'number' },
        { name: 'الكلمات_المهمة', type: 'text' }
      ],
      rows: [
        { data: { 'م': 1, 'الكلمات_المهمة': 'مخدرات' } },
        { data: { 'م': 2, 'الكلمات_المهمة': 'سلاح' } },
        { data: { 'م': 3, 'الكلمات_المهمة': 'سرقة' } },
        { data: { 'م': 4, 'الكلمات_المهمة': 'تهديد' } },
        { data: { 'م': 5, 'الكلمات_المهمة': 'قتل' } },
        { data: { 'م': 6, 'الكلمات_المهمة': 'اختطاف' } },
        { data: { 'م': 7, 'الكلمات_المهمة': 'احتيال' } },
        { data: { 'م': 8, 'الكلمات_المهمة': 'رشوة' } },
        { data: { 'م': 9, 'الكلمات_المهمة': 'تزوير' } },
        { data: { 'م': 10, 'الكلمات_المهمة': 'إرهاب' } },
        { data: { 'م': 11, 'الكلمات_المهمة': 'مرحبا' } },
        { data: { 'م': 12, 'الكلمات_المهمة': 'أهلا' } }
      ]
    }
  ]

  testTabs.forEach(tabData => {
    const existingTab = databaseStore.getTabByName(tabData.name)
    if (!existingTab) {
      const columns = tabData.columns.map((col, index) => ({
        id: generateId(),
        name: col.name,
        type: col.type as any,
        width: 150,
        isVisible: true,
        isResizable: true,
        order: index + 1,
        formatting: {
          headerTextColor: '#1f2937',
          headerBackgroundColor: '#f3f4f6',
          headerFontSize: 14,
          headerFontWeight: 'bold' as const,
          headerTextAlign: 'right' as const,
          cellTextColor: '#374151',
          cellBackgroundColor: '#ffffff',
          cellFontSize: 13,
          cellFontWeight: 'normal' as const,
          cellTextAlign: 'right' as const,
          cellTextWrap: false,
          cellFitContent: false,
          sortable: true,
          borderColor: '#e5e7eb',
          borderWidth: 1,
          borderStyle: 'solid' as const
        }
      }))

      const newTab = databaseStore.addTab({
        name: tabData.name,
        icon: tabData.icon,
        columns,
        rows: [],
        settings: {
          showCheckboxes: true,
          showRowNumbers: true,
          alternateRowColors: true,
          pageSize: 50,
          currentPage: 1,
          sortDirection: 'asc' as const,
          filters: {},
          searchQuery: '',
          searchColumns: [],
          exportSettings: {
            includeHeaders: true,
            includeGroupHeaders: true,
            includeHiddenColumns: false,
            preserveFormatting: true,
            paperSize: 'A4' as const,
            orientation: 'portrait' as const,
            margins: { top: 20, right: 20, bottom: 20, left: 20 }
          }
        },
        groupHeaders: {
          enabled: true,
          identifier: 'سجل حركة',
          textColor: '#1e40af',
          backgroundColor: '#dbeafe',
          fontSize: 16,
          fontWeight: 'bold' as const,
          textAlign: 'right' as const,
          colSpan: true
        },
        createdAt: new Date(),
        updatedAt: new Date()
      })

      // إضافة الصفوف
      tabData.rows.forEach(rowData => {
        databaseStore.addRow(newTab.id, rowData.data)
      })
    }
  })
}

// دالة لحذف جميع البيانات وإعادة إنشائها
function resetTestData() {
  // حذف جميع التبويبات
  const allTabs = [...tabs.value]
  allTabs.forEach(tab => {
    databaseStore.removeTab(tab.id)
  })

  // إنشاء بيانات جديدة
  createTestData()

  console.log('🔄 تم إعادة تعيين البيانات التجريبية')
}

// إضافة الدالة إلى window للوصول إليها من Console
;(window as any).resetTestData = resetTestData
;(window as any).debugExistingTabs = debugExistingTabs

// Lifecycle
onMounted(async () => {
  await databaseStore.loadTabs()

  // إنشاء بيانات تجريبية إذا لم تكن موجودة
  if (tabs.value.length === 0) {
    createTestData()
  }

  if (tabs.value.length > 0) {
    activeTab.value = tabs.value[0].id
  }

  console.log('🚀 تم تحميل قسم قاعدة البيانات')
  console.log('💡 يمكنك استخدام resetTestData() في Console لإعادة تعيين البيانات')
  console.log('💡 يمكنك استخدام debugExistingTabs() في Console لفحص التبويبات')
})
</script>
