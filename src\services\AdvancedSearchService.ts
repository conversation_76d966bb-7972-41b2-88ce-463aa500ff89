/**
 * خدمة البحث المتقدم للبيانات الضخمة
 * تستخدم تقنيات محسنة للبحث السريع والدقيق
 */

import { electronDatabaseService } from './ElectronDatabaseService'
import Fuse from 'fuse.js'

export interface SearchOptions {
  query: string
  fields?: string[]
  limit?: number
  threshold?: number
  includeScore?: boolean
  sortBy?: 'relevance' | 'date' | 'name'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchResult<T = any> {
  item: T
  score?: number
  matches?: any[]
}

export interface SearchStats {
  totalResults: number
  searchTime: number
  method: 'fts' | 'fuzzy' | 'exact'
  query: string
}

export class AdvancedSearchService {
  private fuseInstances: Map<string, Fuse<any>> = new Map()
  private searchCache: Map<string, { results: any[], timestamp: number }> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 دقائق

  /**
   * البحث الرئيسي - يختار أفضل استراتيجية بحث
   */
  async search<T = any>(
    dataset: string,
    options: SearchOptions
  ): Promise<{ results: SearchResult<T>[], stats: SearchStats }> {
    const startTime = performance.now()
    
    try {
      // التحقق من الكاش أولاً
      const cacheKey = this.generateCacheKey(dataset, options)
      const cached = this.getCachedResults(cacheKey)
      
      if (cached) {
        return {
          results: cached.results,
          stats: {
            totalResults: cached.results.length,
            searchTime: performance.now() - startTime,
            method: 'exact',
            query: options.query
          }
        }
      }

      let results: SearchResult<T>[] = []
      let method: 'fts' | 'fuzzy' | 'exact' = 'exact'

      // اختيار استراتيجية البحث
      if (electronDatabaseService.isAvailable() && this.shouldUseFTS(options.query)) {
        // استخدام Full-Text Search في SQLite
        results = await this.performFTSSearch(dataset, options)
        method = 'fts'
      } else if (this.shouldUseFuzzySearch(options.query)) {
        // استخدام Fuzzy Search للبحث الذكي
        results = await this.performFuzzySearch(dataset, options)
        method = 'fuzzy'
      } else {
        // البحث الدقيق التقليدي
        results = await this.performExactSearch(dataset, options)
        method = 'exact'
      }

      // ترتيب النتائج
      results = this.sortResults(results, options)

      // تطبيق الحد الأقصى للنتائج
      if (options.limit && options.limit > 0) {
        results = results.slice(0, options.limit)
      }

      // حفظ في الكاش
      this.setCachedResults(cacheKey, results)

      const searchTime = performance.now() - startTime

      return {
        results,
        stats: {
          totalResults: results.length,
          searchTime,
          method,
          query: options.query
        }
      }
    } catch (error) {
      console.error('Advanced search failed:', error)
      throw error
    }
  }

  /**
   * البحث باستخدام Full-Text Search في SQLite
   */
  private async performFTSSearch<T>(
    dataset: string,
    options: SearchOptions
  ): Promise<SearchResult<T>[]> {
    try {
      const results = await electronDatabaseService.fullTextSearch(
        dataset,
        this.prepareFTSQuery(options.query),
        options.limit || 1000
      )

      return results.map(item => ({
        item,
        score: 1.0 // SQLite FTS لا يعطي نقاط مفصلة
      }))
    } catch (error) {
      console.error('FTS search failed:', error)
      return []
    }
  }

  /**
   * البحث الضبابي باستخدام Fuse.js
   */
  private async performFuzzySearch<T>(
    dataset: string,
    options: SearchOptions
  ): Promise<SearchResult<T>[]> {
    try {
      // الحصول على البيانات
      const data = await this.getDataset(dataset)
      
      // إنشاء أو الحصول على Fuse instance
      const fuse = this.getFuseInstance(dataset, data, options)
      
      // تنفيذ البحث
      const fuseResults = fuse.search(options.query)
      
      return fuseResults.map(result => ({
        item: result.item,
        score: result.score,
        matches: result.matches
      }))
    } catch (error) {
      console.error('Fuzzy search failed:', error)
      return []
    }
  }

  /**
   * البحث الدقيق التقليدي
   */
  private async performExactSearch<T>(
    dataset: string,
    options: SearchOptions
  ): Promise<SearchResult<T>[]> {
    try {
      const data = await this.getDataset(dataset)
      const query = options.query.toLowerCase()
      
      const results = data.filter((item: any) => {
        if (options.fields && options.fields.length > 0) {
          // البحث في حقول محددة
          return options.fields.some(field => {
            const value = this.getNestedValue(item, field)
            return value && value.toString().toLowerCase().includes(query)
          })
        } else {
          // البحث في جميع الحقول
          return this.searchInAllFields(item, query)
        }
      })

      return results.map((item: T) => ({ item, score: 1.0 }))
    } catch (error) {
      console.error('Exact search failed:', error)
      return []
    }
  }

  /**
   * الحصول على مجموعة البيانات
   */
  private async getDataset(dataset: string): Promise<any[]> {
    switch (dataset) {
      case 'suspects':
        if (electronDatabaseService.isAvailable()) {
          return await electronDatabaseService.getAllSuspects()
        }
        // fallback to IndexedDB
        return []
      
      case 'database_tabs':
        if (electronDatabaseService.isAvailable()) {
          return await electronDatabaseService.getAllDatabaseTabs()
        }
        return []
      
      default:
        throw new Error(`Unknown dataset: ${dataset}`)
    }
  }

  /**
   * إنشاء أو الحصول على Fuse instance
   */
  private getFuseInstance(dataset: string, data: any[], options: SearchOptions): Fuse<any> {
    const key = `${dataset}_${JSON.stringify(options.fields || [])}`
    
    if (!this.fuseInstances.has(key)) {
      const fuseOptions: Fuse.IFuseOptions<any> = {
        includeScore: options.includeScore !== false,
        includeMatches: true,
        threshold: options.threshold || 0.3,
        keys: options.fields || this.getDefaultSearchFields(dataset),
        minMatchCharLength: 2,
        ignoreLocation: true,
        findAllMatches: true
      }
      
      const fuse = new Fuse(data, fuseOptions)
      this.fuseInstances.set(key, fuse)
    }
    
    return this.fuseInstances.get(key)!
  }

  /**
   * الحصول على الحقول الافتراضية للبحث
   */
  private getDefaultSearchFields(dataset: string): string[] {
    switch (dataset) {
      case 'suspects':
        return ['data.fields.name', 'data.fields.nationalId', 'data.fields.phone']
      case 'database_tabs':
        return ['name', 'rows']
      default:
        return []
    }
  }

  /**
   * تحديد ما إذا كان يجب استخدام FTS
   */
  private shouldUseFTS(query: string): boolean {
    // استخدام FTS للاستعلامات الطويلة أو المعقدة
    return query.length >= 3 && (
      query.includes(' ') || // استعلامات متعددة الكلمات
      query.includes('"') || // البحث بعلامات اقتباس
      query.includes('*') || // البحث بـ wildcards
      query.includes('AND') || query.includes('OR') // العمليات المنطقية
    )
  }

  /**
   * تحديد ما إذا كان يجب استخدام Fuzzy Search
   */
  private shouldUseFuzzySearch(query: string): boolean {
    // استخدام Fuzzy Search للاستعلامات القصيرة أو التي قد تحتوي على أخطاء إملائية
    return query.length >= 2 && query.length <= 10
  }

  /**
   * تحضير استعلام FTS
   */
  private prepareFTSQuery(query: string): string {
    // تنظيف وتحضير الاستعلام لـ SQLite FTS
    let ftsQuery = query.trim()
    
    // إضافة wildcards للبحث الجزئي
    if (!ftsQuery.includes('*') && !ftsQuery.includes('"')) {
      ftsQuery = ftsQuery.split(' ').map(term => `${term}*`).join(' ')
    }
    
    return ftsQuery
  }

  /**
   * ترتيب النتائج
   */
  private sortResults<T>(results: SearchResult<T>[], options: SearchOptions): SearchResult<T>[] {
    if (!options.sortBy || options.sortBy === 'relevance') {
      // ترتيب حسب النقاط (الأعلى أولاً)
      return results.sort((a, b) => (b.score || 0) - (a.score || 0))
    }
    
    // ترتيب مخصص
    return results.sort((a, b) => {
      let aValue: any, bValue: any
      
      switch (options.sortBy) {
        case 'date':
          aValue = new Date(a.item.created_at || a.item.createdAt || 0)
          bValue = new Date(b.item.created_at || b.item.createdAt || 0)
          break
        case 'name':
          aValue = a.item.name || a.item.data?.fields?.name || ''
          bValue = b.item.name || b.item.data?.fields?.name || ''
          break
        default:
          return 0
      }
      
      if (options.sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      }
    })
  }

  /**
   * البحث في جميع حقول الكائن
   */
  private searchInAllFields(obj: any, query: string): boolean {
    if (typeof obj === 'string') {
      return obj.toLowerCase().includes(query)
    }
    
    if (typeof obj === 'number') {
      return obj.toString().includes(query)
    }
    
    if (Array.isArray(obj)) {
      return obj.some(item => this.searchInAllFields(item, query))
    }
    
    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(value => this.searchInAllFields(value, query))
    }
    
    return false
  }

  /**
   * الحصول على قيمة متداخلة من كائن
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * إنشاء مفتاح الكاش
   */
  private generateCacheKey(dataset: string, options: SearchOptions): string {
    return `${dataset}_${JSON.stringify(options)}`
  }

  /**
   * الحصول على النتائج المحفوظة في الكاش
   */
  private getCachedResults(key: string): { results: any[] } | null {
    const cached = this.searchCache.get(key)
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached
    }
    
    // إزالة الكاش المنتهي الصلاحية
    this.searchCache.delete(key)
    return null
  }

  /**
   * حفظ النتائج في الكاش
   */
  private setCachedResults(key: string, results: any[]): void {
    this.searchCache.set(key, {
      results,
      timestamp: Date.now()
    })
    
    // تنظيف الكاش القديم
    this.cleanupCache()
  }

  /**
   * تنظيف الكاش القديم
   */
  private cleanupCache(): void {
    const now = Date.now()
    
    for (const [key, value] of this.searchCache.entries()) {
      if (now - value.timestamp >= this.CACHE_DURATION) {
        this.searchCache.delete(key)
      }
    }
  }

  /**
   * مسح جميع الكاش
   */
  clearCache(): void {
    this.searchCache.clear()
    this.fuseInstances.clear()
  }
}

// إنشاء instance واحد
export const advancedSearchService = new AdvancedSearchService()
