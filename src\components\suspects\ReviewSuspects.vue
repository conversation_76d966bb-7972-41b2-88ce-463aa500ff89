<template>
  <div class="space-y-8">
    <!-- Statistics Summary -->
    <div class="neumorphic-card">
      <div class="flex items-center gap-3 mb-6">
        <div class="neumorphic-icon">
          <i class="fas fa-chart-bar text-primary-600"></i>
        </div>
        <h2 class="text-xl font-bold text-secondary-800">ملخص الإحصائيات</h2>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="neumorphic-card bg-white p-4 text-center">
          <div class="text-2xl font-bold text-primary-600 mb-1">{{ suspects.length }}</div>
          <div class="text-sm text-secondary-600">إجمالي المتهمين</div>
        </div>
        <div class="neumorphic-card bg-white p-4 text-center">
          <div class="text-2xl font-bold text-danger-600 mb-1">{{ detainedCount }}</div>
          <div class="text-sm text-secondary-600">رهن الاعتقال</div>
          <div class="text-xs text-secondary-500">{{ detainedPercentage }}%</div>
        </div>
        <div class="neumorphic-card bg-white p-4 text-center">
          <div class="text-2xl font-bold text-success-600 mb-1">{{ releasedCount }}</div>
          <div class="text-sm text-secondary-600">مفرج عنهم</div>
          <div class="text-xs text-secondary-500">{{ releasedPercentage }}%</div>
        </div>
        <div class="neumorphic-card bg-white p-4 text-center">
          <div class="text-2xl font-bold text-blue-600 mb-1">{{ transferredCount }}</div>
          <div class="text-sm text-secondary-600">محالين للنيابة</div>
          <div class="text-xs text-secondary-500">{{ transferredPercentage }}%</div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="neumorphic-card">
      <div class="flex flex-col md:flex-row gap-4">
        <!-- Search -->
        <div class="flex-1">
          <div class="relative">
            <input
              :value="searchQuery"
              @input="$emit('updateSearch', $event.target.value)"
              type="text"
              class="neumorphic-input w-full pl-10"
              placeholder="البحث في جميع البيانات..."
            />
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400"></i>
          </div>
        </div>
        
        <!-- Status Filter -->
        <div class="flex gap-2">
          <button
            v-for="filter in statusFilters"
            :key="filter.value"
            @click="$emit('updateStatusFilter', filter.value)"
            :class="[
              'neumorphic-button px-4 py-2 text-sm transition-all duration-300',
              statusFilter === filter.value 
                ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' 
                : ''
            ]"
          >
            <i :class="filter.icon + ' ml-1'"></i>
            {{ filter.label }}
            <span class="bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full text-xs mr-2">
              {{ filter.count }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Data Tables -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
      <!-- Suspects Data Table -->
      <div class="xl:col-span-2">
        <div class="neumorphic-card">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-bold text-secondary-800">بيانات المتهمين</h3>
            <div class="flex gap-2">
              <button
                @click="exportData('excel')"
                class="neumorphic-button p-2 text-sm text-success-600"
                title="تصدير Excel"
              >
                <i class="fas fa-file-excel"></i>
              </button>
              <button
                @click="exportData('pdf')"
                class="neumorphic-button p-2 text-sm text-danger-600"
                title="تصدير PDF"
              >
                <i class="fas fa-file-pdf"></i>
              </button>
              <button
                @click="printData"
                class="neumorphic-button p-2 text-sm text-primary-600"
                title="طباعة"
              >
                <i class="fas fa-print"></i>
              </button>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b border-secondary-200">
                  <th
                    v-for="field in visibleTableFields"
                    :key="field.id"
                    class="text-right p-3 text-sm font-medium text-secondary-700 bg-secondary-50"
                  >
                    <div class="flex items-center gap-2">
                      <i :class="field.icon"></i>
                      {{ field.label }}
                    </div>
                  </th>
                  <th class="text-right p-3 text-sm font-medium text-secondary-700 bg-secondary-50">
                    العمليات
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="suspect in paginatedSuspects"
                  :key="suspect.id"
                  :class="[
                    'border-b border-secondary-100 hover:bg-secondary-50 transition-colors',
                    getRowStatusClass(suspect)
                  ]"
                >
                  <td
                    v-for="field in visibleTableFields"
                    :key="field.id"
                    class="p-3 text-sm"
                  >
                    <div v-if="field.inputType === 'image' && suspect.fields[field.id!]">
                      <img
                        :src="getImageUrl(suspect, field.id!)"
                        :alt="field.label"
                        class="w-12 h-12 rounded-neumorphic object-cover"
                      />
                    </div>
                    <div v-else-if="field.inputType === 'date' && suspect.fields[field.id!]">
                      {{ formatDate(suspect.fields[field.id!]) }}
                    </div>
                    <div v-else class="max-w-32 truncate" :title="suspect.fields[field.id!]">
                      {{ suspect.fields[field.id!] || '-' }}
                    </div>
                  </td>
                  <td class="p-3">
                    <div class="flex gap-1">
                      <button
                        @click="editSuspect(suspect)"
                        class="neumorphic-button p-1 text-xs text-primary-600"
                        title="تعديل"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        @click="viewSuspect(suspect)"
                        class="neumorphic-button p-1 text-xs text-blue-600"
                        title="عرض"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                      <button
                        @click="deleteSuspect(suspect)"
                        class="neumorphic-button p-1 text-xs text-danger-600"
                        title="حذف"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 pt-6 border-t border-secondary-200">
            <div class="text-sm text-secondary-600">
              عرض {{ startIndex + 1 }} - {{ endIndex }} من {{ suspects.length }} متهم
            </div>
            <div class="flex gap-2">
              <button
                @click="currentPage--"
                :disabled="currentPage === 1"
                class="neumorphic-button p-2 text-sm"
                :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''"
              >
                <i class="fas fa-chevron-right"></i>
              </button>
              <span class="neumorphic-button p-2 text-sm bg-primary-100 text-primary-700">
                {{ currentPage }} / {{ totalPages }}
              </span>
              <button
                @click="currentPage++"
                :disabled="currentPage === totalPages"
                class="neumorphic-button p-2 text-sm"
                :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''"
              >
                <i class="fas fa-chevron-left"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Table -->
      <div class="xl:col-span-1">
        <div class="neumorphic-card">
          <h3 class="text-lg font-bold text-secondary-800 mb-6">الوضع الحالي</h3>
          
          <div class="space-y-4">
            <div
              v-for="suspect in paginatedSuspects"
              :key="suspect.id"
              :class="[
                'neumorphic-card bg-white p-4 transition-all duration-300',
                getRowStatusClass(suspect)
              ]"
            >
              <div class="flex items-center justify-between mb-3">
                <div class="font-medium text-secondary-800 truncate">
                  {{ getSuspectName(suspect) }}
                </div>
                <div class="text-xs text-secondary-500">
                  #{{ suspect.fields.fileNumber }}
                </div>
              </div>

              <div class="grid grid-cols-3 gap-2 text-xs">
                <!-- Released -->
                <label class="flex flex-col items-center gap-1 cursor-pointer">
                  <input
                    type="checkbox"
                    :checked="suspect.fields.isReleased"
                    @change="updateStatus(suspect, 'released', $event.target.checked)"
                    class="hidden"
                  />
                  <div :class="[
                    'w-4 h-4 rounded border-2 flex items-center justify-center transition-all',
                    suspect.fields.isReleased 
                      ? 'bg-success-500 border-success-500 text-white' 
                      : 'border-secondary-300'
                  ]">
                    <i v-if="suspect.fields.isReleased" class="fas fa-check text-xs"></i>
                  </div>
                  <span class="text-center">مفرج</span>
                  <input
                    v-if="suspect.fields.isReleased"
                    type="date"
                    :value="formatDateInput(suspect.fields.releaseDate)"
                    @change="updateReleaseDate(suspect, $event.target.value)"
                    class="neumorphic-input text-xs p-1 w-full"
                  />
                </label>

                <!-- Transferred -->
                <label class="flex flex-col items-center gap-1 cursor-pointer">
                  <input
                    type="checkbox"
                    :checked="suspect.fields.isTransferred"
                    @change="updateStatus(suspect, 'transferred', $event.target.checked)"
                    class="hidden"
                  />
                  <div :class="[
                    'w-4 h-4 rounded border-2 flex items-center justify-center transition-all',
                    suspect.fields.isTransferred 
                      ? 'bg-blue-500 border-blue-500 text-white' 
                      : 'border-secondary-300'
                  ]">
                    <i v-if="suspect.fields.isTransferred" class="fas fa-check text-xs"></i>
                  </div>
                  <span class="text-center">محال للنيابة</span>
                  <input
                    v-if="suspect.fields.isTransferred"
                    type="date"
                    :value="formatDateInput(suspect.fields.transferDate)"
                    @change="updateTransferDate(suspect, $event.target.value)"
                    class="neumorphic-input text-xs p-1 w-full"
                  />
                </label>

                <!-- Detained -->
                <div class="flex flex-col items-center gap-1">
                  <div :class="[
                    'w-4 h-4 rounded border-2 flex items-center justify-center',
                    !suspect.fields.isReleased && !suspect.fields.isTransferred
                      ? 'bg-danger-500 border-danger-500 text-white' 
                      : 'border-secondary-300'
                  ]">
                    <i v-if="!suspect.fields.isReleased && !suspect.fields.isTransferred" class="fas fa-check text-xs"></i>
                  </div>
                  <span class="text-center">رهن الاعتقال</span>
                  <div v-if="!suspect.fields.isReleased && !suspect.fields.isTransferred" class="text-center">
                    <div class="font-bold text-danger-600">{{ getDaysDetained(suspect) }}</div>
                    <div class="text-xs">يوم</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SuspectData, SuspectField } from '@/types'

// Props
interface Props {
  suspects: SuspectData[]
  fields: SuspectField[]
  loading: boolean
  searchQuery: string
  statusFilter: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  updateSearch: [query: string]
  updateStatusFilter: [filter: string]
  updateSuspect: [id: string, updates: Partial<SuspectData>]
  deleteSuspect: [id: string]
  updateSuspectStatus: [id: string, status: 'released' | 'transferred', date?: Date]
}>()

// Reactive data
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed
const visibleTableFields = computed(() => {
  return props.fields
    .filter(field => field.isVisible && field.inputType !== 'file')
    .sort((a, b) => a.order - b.order)
    .slice(0, 6) // Limit to 6 columns for better display
})

const detainedCount = computed(() =>
  props.suspects.filter(s => !s.fields.isReleased && !s.fields.isTransferred).length
)

const releasedCount = computed(() =>
  props.suspects.filter(s => s.fields.isReleased).length
)

const transferredCount = computed(() =>
  props.suspects.filter(s => s.fields.isTransferred).length
)

const detainedPercentage = computed(() =>
  props.suspects.length > 0 ? Math.round((detainedCount.value / props.suspects.length) * 100) : 0
)

const releasedPercentage = computed(() =>
  props.suspects.length > 0 ? Math.round((releasedCount.value / props.suspects.length) * 100) : 0
)

const transferredPercentage = computed(() =>
  props.suspects.length > 0 ? Math.round((transferredCount.value / props.suspects.length) * 100) : 0
)

const statusFilters = computed(() => [
  {
    value: 'all',
    label: 'الكل',
    icon: 'fas fa-list',
    count: props.suspects.length
  },
  {
    value: 'detained',
    label: 'معتقل',
    icon: 'fas fa-lock',
    count: detainedCount.value
  },
  {
    value: 'released',
    label: 'مفرج',
    icon: 'fas fa-unlock',
    count: releasedCount.value
  },
  {
    value: 'transferred',
    label: 'محال',
    icon: 'fas fa-exchange-alt',
    count: transferredCount.value
  }
])

const totalPages = computed(() => Math.ceil(props.suspects.length / itemsPerPage.value))

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
const endIndex = computed(() => Math.min(startIndex.value + itemsPerPage.value, props.suspects.length))

const paginatedSuspects = computed(() => {
  return props.suspects.slice(startIndex.value, endIndex.value)
})

// Methods
function getRowStatusClass(suspect: SuspectData): string {
  if (suspect.fields.isReleased) return 'bg-success-50 border-l-4 border-success-400'
  if (suspect.fields.isTransferred) return 'bg-blue-50 border-l-4 border-blue-400'
  return 'bg-danger-50 border-l-4 border-danger-400'
}

function getImageUrl(suspect: SuspectData, fieldId: string): string {
  // In a real app, this would return the actual image URL
  const attachment = suspect.attachments.find(att => att.fieldId === fieldId)
  return attachment?.filePath || '/placeholder-image.png'
}

function formatDate(date: any): string {
  if (!date) return '-'
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

function formatDateInput(date: any): string {
  if (!date) return ''
  return new Date(date).toISOString().split('T')[0]
}

function getDaysDetained(suspect: SuspectData): number {
  // Find the arrest date field (order 12)
  const arrestDateField = props.fields.find(field => field.order === 12)
  let arrestDate: Date

  if (arrestDateField && suspect.fields[arrestDateField.id!]) {
    arrestDate = new Date(suspect.fields[arrestDateField.id!])
  } else {
    // Fallback to creation date if no arrest date
    arrestDate = new Date(suspect.createdAt)
  }

  const now = new Date()
  const diffTime = Math.abs(now.getTime() - arrestDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

function updateStatus(suspect: SuspectData, status: 'released' | 'transferred', checked: boolean) {
  if (checked) {
    emit('updateSuspectStatus', suspect.id!, status, new Date())
  } else {
    // Reset status
    const updates: any = {
      fields: { ...suspect.fields }
    }

    if (status === 'released') {
      updates.fields.isReleased = false
      updates.fields.releaseDate = null
    } else if (status === 'transferred') {
      updates.fields.isTransferred = false
      updates.fields.transferDate = null
    }

    emit('updateSuspect', suspect.id!, updates)
  }
}

function updateReleaseDate(suspect: SuspectData, dateString: string) {
  const updates = {
    fields: {
      ...suspect.fields,
      releaseDate: new Date(dateString)
    }
  }
  emit('updateSuspect', suspect.id!, updates)
}

function updateTransferDate(suspect: SuspectData, dateString: string) {
  const updates = {
    fields: {
      ...suspect.fields,
      transferDate: new Date(dateString)
    }
  }
  emit('updateSuspect', suspect.id!, updates)
}

function editSuspect(suspect: SuspectData) {
  // This would open an edit modal or navigate to edit page
  console.log('Edit suspect:', suspect.id)
}

function viewSuspect(suspect: SuspectData) {
  // This would open a view modal or navigate to detail page
  console.log('View suspect:', suspect.id)
}

function getSuspectName(suspect: SuspectData): string {
  // Try different ways to get the name
  if (suspect.fields.fullName) {
    return suspect.fields.fullName
  }

  // Find the name field by order (should be order 2)
  const nameField = props.fields.find(field => field.order === 2)
  if (nameField && suspect.fields[nameField.id!]) {
    return suspect.fields[nameField.id!]
  }

  // Try common field names
  const possibleNameFields = ['fullName', 'name', 'الاسم_الرباعي', 'اسم_المتهم']
  for (const fieldName of possibleNameFields) {
    if (suspect.fields[fieldName]) {
      return suspect.fields[fieldName]
    }
  }

  // Find any field with "اسم" in the label
  const nameFieldByLabel = props.fields.find(field =>
    field.label.includes('اسم') || field.label.includes('الاسم')
  )
  if (nameFieldByLabel && suspect.fields[nameFieldByLabel.id!]) {
    return suspect.fields[nameFieldByLabel.id!]
  }

  return 'غير محدد'
}

function deleteSuspect(suspect: SuspectData) {
  if (confirm(`هل أنت متأكد من حذف المتهم "${getSuspectName(suspect)}"؟`)) {
    emit('deleteSuspect', suspect.id!)
  }
}

async function exportData(format: 'excel' | 'pdf') {
  try {
    const exportData = {
      suspects: props.suspects,
      fields: props.fields
    }

    if (format === 'excel') {
      const { exportToExcel } = await import('@/utils/export')
      exportToExcel(exportData)
    } else if (format === 'pdf') {
      const { exportToPDF } = await import('@/utils/export')
      exportToPDF(exportData)
    }
  } catch (error) {
    console.error('Error exporting data:', error)
    alert('حدث خطأ أثناء التصدير')
  }
}

function printData() {
  // This would trigger print functionality
  window.print()
}
</script>
