<template>
  <div class="neumorphic-card">
    <div class="flex items-center gap-3 mb-6">
      <div class="neumorphic-icon">
        <i class="fas fa-user-plus text-primary-600"></i>
      </div>
      <div>
        <h2 class="text-xl font-bold text-secondary-800">
          {{ props.editingSuspect ? 'تعديل بيانات المتهم' : 'إضافة بيانات المتهم' }}
        </h2>
        <p class="text-secondary-600">
          {{ props.editingSuspect ? 'تحديث بيانات المتهم الموجود' : 'تسجيل بيانات متهم جديد في النظام' }}
        </p>
      </div>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Dynamic Fields based on Settings -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="field in visibleFields"
          :key="field.id"
          :class="getFieldColumnSpan(field)"
        >
          <!-- Text Input -->
          <div v-if="field.inputType === 'text'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <input
              v-model="formData[field.id!]"
              type="text"
              :class="[
                'neumorphic-input w-full',
                errors[field.id!] ? 'border-danger-300' : ''
              ]"
              :placeholder="getFieldPlaceholder(field)"
              :required="field.isRequired"
              @blur="validateField(field)"
            />
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>

          <!-- Textarea -->
          <div v-if="field.inputType === 'textarea'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <textarea
              v-model="formData[field.id!]"
              :class="[
                'neumorphic-input w-full h-24 resize-none',
                errors[field.id!] ? 'border-danger-300' : ''
              ]"
              :placeholder="getFieldPlaceholder(field)"
              :required="field.isRequired"
              @blur="validateField(field)"
            ></textarea>
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>

          <!-- Date Input -->
          <div v-if="field.inputType === 'date'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <div class="relative">
              <input
                v-model="formData[field.id!]"
                type="date"
                :class="[
                  'neumorphic-input w-full',
                  errors[field.id!] ? 'border-danger-300' : ''
                ]"
                :required="field.isRequired"
                @blur="validateField(field)"
              />
              <i class="fas fa-calendar-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 pointer-events-none"></i>
            </div>
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>

          <!-- Select Input -->
          <div v-if="field.inputType === 'select'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <select
              v-model="formData[field.id!]"
              :class="[
                'neumorphic-select w-full',
                errors[field.id!] ? 'border-danger-300' : ''
              ]"
              :required="field.isRequired"
              @blur="validateField(field)"
            >
              <option value="">اختر {{ field.label }}</option>
              <option
                v-for="option in field.options"
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>

          <!-- Image Upload -->
          <div v-if="field.inputType === 'image'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <div class="neumorphic-card bg-white p-4">
              <div
                @click="triggerFileUpload(field.id!)"
                @dragover.prevent
                @drop.prevent="handleFileDrop($event, field)"
                class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-6 text-center cursor-pointer hover:border-primary-400 transition-colors"
              >
                <div v-if="formData[field.id!]" class="mb-4">
                  <img 
                    :src="getImagePreview(field.id!)" 
                    alt="Preview" 
                    class="max-w-32 max-h-32 mx-auto rounded-neumorphic object-cover"
                  />
                  <p class="text-sm text-secondary-600 mt-2">{{ getFileName(field.id!) }}</p>
                </div>
                <div v-else class="text-secondary-500">
                  <i class="fas fa-camera text-2xl mb-2"></i>
                  <p>اضغط أو اسحب الصورة هنا</p>
                  <p class="text-sm">JPG, PNG, GIF (حد أقصى {{ getMaxFileSize(field) }})</p>
                </div>
              </div>
              <input
                :ref="el => fileInputs[field.id!] = el"
                type="file"
                :accept="getAcceptedFileTypes(field)"
                @change="handleFileUpload($event, field)"
                class="hidden"
              />
            </div>
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>

          <!-- File Upload -->
          <div v-if="field.inputType === 'file'">
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i :class="field.icon + ' ml-1'"></i>
              {{ field.label }}
              <span v-if="field.isRequired" class="text-danger-500">*</span>
            </label>
            <div class="neumorphic-card bg-white p-4">
              <div
                @click="triggerFileUpload(field.id!)"
                @dragover.prevent
                @drop.prevent="handleFileDrop($event, field)"
                class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-4 text-center cursor-pointer hover:border-primary-400 transition-colors"
              >
                <div v-if="formData[field.id!]" class="flex items-center gap-3">
                  <i class="fas fa-file text-2xl text-primary-600"></i>
                  <div class="text-right">
                    <p class="font-medium text-secondary-800">{{ getFileName(field.id!) }}</p>
                    <p class="text-sm text-secondary-600">{{ getFileSize(field.id!) }}</p>
                  </div>
                </div>
                <div v-else class="text-secondary-500">
                  <i class="fas fa-upload text-xl mb-2"></i>
                  <p>اضغط أو اسحب الملف هنا</p>
                  <p class="text-sm">{{ getAcceptedFileTypes(field) }} (حد أقصى {{ getMaxFileSize(field) }})</p>
                </div>
              </div>
              <input
                :ref="el => fileInputs[field.id!] = el"
                type="file"
                :accept="getAcceptedFileTypes(field)"
                @change="handleFileUpload($event, field)"
                class="hidden"
              />
            </div>
            <p v-if="errors[field.id!]" class="form-error">{{ errors[field.id!] }}</p>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end gap-4 pt-6 border-t border-secondary-200">
        <button
          type="button"
          @click="resetForm"
          class="neumorphic-button text-secondary-600 hover:text-secondary-700"
        >
          <i class="fas fa-undo ml-2"></i>
          إعادة تعيين
        </button>
        <button
          type="button"
          @click="saveAsDraft"
          class="neumorphic-button text-warning-600 hover:text-warning-700"
        >
          <i class="fas fa-save ml-2"></i>
          حفظ كمسودة
        </button>
        <button
          type="submit"
          class="neumorphic-button text-success-600 hover:text-success-700"
          :disabled="loading || !isFormValid"
        >
          <i :class="props.editingSuspect ? 'fas fa-save ml-2' : 'fas fa-plus ml-2'"></i>
          {{ props.editingSuspect ? 'حفظ التعديلات' : 'إضافة المتهم' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch } from 'vue'
import type { SuspectField, SuspectData, SuspectAttachment } from '@/types'

// Props
interface Props {
  fields: SuspectField[]
  loading: boolean
  editingSuspect?: SuspectData | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  addSuspect: [suspect: Omit<SuspectData, 'id' | 'createdAt' | 'updatedAt'>]
  clearEditing: []
}>()

// Reactive data
const formData = reactive<Record<string, any>>({})
const errors = reactive<Record<string, string>>({})
const fileInputs = ref<Record<string, HTMLInputElement>>({})
const uploadedFiles = ref<Record<string, File>>({})

// Computed
const visibleFields = computed(() => {
  return props.fields
    .filter(field => field.isVisible)
    .sort((a, b) => a.order - b.order)
})

const isFormValid = computed(() => {
  // Check required fields
  const requiredFields = visibleFields.value.filter(field => field.isRequired)
  return requiredFields.every(field => {
    const value = formData[field.id!]
    return value !== undefined && value !== null && value !== ''
  }) && Object.keys(errors).length === 0
})

// Methods
function getFieldColumnSpan(field: SuspectField): string {
  if (field.inputType === 'textarea') return 'md:col-span-2'
  if (field.inputType === 'image' || field.inputType === 'file') return 'md:col-span-2 lg:col-span-1'
  return ''
}

function getFieldPlaceholder(field: SuspectField): string {
  const placeholders: Record<string, string> = {
    'fileNumber': 'سيتم إنشاؤه تلقائياً',
    'fullName': 'الاسم الرباعي كاملاً',
    'idNumber': 'رقم الهوية الوطنية',
    'address': 'العنوان الكامل',
    'phone': '+966xxxxxxxxx',
    'age': 'العمر بالسنوات',
    'profession': 'المهنة أو الوظيفة',
    'seizures': 'وصف المضبوطات',
    'notes': 'ملاحظات إضافية'
  }
  return placeholders[field.id!] || `أدخل ${field.label}`
}

function validateField(field: SuspectField) {
  const value = formData[field.id!]
  const fieldId = field.id!

  // Clear previous error
  delete errors[fieldId]

  // Required validation
  if (field.isRequired && (!value || value.toString().trim() === '')) {
    errors[fieldId] = `${field.label} مطلوب`
    return
  }

  // Skip validation if field is empty and not required
  if (!value || value.toString().trim() === '') return

  // Validation rules
  if (field.validation) {
    const validation = field.validation
    const stringValue = value.toString()

    // Min/Max length
    if (validation.minLength && stringValue.length < validation.minLength) {
      errors[fieldId] = `${field.label} يجب أن يحتوي على ${validation.minLength} أحرف على الأقل`
      return
    }
    if (validation.maxLength && stringValue.length > validation.maxLength) {
      errors[fieldId] = `${field.label} يجب أن لا يتجاوز ${validation.maxLength} حرف`
      return
    }

    // Pattern validation
    if (validation.pattern) {
      const regex = new RegExp(validation.pattern)
      if (!regex.test(stringValue)) {
        errors[fieldId] = `${field.label} غير صحيح`
        return
      }
    }
  }
}

function triggerFileUpload(fieldId: string) {
  fileInputs.value[fieldId]?.click()
}

function handleFileUpload(event: Event, field: SuspectField) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file, field)
  }
}

function handleFileDrop(event: DragEvent, field: SuspectField) {
  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file, field)
  }
}

function processFile(file: File, field: SuspectField) {
  const fieldId = field.id!

  // Validate file
  if (field.validation) {
    // File type validation
    if (field.validation.fileTypes) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      if (!field.validation.fileTypes.includes(fileExtension!)) {
        errors[fieldId] = `نوع الملف غير مدعوم. الأنواع المدعومة: ${field.validation.fileTypes.join(', ')}`
        return
      }
    }

    // File size validation
    if (field.validation.maxFileSize && file.size > field.validation.maxFileSize) {
      errors[fieldId] = `حجم الملف كبير جداً. الحد الأقصى: ${formatFileSize(field.validation.maxFileSize)}`
      return
    }
  }

  // Clear error and store file
  delete errors[fieldId]
  uploadedFiles.value[fieldId] = file
  formData[fieldId] = file.name
}

function getImagePreview(fieldId: string): string {
  const file = uploadedFiles.value[fieldId]
  if (file && file.type.startsWith('image/')) {
    return URL.createObjectURL(file)
  }
  return ''
}

function getFileName(fieldId: string): string {
  return uploadedFiles.value[fieldId]?.name || formData[fieldId] || ''
}

function getFileSize(fieldId: string): string {
  const file = uploadedFiles.value[fieldId]
  return file ? formatFileSize(file.size) : ''
}

function getAcceptedFileTypes(field: SuspectField): string {
  if (field.validation?.fileTypes) {
    return field.validation.fileTypes.map(type => `.${type}`).join(',')
  }
  return field.inputType === 'image' ? 'image/*' : '*/*'
}

function getMaxFileSize(field: SuspectField): string {
  if (field.validation?.maxFileSize) {
    return formatFileSize(field.validation.maxFileSize)
  }
  return field.inputType === 'image' ? '5MB' : '10MB'
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function resetForm() {
  // Clear form data
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })

  // Clear errors
  Object.keys(errors).forEach(key => {
    delete errors[key]
  })

  // Clear uploaded files
  uploadedFiles.value = {}

  // Reset file inputs
  Object.values(fileInputs.value).forEach(input => {
    if (input) input.value = ''
  })

  // Initialize form data with default values
  visibleFields.value.forEach(field => {
    if (formData[field.id!] === undefined) {
      formData[field.id!] = ''
    }
  })

  // Clear editing state
  emit('clearEditing')
}

function loadSuspectData(suspect: SuspectData) {
  // Load suspect field data
  Object.keys(suspect.fields).forEach(key => {
    formData[key] = suspect.fields[key]
  })

  // Load attachments as existing files
  if (suspect.attachments) {
    suspect.attachments.forEach(attachment => {
      if (attachment.fieldId && attachment.filePath) {
        formData[attachment.fieldId] = attachment.originalName
        // For existing files, we'll store the path instead of File object
        // This will be handled differently in the submit function
      }
    })
  }
}

function saveAsDraft() {
  // In a real app, this would save to localStorage or a drafts table
  localStorage.setItem('suspect_draft', JSON.stringify({
    formData: { ...formData },
    timestamp: new Date().toISOString()
  }))
  alert('تم حفظ المسودة بنجاح')
}

function handleSubmit() {
  // Validate all fields
  visibleFields.value.forEach(field => validateField(field))

  if (!isFormValid.value) {
    alert('يرجى تصحيح الأخطاء قبل المتابعة')
    return
  }

  // Prepare attachments
  const attachments: SuspectAttachment[] = []
  Object.entries(uploadedFiles.value).forEach(([fieldId, file]) => {
    attachments.push({
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      fieldId,
      fileName: `${formData.fullName || 'unknown'}_${fieldId}.${file.name.split('.').pop()}`,
      originalName: file.name,
      fileType: file.type,
      fileSize: file.size,
      filePath: URL.createObjectURL(file), // In real app, this would be uploaded to server
      uploadedAt: new Date()
    })
  })

  // Prepare suspect data
  const suspectData: Omit<SuspectData, 'id' | 'createdAt' | 'updatedAt'> = {
    fields: { ...formData },
    attachments,
    createdBy: 'current-user', // In real app, get from auth store
    updatedBy: 'current-user'
  }

  emit('addSuspect', suspectData)
  resetForm()
}

// Watch for editing suspect changes
watch(() => props.editingSuspect, (newSuspect) => {
  if (newSuspect) {
    loadSuspectData(newSuspect)
  } else {
    resetForm()
  }
}, { immediate: true })

// Initialize form
onMounted(() => {
  // Load draft if exists (only if not editing)
  if (!props.editingSuspect) {
    const draft = localStorage.getItem('suspect_draft')
    if (draft) {
      try {
        const { formData: draftData } = JSON.parse(draft)
        Object.assign(formData, draftData)
      } catch (error) {
        console.error('Error loading draft:', error)
      }
    }
  }

  // Initialize form data with default values
  visibleFields.value.forEach(field => {
    if (formData[field.id!] === undefined) {
      formData[field.id!] = ''
    }
  })
})
</script>
