// Database Types for Advanced Data Management

export interface DatabaseTab {
  id: string
  name: string
  icon: string
  columns: DatabaseColumn[]
  rows: DatabaseRow[]
  settings: TabSettings
  groupHeaders: GroupHeaderSettings
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseColumn {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'time' | 'datetime'
  width: number
  isVisible: boolean
  isResizable: boolean
  order: number
  formatting: ColumnFormatting
  validation?: ColumnValidation
}

export interface DatabaseRow {
  id: string
  data: Record<string, any>
  isSelected: boolean
  isGroupHeader: boolean
  groupHeaderText?: string
  formatting?: RowFormatting
  isImportant?: boolean // تمييز الصف كمحتوى مهم
  importantColor?: string // لون المحتوى المهم
  originalTextColor?: string // اللون الأصلي للنص (للاستعادة)
}

export interface ColumnFormatting {
  // Header formatting
  headerTextColor: string
  headerBackgroundColor: string
  headerFontSize: number
  headerFontWeight: 'normal' | 'bold'
  headerTextAlign: 'left' | 'center' | 'right'
  
  // Cell formatting
  cellTextColor: string
  cellBackgroundColor: string
  cellFontSize: number
  cellFontWeight: 'normal' | 'bold'
  cellTextAlign: 'left' | 'center' | 'right'
  cellTextWrap: boolean
  cellFitContent: boolean // احتواء مناسب

  // Column behavior
  sortable: boolean // إمكانية الفرز
  sortDirection?: 'asc' | 'desc' // اتجاه الفرز الحالي
  
  // Border formatting
  borderColor: string
  borderWidth: number
  borderStyle: 'solid' | 'dashed' | 'dotted'
  
  // Number formatting (for number columns)
  numberFormat?: {
    decimals: number
    thousandsSeparator: boolean
    prefix?: string
    suffix?: string
  }
  
  // Date formatting (for date columns)
  dateFormat?: string
}

export interface RowFormatting {
  backgroundColor: string
  textColor: string
  fontSize: number
  fontWeight: 'normal' | 'bold'
}

export interface ColumnValidation {
  required: boolean
  minValue?: number
  maxValue?: number
  minLength?: number
  maxLength?: number
  pattern?: string
  customValidation?: (value: any) => boolean | string
}

export interface TabSettings {
  // Display settings
  showRowNumbers: boolean
  showCheckboxes: boolean
  alternateRowColors: boolean
  
  // Pagination
  pageSize: number
  currentPage: number
  
  // Sorting
  sortColumn?: string
  sortDirection: 'asc' | 'desc'
  
  // Filtering
  filters: Record<string, any>
  
  // Search
  searchQuery: string
  searchColumns: string[]
  
  // Export settings
  exportSettings: ExportSettings
}

export interface GroupHeaderSettings {
  enabled: boolean
  identifier: string // Text that identifies a group header row
  textColor: string
  backgroundColor: string
  fontSize: number
  fontWeight: 'normal' | 'bold'
  textAlign: 'left' | 'center' | 'right'
  colSpan: boolean // Whether to span across all columns
}

export interface ExportSettings {
  includeHeaders: boolean
  includeGroupHeaders: boolean
  includeHiddenColumns: boolean
  preserveFormatting: boolean
  paperSize: 'A4' | 'A3' | 'Letter'
  orientation: 'portrait' | 'landscape'
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

export interface ImportResult {
  success: boolean
  tabId?: string
  rowsImported: number
  columnsImported: number
  errors: ImportError[]
  warnings: ImportWarning[]
}

export interface ImportError {
  row: number
  column: string
  message: string
  value: any
}

export interface ImportWarning {
  row: number
  column: string
  message: string
  value: any
}

export interface ExcelImportOptions {
  sheetName?: string
  startRow: number
  startColumn: number
  preserveFormatting: boolean
  detectDataTypes: boolean
  createNewTab: boolean
  tabName?: string
  existingTabId?: string
}

export interface SavedFormat {
  id: string
  tabId: string
  name: string
  columns: DatabaseColumn[]
  groupHeaders: GroupHeaderSettings
  tabSettings: Partial<TabSettings>
  createdAt: Date
}

// Search and Filter Types
export interface SearchOptions {
  query: string
  columns: string[]
  caseSensitive: boolean
  wholeWord: boolean
  regex: boolean
}

export interface FilterOptions {
  column: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in' | 'notIn'
  value: any
  value2?: any // For 'between' operator
}

// UI Component Types
export interface ColumnSettingsModal {
  isOpen: boolean
  columnId: string
  settings: ColumnFormatting
}

export interface GroupHeaderModal {
  isOpen: boolean
  settings: GroupHeaderSettings
}

export interface ExportModal {
  isOpen: boolean
  format: 'csv' | 'pdf' | 'html'
  settings: ExportSettings
}

// Utility Types
export type CellValue = string | number | Date | boolean | null
export type CellStyle = Partial<CSSStyleDeclaration>

export interface CellData {
  value: CellValue
  displayValue: string
  style: CellStyle
  isEditable: boolean
  validation?: ColumnValidation
}

export interface TableState {
  selectedRows: string[]
  editingCell?: { rowId: string, columnId: string }
  draggedColumn?: string
  resizingColumn?: string
  sortedColumn?: string
  sortDirection: 'asc' | 'desc'
}

// Event Types
export interface CellEditEvent {
  rowId: string
  columnId: string
  oldValue: CellValue
  newValue: CellValue
}

export interface ColumnResizeEvent {
  columnId: string
  oldWidth: number
  newWidth: number
}

export interface ColumnReorderEvent {
  columnId: string
  oldIndex: number
  newIndex: number
}

export interface RowSelectionEvent {
  rowIds: string[]
  isSelected: boolean
}
