// Report template utilities for adding organization logo and footer
import { useSettingsStore } from '@/stores/settings'
import type { ThemeSettings, OrganizationSettings } from '@/types'

/**
 * Generate organization header with logo for reports
 */
export function generateReportHeader(title: string, subtitle?: string): string {
  const settingsStore = useSettingsStore()
  const theme = settingsStore.theme
  const organization = settingsStore.organization

  return `
    <div class="report-header">
      <!-- الأكليشة - شعار المؤسسة على طول الصفحة -->
      ${generateFullWidthLogoSection(theme, organization)}

      <!-- عنوان التقرير أسفل الأكليشة -->
      <div class="report-title-section">
        <h1 class="report-title">${title}</h1>
        ${subtitle ? `<h2 class="report-subtitle">${subtitle}</h2>` : ''}
        <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')}</div>
      </div>
    </div>
  `
}

/**
 * Generate full-width logo section (أكليشة) for report header
 */
function generateFullWidthLogoSection(theme: ThemeSettings, organization: OrganizationSettings): string {
  if (!theme.customLogo) {
    return `
      <div class="logo-header-placeholder">
        <div class="organization-header-name">${organization.name}</div>
        <div class="organization-header-name-en">${organization.nameEn || ''}</div>
      </div>
    `
  }

  return `
    <div class="logo-header-section">
      <img src="${theme.customLogo}" alt="أكليشة المؤسسة" class="organization-header-logo" />
    </div>
  `
}

/**
 * Generate logo section for report header (الدالة القديمة للتوافق)
 */
function generateLogoSection(theme: ThemeSettings, organization: OrganizationSettings): string {
  return generateFullWidthLogoSection(theme, organization)
}

/**
 * Generate organization footer for reports (سطر واحد أفقي)
 */
export function generateReportFooter(): string {
  const settingsStore = useSettingsStore()
  const organization = settingsStore.organization

  // تجميع جميع المعلومات في سطر واحد
  const footerText = [
    organization.name,
    organization.nameEn ? `(${organization.nameEn})` : '',
    organization.address,
    `هاتف: ${organization.phone}`,
    `بريد إلكتروني: ${organization.email}`,
    organization.website ? `الموقع: ${organization.website}` : ''
  ].filter(Boolean).join(' | ')

  return `
    <div class="report-footer">
      <div class="organization-info-horizontal">
        ${footerText}
      </div>
    </div>
  `
}

/**
 * Generate complete CSS styles for report templates
 */
export function generateReportStyles(theme: ThemeSettings): string {
  return `
    <style>
      body {
        font-family: '${theme.fontFamily}', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: ${theme.fontSize}px;
        line-height: 1.6;
        margin: 0;
        padding: 20px;
        direction: rtl;
        background: white;
        color: #333;
      }

      /* Report Header Styles */
      .report-header {
        margin-bottom: 30px;
        page-break-inside: avoid;
      }

      /* الأكليشة - شعار المؤسسة على طول الصفحة */
      .logo-header-section {
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 3px solid ${theme.primaryColor};
        overflow: hidden;
      }

      .organization-header-logo {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 100% !important;
        height: auto;
        max-height: 120px;
        min-height: 80px;
        object-fit: fill !important; /* تمديد الشعار ليملأ العرض كاملاً */
        object-position: center;
        display: block;
        margin: 0;
        border-radius: 0;
      }

      .logo-header-placeholder {
        width: 100%;
        padding: 20px;
        background: linear-gradient(135deg, ${theme.primaryColor}15, ${theme.secondaryColor}15);
        border: 2px dashed ${theme.primaryColor};
        border-radius: ${theme.borderRadius}px;
        text-align: center;
        margin-bottom: 20px;
        min-height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      /* إعدادات إضافية لضمان التمديد الكامل */
      .report-header img,
      .logo-header-section img,
      .organization-header-logo {
        transform: scaleX(1) !important;
        transform-origin: center !important;
        aspect-ratio: auto !important;
      }

      /* تأكيد عدم وجود قيود على العرض */
      .report-header,
      .logo-header-section {
        box-sizing: border-box;
        max-width: none !important;
        position: relative;
      }

      /* CSS خاص للشعارات الصغيرة - تمديدها لتملأ العرض */
      .organization-header-logo[src] {
        background-size: 100% 100% !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
      }

      /* للشعارات الكبيرة - تصغيرها مع الحفاظ على التمديد */
      @supports (object-fit: fill) {
        .organization-header-logo {
          object-fit: fill !important;
          object-position: center !important;
        }
      }

      .organization-header-name {
        font-size: 24px;
        font-weight: bold;
        color: ${theme.primaryColor};
        margin-bottom: 5px;
      }

      .organization-header-name-en {
        font-size: 18px;
        color: ${theme.secondaryColor};
        direction: ltr;
      }

      /* قسم عنوان التقرير */
      .report-title-section {
        text-align: center;
        margin: 20px 0;
      }

      .report-title {
        color: ${theme.primaryColor};
        margin: 0 0 10px 0;
        font-size: 28px;
        font-weight: bold;
      }

      .report-subtitle {
        color: ${theme.secondaryColor};
        margin: 0 0 15px 0;
        font-size: 20px;
        font-weight: 500;
      }

      .report-date {
        color: #666;
        font-size: 14px;
        margin-top: 10px;
      }

      /* Table Styles */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        font-size: 11px;
        table-layout: fixed;
      }

      th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: right;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      th {
        background-color: ${theme.primaryColor}15;
        color: ${theme.primaryColor};
        font-weight: bold;
        font-size: 12px;
      }

      .group-header {
        background-color: ${theme.secondaryColor}10;
        font-weight: bold;
        color: ${theme.secondaryColor};
      }

      .alternate-row {
        background-color: #f9f9f9;
      }

      /* Report Footer Styles - سطر واحد أفقي */
      .report-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 2px solid ${theme.primaryColor};
        padding: 10px 20px;
        font-size: 10px;
        color: #666;
        page-break-inside: avoid;
        z-index: 1000;
      }

      .organization-info-horizontal {
        text-align: center;
        line-height: 1.4;
        font-weight: 500;
        color: ${theme.secondaryColor};
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* Print Styles */
      @media print {
        body {
          margin: 0;
          padding: 15px 15px 60px 15px; /* هامش إضافي للتذييل */
          font-size: 10px;
        }

        .report-header {
          margin-bottom: 20px;
        }

        /* الأكليشة في الطباعة */
        .logo-header-section {
          margin-bottom: 15px;
          padding: 5px 0;
          width: 100%;
        }

        .organization-header-logo {
          width: 100% !important;
          max-width: 100% !important;
          min-width: 100% !important;
          max-height: 100px;
          min-height: 60px;
          object-fit: fill !important; /* تمديد كامل في الطباعة */
        }

        .logo-header-placeholder {
          padding: 15px;
          min-height: 60px;
        }

        .organization-header-name {
          font-size: 20px;
        }

        .organization-header-name-en {
          font-size: 16px;
        }

        /* إعدادات إضافية للطباعة */
        .report-header,
        .logo-header-section {
          width: 100% !important;
          max-width: 100% !important;
        }

        .report-header img,
        .logo-header-section img {
          width: 100% !important;
          object-fit: fill !important;
        }

        .report-title {
          font-size: 18px;
        }

        .report-subtitle {
          font-size: 14px;
        }

        table {
          font-size: 9px;
          page-break-inside: auto;
        }

        tr {
          page-break-inside: avoid;
          page-break-after: auto;
        }

        th, td {
          padding: 4px;
        }

        /* التذييل في الطباعة */
        .report-footer {
          position: fixed;
          bottom: 0;
          font-size: 8px;
          padding: 8px 15px;
        }

        .organization-info-horizontal {
          font-size: 8px;
          white-space: normal; /* السماح بالتفاف النص في الطباعة */
        }
      }

      /* Page break settings */
      @page {
        size: A4 landscape;
        margin: 1cm 1cm 2.5cm 1cm; /* هامش محسن للأكليشة والتذييل */
      }

      /* الصفحة الأولى - هامش إضافي للأكليشة */
      @page :first {
        margin-top: 1.5cm;
      }

      /* Responsive logo sizing */
      @media screen and (max-width: 768px) {
        .report-header {
          flex-direction: column;
          text-align: center;
        }

        .logo-section {
          margin-bottom: 20px;
          max-width: 200px;
        }

        .report-title-section {
          margin: 0;
        }
      }
    </style>
  `
}

/**
 * Generate complete HTML document with organization branding
 */
export function generateBrandedHTMLDocument(
  title: string,
  content: string,
  subtitle?: string
): string {
  const settingsStore = useSettingsStore()
  const theme = settingsStore.theme

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    ${generateReportStyles(theme)}
</head>
<body>
    ${generateReportHeader(title, subtitle)}
    
    <div class="report-content">
        ${content}
    </div>
    
    ${generateReportFooter()}
</body>
</html>
  `
}
