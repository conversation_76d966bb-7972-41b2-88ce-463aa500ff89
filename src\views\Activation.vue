<template>
  <div class="activation-container">
    <!-- خلفية متحركة -->
    <div class="animated-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- محتوى التفعيل -->
    <div class="activation-content">
      <!-- شعار النظام -->
      <div class="system-logo">
        <div class="logo-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <h1 class="system-title">البرنامج الشامل</h1>
        <p class="system-subtitle">بيانات وسجلات المتهمين</p>
      </div>

      <!-- نموذج التفعيل -->
      <div class="activation-form-container">
        <div class="activation-form-header">
          <h2>🔐 تفعيل البرنامج</h2>
          <p>يرجى إدخال رمز التفعيل للمتابعة</p>
        </div>

        <form @submit.prevent="handleActivation" class="activation-form">
          <!-- حقل رمز التفعيل -->
          <div class="form-group">
            <label for="activationCode" class="form-label">
              <i class="fas fa-key"></i>
              رمز التفعيل
            </label>
            <div class="input-container">
              <input
                id="activationCode"
                v-model="formData.activationCode"
                type="text"
                class="form-input"
                placeholder="أدخل رمز التفعيل"
                :disabled="isLoading"
                required
                autocomplete="off"
                maxlength="20"
              />
            </div>
          </div>

          <!-- رسالة الخطأ -->
          <div v-if="errorMessage" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ errorMessage }}
          </div>

          <!-- رسالة النجاح -->
          <div v-if="successMessage" class="success-message">
            <i class="fas fa-check-circle"></i>
            {{ successMessage }}
          </div>

          <!-- زر التفعيل -->
          <button 
            type="submit" 
            class="activation-button"
            :disabled="isLoading || !formData.activationCode.trim()"
          >
            <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-unlock"></i>
            {{ isLoading ? 'جاري التحقق...' : 'تفعيل' }}
          </button>
        </form>

        <!-- معلومات إضافية -->
        <div class="activation-info">
          <div class="info-item">
            <i class="fas fa-info-circle"></i>
            <span>يتطلب رمز تفعيل صالح لاستخدام البرنامج</span>
          </div>
          <div class="info-item">
            <i class="fas fa-shield-check"></i>
            <span>التفعيل مطلوب مرة واحدة فقط</span>
          </div>
        </div>
      </div>
    </div>

    <!-- معلومات النظام -->
    <div class="system-info">
      <p>© 2024 البرنامج الشامل لبيانات وسجلات المتهمين</p>
      <p>الإصدار 1.0.0 - نسخة سطح المكتب</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useActivationStore } from '@/stores/activation'

const router = useRouter()
const activationStore = useActivationStore()

// بيانات النموذج
const formData = ref({
  activationCode: ''
})

const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// دالة التفعيل
async function handleActivation() {
  if (!formData.value.activationCode.trim()) {
    errorMessage.value = 'يرجى إدخال رمز التفعيل'
    return
  }

  isLoading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const isValid = await activationStore.validateActivationCode(formData.value.activationCode.trim())
    
    if (isValid) {
      successMessage.value = 'تم التفعيل بنجاح! جاري التوجيه...'
      
      // انتظار قصير لإظهار رسالة النجاح
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    } else {
      errorMessage.value = 'رمز التفعيل غير صحيح، يرجى المحاولة مرة أخرى'
      formData.value.activationCode = ''
    }
  } catch (error) {
    console.error('خطأ في التفعيل:', error)
    errorMessage.value = 'حدث خطأ أثناء التحقق من رمز التفعيل'
  } finally {
    isLoading.value = false
  }
}

// التحقق من حالة التفعيل عند تحميل الصفحة
onMounted(async () => {
  const isActivated = await activationStore.checkActivationStatus()
  if (isActivated) {
    // إذا كان البرنامج مفعل بالفعل، توجيه مباشر لتسجيل الدخول
    router.push('/login')
  }
})
</script>

<style scoped>
.activation-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  overflow: hidden;
}

/* خلفية متحركة */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 10%;
  right: 50%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* محتوى التفعيل */
.activation-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  padding: 0 20px;
}

/* شعار النظام */
.system-logo {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.logo-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.system-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* نموذج التفعيل */
.activation-form-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.activation-form-header {
  text-align: center;
  margin-bottom: 30px;
}

.activation-form-header h2 {
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.activation-form-header p {
  color: #718096;
  font-size: 1rem;
}

/* حقول النموذج */
.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f7fafc;
  text-align: center;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:disabled {
  background: #f1f5f9;
  cursor: not-allowed;
}

/* رسائل الحالة */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e53e3e;
  background: #fed7d7;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 20px;
  border: 1px solid #feb2b2;
}

.success-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #38a169;
  background: #c6f6d5;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 20px;
  border: 1px solid #9ae6b4;
}

/* زر التفعيل */
.activation-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 25px;
}

.activation-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.activation-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* معلومات إضافية */
.activation-info {
  border-top: 1px solid #e2e8f0;
  padding-top: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.info-item i {
  color: #667eea;
  width: 16px;
}

/* معلومات النظام */
.system-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  z-index: 2;
}

.system-info p {
  margin: 4px 0;
}

/* تجاوب */
@media (max-width: 768px) {
  .activation-content {
    max-width: 90%;
  }
  
  .activation-form-container {
    padding: 30px 20px;
  }
  
  .system-title {
    font-size: 2rem;
  }
  
  .system-subtitle {
    font-size: 1rem;
  }
}
</style>
