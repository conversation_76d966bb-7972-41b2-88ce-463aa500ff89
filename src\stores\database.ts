import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  DatabaseTab, 
  DatabaseColumn, 
  DatabaseRow, 
  ImportResult,
  ExcelImportOptions,
  SavedFormat,
  GroupHeaderSettings,
  TabSettings,
  ColumnFormatting
} from '@/types/database'
import { generateId } from '@/utils/helpers'

export const useDatabaseStore = defineStore('database', () => {
  // State
  const tabs = ref<DatabaseTab[]>([])
  const savedFormats = ref<SavedFormat[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getTabById = computed(() => (id: string) => 
    tabs.value.find(tab => tab.id === id)
  )

  const getTabByName = computed(() => (name: string) => 
    tabs.value.find(tab => tab.name === name)
  )

  // Actions
  function addTab(tabData: Omit<DatabaseTab, 'id'>): DatabaseTab {
    const newTab: DatabaseTab = {
      ...tabData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    tabs.value.push(newTab)
    saveTabsToStorage()
    return newTab
  }

  function removeTab(tabId: string): boolean {
    const index = tabs.value.findIndex(tab => tab.id === tabId)
    if (index !== -1) {
      tabs.value.splice(index, 1)
      saveTabsToStorage()
      return true
    }
    return false
  }

  function updateTab(tabId: string, updates: Partial<DatabaseTab>): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      Object.assign(tab, updates, { updatedAt: new Date() })
      saveTabsToStorage()
      return true
    }
    return false
  }

  function updateTabData(tabId: string, rows: DatabaseRow[], columns?: DatabaseColumn[]): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      tab.rows = rows
      if (columns) {
        tab.columns = columns
      }
      tab.updatedAt = new Date()
      saveTabsToStorage()
      return true
    }
    return false
  }

  function addColumn(tabId: string, column: Omit<DatabaseColumn, 'id'>): DatabaseColumn | null {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const newColumn: DatabaseColumn = {
        ...column,
        id: generateId()
      }
      tab.columns.push(newColumn)
      tab.updatedAt = new Date()
      saveTabsToStorage()
      return newColumn
    }
    return null
  }

  function updateColumn(tabId: string, columnId: string, updates: Partial<DatabaseColumn>): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const column = tab.columns.find(c => c.id === columnId)
      if (column) {
        Object.assign(column, updates)
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function removeColumn(tabId: string, columnId: string): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const index = tab.columns.findIndex(c => c.id === columnId)
      if (index !== -1) {
        tab.columns.splice(index, 1)
        // Remove column data from all rows
        tab.rows.forEach(row => {
          delete row.data[columnId]
        })
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function toggleColumnVisibility(tabId: string, columnId: string): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const column = tab.columns.find(c => c.id === columnId)
      if (column) {
        column.isVisible = !column.isVisible
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function updateColumnSettings(tabId: string, columnId: string, settings: Partial<ColumnFormatting>): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const column = tab.columns.find(c => c.id === columnId)
      if (column) {
        column.formatting = { ...column.formatting, ...settings }
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function updateGroupHeaderSettings(tabId: string, settings: Partial<GroupHeaderSettings>): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      tab.groupHeaders = { ...tab.groupHeaders, ...settings }
      tab.updatedAt = new Date()
      saveTabsToStorage()
      return true
    }
    return false
  }

  function addRow(tabId: string, rowData: Record<string, any>): DatabaseRow | null {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const newRow: DatabaseRow = {
        id: generateId(),
        data: rowData,
        isSelected: false,
        isGroupHeader: false
      }
      tab.rows.push(newRow)
      tab.updatedAt = new Date()
      saveTabsToStorage()
      return newRow
    }
    return null
  }

  function updateRow(tabId: string, rowId: string, updates: Partial<DatabaseRow>): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const row = tab.rows.find(r => r.id === rowId)
      if (row) {
        Object.assign(row, updates)
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function removeRow(tabId: string, rowId: string): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const index = tab.rows.findIndex(r => r.id === rowId)
      if (index !== -1) {
        tab.rows.splice(index, 1)
        tab.updatedAt = new Date()
        saveTabsToStorage()
        return true
      }
    }
    return false
  }

  function removeSelectedRows(tabId: string): number {
    const tab = tabs.value.find(t => t.id === tabId)
    if (!tab) return 0

    const initialCount = tab.rows.length
    console.log(`🗑️ بدء عملية الحذف - العدد الأولي: ${initialCount}`)

    // عد الصفوف المحددة قبل الحذف
    const selectedCount = tab.rows.filter(row => row.isSelected && !row.isGroupHeader).length
    console.log(`📋 عدد الصفوف المحددة للحذف: ${selectedCount}`)

    // إذا لم يكن هناك صفوف محددة، لا نحتاج للمتابعة
    if (selectedCount === 0) {
      console.log(`⚠️ لا توجد صفوف محددة للحذف`)
      return 0
    }

    // منطق حذف مبسط وآمن
    const rowsToKeep: DatabaseRow[] = []

    console.log('🔍 بدء معالجة الصفوف للحذف...')

    for (let i = 0; i < tab.rows.length; i++) {
      const row = tab.rows[i]

      // إذا كان الصف محدد للحذف، تجاهله
      if (row.isSelected && !row.isGroupHeader) {
        console.log(`🗑️ حذف الصف ${i + 1}: ${JSON.stringify(row.data).substring(0, 50)}...`)
        continue // تجاهل هذا الصف (حذفه)
      }

      // إذا كان عنوان مجموعة، تحقق من الصفوف التابعة له
      if (row.isGroupHeader) {
        // البحث عن الصفوف التابعة لهذه المجموعة
        const groupRows: DatabaseRow[] = []
        for (let j = i + 1; j < tab.rows.length; j++) {
          if (tab.rows[j].isGroupHeader) break // مجموعة جديدة
          groupRows.push(tab.rows[j])
        }

        // تحقق من الصفوف المتبقية في المجموعة (غير المحددة للحذف)
        const remainingRows = groupRows.filter(r => !r.isSelected)

        // إذا كانت هناك صفوف متبقية، احتفظ بعنوان المجموعة
        if (remainingRows.length > 0) {
          console.log(`📋 الاحتفاظ بعنوان المجموعة: ${row.groupHeaderText}`)
          rowsToKeep.push(row)
        } else {
          console.log(`🗑️ حذف عنوان المجموعة: ${row.groupHeaderText} (لا توجد صفوف متبقية)`)
        }
      } else {
        // صف عادي غير محدد للحذف
        console.log(`✅ الاحتفاظ بالصف ${i + 1}`)
        rowsToKeep.push(row)
      }
    }

    // Update the tab with filtered rows
    tab.rows = rowsToKeep
    const removedCount = initialCount - tab.rows.length

    console.log(`📊 نتائج الحذف:`)
    console.log(`   - العدد الأولي: ${initialCount}`)
    console.log(`   - العدد النهائي: ${tab.rows.length}`)
    console.log(`   - تم حذف: ${removedCount} صف`)

    if (removedCount > 0) {
      // إعادة تعيين حالة التحديد لجميع الصفوف المتبقية
      tab.rows.forEach(row => {
        row.isSelected = false
      })

      tab.updatedAt = new Date()
      saveTabsToStorage()
      console.log(`✅ تم حفظ التغييرات وإعادة تعيين حالة التحديد`)
    } else {
      console.log(`⚠️ لم يتم حذف أي صف`)
    }

    return removedCount
  }

  // Format Management
  function saveTabFormat(tabId: string, formatName?: string): SavedFormat | null {
    const tab = tabs.value.find(t => t.id === tabId)
    if (tab) {
      const savedFormat: SavedFormat = {
        id: generateId(),
        tabId,
        name: formatName || `تنسيق ${tab.name} - ${new Date().toLocaleDateString('ar')}`,
        columns: JSON.parse(JSON.stringify(tab.columns)),
        groupHeaders: JSON.parse(JSON.stringify(tab.groupHeaders)),
        tabSettings: JSON.parse(JSON.stringify(tab.settings)),
        createdAt: new Date()
      }
      
      savedFormats.value.push(savedFormat)
      saveFormatsToStorage()
      
      // Also save to localStorage with tab-specific key
      localStorage.setItem(`tableFormats_${tab.name}`, JSON.stringify(savedFormat))
      
      return savedFormat
    }
    return null
  }

  function applyTabFormat(tabId: string, formatId?: string): boolean {
    const tab = tabs.value.find(t => t.id === tabId)
    if (!tab) return false

    let format: SavedFormat | null = null

    if (formatId) {
      format = savedFormats.value.find(f => f.id === formatId)
    } else {
      // Try to load from localStorage
      const savedFormatData = localStorage.getItem(`tableFormats_${tab.name}`)
      if (savedFormatData) {
        try {
          format = JSON.parse(savedFormatData)
        } catch (error) {
          console.error('Error parsing saved format:', error)
          return false
        }
      }
    }

    if (format) {
      // Apply column formatting
      format.columns.forEach(savedColumn => {
        const existingColumn = tab.columns.find(c => c.name === savedColumn.name)
        if (existingColumn) {
          existingColumn.formatting = savedColumn.formatting
          existingColumn.width = savedColumn.width
          existingColumn.isVisible = savedColumn.isVisible
        }
      })

      // Apply group header settings
      tab.groupHeaders = format.groupHeaders

      // Apply tab settings
      Object.assign(tab.settings, format.tabSettings)

      tab.updatedAt = new Date()
      saveTabsToStorage()
      return true
    }

    return false
  }

  // Storage Management
  function saveTabsToStorage(): void {
    try {
      localStorage.setItem('database_tabs', JSON.stringify(tabs.value))
    } catch (error) {
      console.error('Error saving tabs to storage:', error)
    }
  }

  function saveFormatsToStorage(): void {
    try {
      localStorage.setItem('database_formats', JSON.stringify(savedFormats.value))
    } catch (error) {
      console.error('Error saving formats to storage:', error)
    }
  }

  async function loadTabs(): Promise<void> {
    try {
      const savedTabs = localStorage.getItem('database_tabs')
      if (savedTabs) {
        tabs.value = JSON.parse(savedTabs)
      }

      const savedFormatsData = localStorage.getItem('database_formats')
      if (savedFormatsData) {
        savedFormats.value = JSON.parse(savedFormatsData)
      }
    } catch (error) {
      console.error('Error loading tabs from storage:', error)
      error.value = 'فشل في تحميل البيانات المحفوظة'
    }
  }

  // Excel import functionality
  async function importExcelFile(file: File, tabName?: string, tabId?: string): Promise<ImportResult> {
    try {
      // This will be handled by the ImportExcelModal component
      // The actual import logic will be in the modal and passed here as processed data

      // For now, create a placeholder tab if needed
      if (tabName && !tabId) {
        const newTab = addTab({
          name: tabName,
          icon: 'fas fa-file-excel',
          columns: [],
          rows: [],
          settings: {
            showCheckboxes: true,
            showRowNumbers: true,
            alternateRowColors: true,
            pageSize: 50,
            currentPage: 1,
            sortDirection: 'asc',
            filters: {},
            searchQuery: '',
            searchColumns: [],
            exportSettings: {
              includeHeaders: true,
              includeGroupHeaders: true,
              includeHiddenColumns: false,
              preserveFormatting: true,
              paperSize: 'A4',
              orientation: 'portrait',
              margins: { top: 20, right: 20, bottom: 20, left: 20 }
            }
          },
          groupHeaders: {
            enabled: false, // تعطيل مؤقت للاختبار
            identifier: 'سجل حركة',
            textColor: '#1e40af',
            backgroundColor: '#dbeafe',
            fontSize: 16,
            fontWeight: 'bold',
            textAlign: 'right',
            colSpan: true
          },
          createdAt: new Date(),
          updatedAt: new Date()
        })

        return {
          success: true,
          tabId: newTab.id,
          rowsImported: 0,
          columnsImported: 0,
          errors: [],
          warnings: []
        }
      }

      return {
        success: true,
        tabId: tabId,
        rowsImported: 0,
        columnsImported: 0,
        errors: [],
        warnings: []
      }
    } catch (error) {
      return {
        success: false,
        rowsImported: 0,
        columnsImported: 0,
        errors: [{ row: 0, column: '', message: error.message, value: null }],
        warnings: []
      }
    }
  }

  // Import processed Excel data
  async function importProcessedData(
    data: { columns: any[], rows: any[] },
    tabName?: string,
    tabId?: string
  ): Promise<ImportResult> {
    try {
      console.log('🔍 Import Debug - Received parameters:', {
        hasTabName: !!tabName,
        tabName,
        hasTabId: !!tabId,
        tabId,
        columnsCount: data.columns.length,
        rowsCount: data.rows.length
      })

      let targetTab: DatabaseTab | undefined
      let isAppendingToExisting = false

      if (tabId && tabId.trim() !== '') {
        // Import to existing tab
        console.log('🔍 Looking for tab with ID:', tabId)
        console.log('🔍 Available tabs:', tabs.value.map(t => ({ id: t.id, name: t.name })))

        targetTab = tabs.value.find(t => t.id === tabId)
        if (!targetTab) {
          console.error('❌ Tab not found with ID:', tabId)
          throw new Error(`التبويب المحدد غير موجود (ID: ${tabId})`)
        }

        console.log('✅ Found target tab:', targetTab.name)

        isAppendingToExisting = true

        // When appending to existing tab, skip the first row if it looks like headers
        if (data.rows.length > 0) {
          const firstRow = data.rows[0]
          const isHeaderRow = Object.values(firstRow).every(value =>
            typeof value === 'string' &&
            !value.match(/^\d+$/) && // Not just numbers
            value.length > 0 &&
            value.length < 50 // Reasonable header length
          )

          if (isHeaderRow) {
            console.log('🗑️ تم حذف الصف الأول (عناوين الأعمدة) لأنه موجود مسبقاً')
            data.rows = data.rows.slice(1) // Remove first row (headers)
          }
        }

        // Update existing columns or add new ones if needed
        data.columns.forEach((newCol, index) => {
          const existingColumn = targetTab!.columns.find(c => c.name === newCol.name)
          if (!existingColumn) {
            // Add new column if it doesn't exist, but copy formatting from existing columns
            const referenceColumn = targetTab!.columns[0] // Use first column as reference
            const newColumn: DatabaseColumn = {
              id: generateId(),
              name: newCol.name,
              type: newCol.type,
              width: referenceColumn?.width || 150,
              isVisible: true,
              isResizable: true,
              order: targetTab!.columns.length + index + 1,
              formatting: referenceColumn ? { ...referenceColumn.formatting } : getDefaultColumnFormatting()
            }
            targetTab!.columns.push(newColumn)
          }
        })
      } else if (tabName) {
        // Create new tab
        const columns: DatabaseColumn[] = data.columns.map((col, index) => ({
          id: generateId(),
          name: col.name,
          type: col.type,
          width: 150,
          isVisible: true,
          isResizable: true,
          order: index + 1,
          formatting: getDefaultColumnFormatting()
        }))

        const newTab = addTab({
          name: tabName,
          icon: 'fas fa-file-excel',
          columns,
          rows: [],
          settings: getDefaultTabSettings(),
          groupHeaders: getDefaultGroupHeaders(),
          createdAt: new Date(),
          updatedAt: new Date()
        })

        targetTab = newTab
      } else {
        console.error('❌ No valid tab identifier provided')
        console.error('❌ tabName:', tabName)
        console.error('❌ tabId:', tabId)
        throw new Error('يجب تحديد اسم التبويب أو معرف التبويب صالح')
      }

      // Convert rows to DatabaseRow format with proper column mapping
      const dbRows: DatabaseRow[] = data.rows.map((rowData, index) => {
        const isHeader = isGroupHeaderRow(rowData, targetTab!.groupHeaders)

        // Map data from column names to column IDs
        const mappedData: Record<string, any> = {}
        targetTab!.columns.forEach(column => {
          // Use column name as key to find data, then map to column ID
          mappedData[column.id] = rowData[column.name] || ''
        })

        // Debug logging for development (disabled in production)
        if (false && index < 2) {
          console.log(`Row ${index + 1}:`, { isHeader, hasData: Object.keys(mappedData).length })
        }

        return {
          id: generateId(),
          data: mappedData, // Use mapped data instead of original
          isSelected: false,
          isGroupHeader: isHeader,
          groupHeaderText: isHeader
            ? extractGroupHeaderText(rowData, targetTab!.groupHeaders)
            : undefined
        }
      })

      // Apply existing formatting to new data when appending
      if (isAppendingToExisting && targetTab.rows.length > 0) {
        console.log('🎨 تطبيق تنسيق البيانات السابقة على البيانات الجديدة')
        // The formatting is already applied through column settings, no additional action needed
        // Group header settings are also inherited from the existing tab
      }

      // Update tab with new data
      if (tabId) {
        // Append to existing data
        targetTab.rows = [...targetTab.rows, ...dbRows]
      } else {
        // Replace data for new tab
        targetTab.rows = dbRows
      }

      targetTab.updatedAt = new Date()
      saveTabsToStorage()

      // Success logging
      if (isAppendingToExisting) {
        console.log(`✅ تم إضافة ${dbRows.length} صف جديد إلى التبويب "${targetTab.name}" مع تطبيق التنسيق السابق`)
      } else {
        console.log(`✅ تم استيراد ${dbRows.length} صف بنجاح إلى التبويب الجديد "${targetTab.name}"`)
      }

      return {
        success: true,
        tabId: targetTab.id,
        rowsImported: dbRows.length,
        columnsImported: data.columns.length,
        errors: [],
        warnings: []
      }
    } catch (error) {
      return {
        success: false,
        rowsImported: 0,
        columnsImported: 0,
        errors: [{ row: 0, column: '', message: error.message, value: null }],
        warnings: []
      }
    }
  }

  async function exportTabData(
    tabId: string,
    format: 'csv' | 'html',
    options?: {
      filteredRows?: DatabaseRow[]
      searchQuery?: string
    }
  ): Promise<void> {
    const tab = tabs.value.find(t => t.id === tabId)
    if (!tab) {
      throw new Error('التبويب غير موجود')
    }

    const { exportToCSV, exportToHTML } = await import('@/utils/export-advanced')

    if (format === 'csv') {
      await exportToCSV(tab, options?.filteredRows, options?.searchQuery)
    } else if (format === 'html') {
      await exportToHTML(tab, options?.filteredRows, options?.searchQuery)
    } else {
      throw new Error('تنسيق التصدير غير مدعوم')
    }
  }

  // Helper functions
  function getDefaultColumnFormatting(): ColumnFormatting {
    return {
      // Header formatting
      headerTextColor: '#334155',
      headerBackgroundColor: '#f1f5f9',
      headerFontSize: 14,
      headerFontWeight: 'bold',
      headerTextAlign: 'center',

      // Cell formatting
      cellTextColor: '#475569',
      cellBackgroundColor: 'transparent',
      cellFontSize: 14,
      cellFontWeight: 'normal',
      cellTextAlign: 'right',
      cellTextWrap: false,
      cellFitContent: false,

      // Column behavior
      sortable: true,

      // Border formatting
      borderColor: '#e2e8f0',
      borderWidth: 1,
      borderStyle: 'solid',

      // Number formatting
      numberFormat: {
        decimals: 0,
        thousandsSeparator: false
      },

      // Date formatting
      dateFormat: 'YYYY-MM-DD'
    }
  }

  function getDefaultTabSettings(): TabSettings {
    return {
      showCheckboxes: true,
      showRowNumbers: true,
      alternateRowColors: true,
      pageSize: 50,
      currentPage: 1,
      sortDirection: 'asc',
      filters: {},
      searchQuery: '',
      searchColumns: [],
      exportSettings: {
        includeHeaders: true,
        includeGroupHeaders: true,
        includeHiddenColumns: false,
        preserveFormatting: true,
        paperSize: 'A4',
        orientation: 'portrait',
        margins: { top: 20, right: 20, bottom: 20, left: 20 }
      }
    }
  }

  function getDefaultGroupHeaders(): GroupHeaderSettings {
    return {
      enabled: true, // تفعيل عناوين المجموعات
      identifier: 'سجل حركة',
      textColor: '#1e40af',
      backgroundColor: '#dbeafe',
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'right',
      colSpan: true
    }
  }

  function isGroupHeaderRow(rowData: Record<string, any>, groupSettings: GroupHeaderSettings): boolean {
    if (!groupSettings.enabled) return false

    // Check if any cell in the row starts with the identifier
    const hasIdentifier = Object.values(rowData).some(value =>
      typeof value === 'string' && value.trim().startsWith(groupSettings.identifier)
    )

    if (!hasIdentifier) return false

    // Additional checks for group headers
    const filledCells = Object.values(rowData).filter(value =>
      value !== null && value !== undefined && value.toString().trim() !== ''
    ).length

    const totalCells = Object.keys(rowData).length
    const emptyRatio = (totalCells - filledCells) / totalCells

    // Check if it's a descriptive header (long text in one cell, others empty)
    const hasLongDescriptiveText = Object.values(rowData).some(value =>
      typeof value === 'string' &&
      value.includes(groupSettings.identifier) &&
      value.length > 50 // Long descriptive text
    )

    // Group headers are either:
    // 1. Have many empty cells (> 50% empty)
    // 2. Have long descriptive text with identifier
    // 3. Have very few filled cells (≤ 3)
    return emptyRatio > 0.5 || hasLongDescriptiveText || filledCells <= 3
  }

  function extractGroupHeaderText(rowData: Record<string, any>, groupSettings: GroupHeaderSettings): string {
    // Find the cell that contains the group header text
    for (const value of Object.values(rowData)) {
      if (typeof value === 'string' && value.trim().startsWith(groupSettings.identifier)) {
        return value.trim()
      }
    }
    return groupSettings.identifier
  }

  return {
    // State
    tabs,
    savedFormats,
    isLoading,
    error,

    // Getters
    getTabById,
    getTabByName,

    // Actions
    addTab,
    removeTab,
    updateTab,
    updateTabData,
    addColumn,
    updateColumn,
    removeColumn,
    toggleColumnVisibility,
    updateColumnSettings,
    updateGroupHeaderSettings,
    addRow,
    updateRow,
    removeRow,
    removeSelectedRows,
    saveTabFormat,
    applyTabFormat,
    loadTabs,
    importExcelFile,
    importProcessedData,
    exportTabData
  }
})
