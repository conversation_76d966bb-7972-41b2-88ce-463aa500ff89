// User and Authentication Types
export interface User {
  id: string
  name: string
  email: string
  role: UserRole
  permissions: Permission[]
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

export interface UserRole {
  id: string
  name: string
  displayName: string
  permissions: Permission[]
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: 'view' | 'create' | 'update' | 'delete'
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Suspect Data Types
export interface SuspectField {
  id: string
  label: string
  icon: string
  inputType: 'text' | 'image' | 'file' | 'date' | 'select' | 'textarea'
  isRequired: boolean
  isVisible: boolean
  order: number
  options?: string[] // For select type
  validation?: FieldValidation
}

export interface FieldValidation {
  minLength?: number
  maxLength?: number
  pattern?: string
  fileTypes?: string[]
  maxFileSize?: number
}

export interface SuspectData {
  id: string
  fields: Record<string, any> & {
    fileNumber?: string
    fullName?: string
    idNumber?: string
    address?: string
    phone?: string
    age?: string
    nationality?: string
    profession?: string
    maritalStatus?: string
    seizures?: string
    arrestDate?: Date
    notes?: string
    isReleased?: boolean
    releaseDate?: Date
    isTransferred?: boolean
    transferDate?: Date
  }
  attachments: SuspectAttachment[]
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy: string
}

export interface SuspectAttachment {
  id: string
  fieldId: string
  fileName: string
  originalName: string
  fileType: string
  fileSize: number
  filePath: string
  uploadedAt: Date
}

// Settings Types
export interface DeveloperInfo {
  title: string
  developerName: string
  supervisorName: string
  icon?: string // Font Awesome icon class
  customIcon?: string // Custom image as data URL
}

export interface AppSettings {
  theme: ThemeSettings
  organization: OrganizationSettings
  fields: SuspectField[]
  users: User[]
  backup: BackupSettings
  developerInfo: DeveloperInfo
}

export interface ThemeSettings {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  fontFamily: string
  fontSize: number
  borderRadius: number
  customLogo?: string
  logoPosition: 'top-left' | 'top-center' | 'top-right'
  logoSize: number
}

export interface OrganizationSettings {
  name: string
  nameEn?: string
  address: string
  phone: string
  email: string
  website?: string
  logo?: string
  stamp?: string
  signature?: string
}

export interface BackupSettings {
  autoBackup: boolean
  backupInterval: number // in hours
  maxBackups: number
  lastBackup?: Date
}

// Report Types
export interface ReportTemplate {
  id: string
  name: string
  type: 'pdf' | 'excel' | 'csv'
  fields: string[]
  filters: ReportFilter[]
  formatting: ReportFormatting
  createdAt: Date
  updatedAt: Date
}

export interface ReportFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'between' | 'in'
  value: any
}

export interface ReportFormatting {
  header: {
    showLogo: boolean
    showOrganization: boolean
    showDate: boolean
    customText?: string
  }
  footer: {
    showPageNumbers: boolean
    showSignature: boolean
    customText?: string
  }
  table: {
    showBorders: boolean
    alternateRowColors: boolean
    fontSize: number
    headerBackgroundColor: string
    headerTextColor: string
  }
}

// Database Types
export interface DatabaseConfig {
  name: string
  version: number
  stores: DatabaseStore[]
}

export interface DatabaseStore {
  name: string
  keyPath: string
  autoIncrement: boolean
  indexes: DatabaseIndex[]
}

export interface DatabaseIndex {
  name: string
  keyPath: string | string[]
  unique: boolean
}

// Import/Export Types
export interface ImportResult {
  success: boolean
  totalRows: number
  successfulRows: number
  failedRows: number
  errors: ImportError[]
}

export interface ImportError {
  row: number
  field: string
  message: string
  value: any
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  fields: string[]
  filters: ReportFilter[]
  includeAttachments: boolean
  template?: ReportTemplate
}

// UI Component Types
export interface TabItem {
  id: string
  label: string
  icon: string
  component: any
  isActive: boolean
}

export interface MenuItem {
  id: string
  label: string
  icon: string
  route?: string
  children?: MenuItem[]
  permission?: string
}

export interface NotificationItem {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: NotificationAction[]
  createdAt: Date
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: string
  value: any
  rules: ValidationRule[]
  props?: Record<string, any>
}

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
  message: string
}

export interface FormState {
  fields: Record<string, FormField>
  errors: Record<string, string>
  isValid: boolean
  isSubmitting: boolean
}
