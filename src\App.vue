<template>
  <div id="app" class="min-h-screen bg-secondary-50 font-arabic rtl">
    <!-- Header -->
    <header v-if="authStore.isAuthenticated && activationStore.isActivated" class="neumorphic-header sticky top-0 z-40 bg-secondary-50">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between py-4">
          <!-- Logo and Title -->
          <div class="flex items-center gap-4">
            <div class="neumorphic-icon">
              <i class="fas fa-shield-alt text-primary-600 text-xl"></i>
            </div>
            <div>
              <h1 class="text-xl font-bold text-secondary-800">برنامج بيانات المتهمين</h1>
              <p class="text-sm text-secondary-600">نظام متكامل لإدارة بيانات المتهمين</p>
            </div>
          </div>

          <!-- Navigation -->
          <nav class="hidden md:flex items-center gap-2">
            <router-link
              v-for="item in navigationItems"
              :key="item.path"
              :to="item.path"
              :class="[
                'neumorphic-button flex items-center gap-2 px-4 py-2 transition-all duration-300',
                $route.path === item.path ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' : ''
              ]"
            >
              <i :class="item.icon"></i>
              <span>{{ item.label }}</span>
            </router-link>
          </nav>

          <!-- User Info and Logout -->
          <div class="hidden md:flex items-center gap-3">
            <!-- User Info -->
            <div class="flex items-center gap-2 neumorphic-card bg-white px-3 py-2">
              <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white text-sm"></i>
              </div>
              <div class="text-sm">
                <p class="font-medium text-secondary-800">{{ authStore.currentUser?.fullName }}</p>
                <p class="text-xs text-secondary-600">{{ getRoleLabel(authStore.currentUser?.role) }}</p>
              </div>
            </div>

            <!-- Logout Button -->
            <button
              @click="handleLogout"
              class="neumorphic-button p-2 text-red-600 hover:bg-red-50"
              title="تسجيل الخروج"
            >
              <i class="fas fa-sign-out-alt"></i>
            </button>
          </div>

          <!-- Mobile Menu Button -->
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="md:hidden neumorphic-button p-2"
          >
            <i class="fas fa-bars"></i>
          </button>
        </div>

        <!-- Mobile Navigation -->
        <div v-if="showMobileMenu" class="md:hidden pb-4">
          <nav class="flex flex-col gap-2">
            <router-link
              v-for="item in navigationItems"
              :key="item.path"
              :to="item.path"
              :class="[
                'neumorphic-button flex items-center gap-2 px-4 py-2 transition-all duration-300',
                $route.path === item.path ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' : ''
              ]"
              @click="showMobileMenu = false"
            >
              <i :class="item.icon"></i>
              <span>{{ item.label }}</span>
            </router-link>

            <!-- Mobile User Info and Logout -->
            <div class="border-t border-secondary-200 pt-2 mt-2">
              <div class="flex items-center gap-2 neumorphic-card bg-white px-3 py-2 mb-2">
                <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <i class="fas fa-user text-white text-sm"></i>
                </div>
                <div class="text-sm">
                  <p class="font-medium text-secondary-800">{{ authStore.currentUser?.fullName }}</p>
                  <p class="text-xs text-secondary-600">{{ getRoleLabel(authStore.currentUser?.role) }}</p>
                </div>
              </div>

              <button
                @click="handleLogout"
                class="neumorphic-button flex items-center gap-2 px-4 py-2 w-full text-red-600 hover:bg-red-50"
              >
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
              </button>
            </div>
          </nav>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main :class="authStore.isAuthenticated ? 'container mx-auto px-4 py-6' : ''">
      <router-view />
    </main>

    <!-- Footer -->
    <footer v-if="authStore.isAuthenticated" class="mt-auto py-6 border-t border-secondary-200">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
          <!-- Developer Info -->
          <div class="flex items-center gap-3 neumorphic-card bg-white p-3">
            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
              <!-- Custom Icon -->
              <img v-if="developerInfo.customIcon"
                   :src="developerInfo.customIcon"
                   alt="أيقونة المطور"
                   class="w-6 h-6 object-cover rounded-full" />
              <!-- Font Awesome Icon -->
              <i v-else :class="(developerInfo.icon || 'fas fa-code') + ' text-white text-sm'"></i>
            </div>
            <div class="text-sm">
              <p class="font-medium text-secondary-800">{{ developerInfo.title }}</p>
              <p class="text-secondary-600">{{ developerInfo.developerName }}</p>
              <p class="text-secondary-600">اشراف: {{ developerInfo.supervisorName }}</p>
            </div>
            <button
              @click="showDeveloperInfo = true"
              class="neumorphic-button p-1 text-xs text-primary-600"
            >
              <i class="fas fa-info-circle"></i>
            </button>
          </div>

          <!-- Copyright -->
          <div class="text-center text-sm text-secondary-600">
            <p>&copy; {{ currentYear }} جميع الحقوق محفوظة</p>
            <p>الإصدار {{ appVersion }}</p>
          </div>

          <!-- Theme Toggle -->
          <div class="flex items-center gap-2">
            <span class="text-sm text-secondary-600">المظهر:</span>
            <button
              @click="toggleTheme"
              class="neumorphic-button p-2 text-sm"
            >
              <i :class="isDarkMode ? 'fas fa-sun text-yellow-500' : 'fas fa-moon text-blue-500'"></i>
            </button>
          </div>
        </div>
      </div>
    </footer>

    <!-- Developer Info Modal -->
    <div v-if="showDeveloperInfo" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="neumorphic-card bg-white max-w-md w-full">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-secondary-800">معلومات المطور</h3>
          <button @click="showDeveloperInfo = false" class="text-secondary-400 hover:text-secondary-600">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="space-y-4">
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <!-- Custom Icon -->
              <img v-if="developerInfo.customIcon"
                   :src="developerInfo.customIcon"
                   alt="أيقونة المطور"
                   class="w-12 h-12 object-cover rounded-full" />
              <!-- Font Awesome Icon -->
              <i v-else :class="(developerInfo.icon || 'fas fa-laptop-code') + ' text-white text-2xl'"></i>
            </div>
            <h4 class="font-semibold text-secondary-800">{{ developerInfo.title }}</h4>
            <p class="text-sm text-secondary-600">{{ developerInfo.developerName }}</p>
            <p class="text-sm text-secondary-600">اشراف: {{ developerInfo.supervisorName }}</p>
          </div>
          
          <div class="border-t border-secondary-200 pt-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p class="font-medium text-secondary-700">الإصدار:</p>
                <p class="text-secondary-600">{{ appVersion }}</p>
              </div>
              <div>
                <p class="font-medium text-secondary-700">تاريخ البناء:</p>
                <p class="text-secondary-600">{{ buildDate }}</p>
              </div>
              <div>
                <p class="font-medium text-secondary-700">التقنيات:</p>
                <p class="text-secondary-600">Vue.js, TypeScript</p>
              </div>
              <div>
                <p class="font-medium text-secondary-700">الترخيص:</p>
                <p class="text-secondary-600">MIT License</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-secondary-200 pt-4 text-center">
            <p class="text-xs text-secondary-500">
              تم تطوير هذا البرنامج باستخدام أحدث التقنيات لضمان الأداء والأمان
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="neumorphic-card bg-white p-8 text-center">
        <div class="spinner mx-auto mb-4"></div>
        <p class="text-secondary-700">جاري التحميل...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import { useActivationStore } from '@/stores/activation'

// Reactive data
const showMobileMenu = ref(false)
const showDeveloperInfo = ref(false)
const isDarkMode = ref(false)
const isLoading = ref(false)

// Route and Router
const route = useRoute()
const router = useRouter()

// Stores
const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const activationStore = useActivationStore()

// Navigation items
const navigationItems = [
  {
    path: '/suspects',
    label: 'بيانات المتهمين',
    icon: 'fas fa-users'
  },
  {
    path: '/database',
    label: 'قاعدة البيانات',
    icon: 'fas fa-database'
  },
  {
    path: '/reports',
    label: 'التقارير',
    icon: 'fas fa-chart-bar'
  },
  {
    path: '/settings',
    label: 'الإعدادات',
    icon: 'fas fa-cog'
  }
]

// Computed
const currentYear = computed(() => new Date().getFullYear())
const appVersion = computed(() => '1.0.0')
const buildDate = computed(() => new Date().toLocaleDateString('en-GB'))
const developerInfo = computed(() => settingsStore.settings?.developerInfo || {
  title: 'تطوير وتصميم',
  developerName: 'م- محرم اليفرسي',
  supervisorName: 'ق/عبدالرحمن اليفرسي',
  icon: 'fas fa-code'
})

// Methods
function toggleTheme() {
  isDarkMode.value = !isDarkMode.value

  // Apply dark mode class to document
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark-mode')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark-mode')
    localStorage.setItem('theme', 'light')
  }

  console.log('Theme toggled:', isDarkMode.value ? 'dark' : 'light')
}

function getRoleLabel(role: string | undefined): string {
  switch (role) {
    case 'admin': return 'مدير النظام'
    case 'user': return 'مستخدم'
    case 'viewer': return 'مراقب'
    default: return 'غير محدد'
  }
}

async function handleLogout() {
  if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
    authStore.logout()
    router.push('/login')
  }
}

// Lifecycle
onMounted(async () => {
  // Load settings
  await settingsStore.loadSettings()

  // Initialize theme from localStorage or system preference
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkMode.value = savedTheme === 'dark'
  } else {
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  // Apply theme class to document
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark-mode')
  } else {
    document.documentElement.classList.remove('dark-mode')
  }
})
</script>
