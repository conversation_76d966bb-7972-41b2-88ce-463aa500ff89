<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b border-secondary-200">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon">
            <i :class="isEditing ? 'fas fa-user-edit' : 'fas fa-user-plus'" class="text-primary-600"></i>
          </div>
          <h2 class="text-xl font-bold text-secondary-800">
            {{ isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد' }}
          </h2>
        </div>
        <button @click="$emit('cancel')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- القسم الأول: معلومات المستخدم -->
          <div class="neumorphic-card bg-gradient-to-br from-blue-50 to-blue-100 p-6">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white text-sm"></i>
              </div>
              <h3 class="text-lg font-semibold text-blue-800">معلومات المستخدم</h3>
            </div>

            <div class="space-y-4">
              <!-- اسم المستخدم -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  <i class="fas fa-user ml-2"></i>
                  اسم المستخدم
                </label>
                <input
                  v-model="formData.username"
                  type="text"
                  required
                  class="neumorphic-input w-full"
                  placeholder="أدخل اسم المستخدم"
                  :disabled="isEditing"
                />
              </div>

              <!-- الاسم الكامل -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  <i class="fas fa-id-card ml-2"></i>
                  الاسم الكامل
                </label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  class="neumorphic-input w-full"
                  placeholder="أدخل الاسم الكامل"
                />
              </div>

              <!-- البريد الإلكتروني -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  <i class="fas fa-envelope ml-2"></i>
                  البريد الإلكتروني
                </label>
                <input
                  v-model="formData.email"
                  type="email"
                  required
                  class="neumorphic-input w-full"
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>

              <!-- دور المستخدم -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  <i class="fas fa-shield-alt ml-2"></i>
                  دور المستخدم
                </label>
                <select
                  v-model="formData.role"
                  required
                  class="neumorphic-input w-full"
                  @change="onRoleChange"
                >
                  <option value="">اختر الدور</option>
                  <option
                    v-for="role in availableRoles"
                    :key="role.id"
                    :value="role"
                  >
                    {{ role.displayName }}
                  </option>
                </select>
                <p v-if="formData.role?.description" class="text-xs text-secondary-600 mt-1">
                  {{ formData.role.description }}
                </p>
              </div>

              <!-- كلمة المرور -->
              <div v-if="!isEditing">
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  <i class="fas fa-key ml-2"></i>
                  كلمة المرور
                </label>
                <div class="relative">
                  <input
                    v-model="formData.password"
                    :type="showPassword ? 'text' : 'password'"
                    required
                    minlength="8"
                    class="neumorphic-input w-full pl-10"
                    placeholder="أدخل كلمة المرور (8 أحرف على الأقل)"
                  />
                  <button
                    type="button"
                    @click="showPassword = !showPassword"
                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
                  >
                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                </div>
              </div>

              <!-- حالة المستخدم -->
              <div>
                <label class="flex items-center gap-3">
                  <input
                    v-model="formData.isActive"
                    type="checkbox"
                    class="neumorphic-checkbox"
                  />
                  <span class="text-sm font-medium text-secondary-700">
                    <i class="fas fa-toggle-on ml-2"></i>
                    المستخدم نشط
                  </span>
                </label>
              </div>
            </div>
          </div>

          <!-- القسم الثاني: إدارة الصلاحيات -->
          <div class="neumorphic-card bg-gradient-to-br from-green-50 to-green-100 p-6">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-shield-alt text-white text-sm"></i>
              </div>
              <h3 class="text-lg font-semibold text-green-800">إدارة الصلاحيات</h3>
            </div>

            <div v-if="!formData.role" class="text-center py-8">
              <i class="fas fa-info-circle text-blue-500 text-2xl mb-2"></i>
              <p class="text-secondary-600">يرجى اختيار دور المستخدم أولاً</p>
            </div>

            <div v-else class="space-y-4 max-h-96 overflow-y-auto">
              <!-- عرض الصلاحيات حسب الأقسام -->
              <div
                v-for="section in appSections"
                :key="section.id"
                class="neumorphic-card bg-white p-4"
              >
                <!-- عنوان القسم -->
                <div class="flex items-center gap-3 mb-3">
                  <label class="flex items-center gap-2 cursor-pointer">
                    <input
                      v-model="userPermissions.sections[section.id].allowed"
                      type="checkbox"
                      class="neumorphic-checkbox"
                      @change="onSectionToggle(section.id)"
                    />
                    <i :class="section.icon" class="text-primary-600"></i>
                    <span class="font-semibold text-secondary-800">{{ section.displayName }}</span>
                  </label>
                </div>

                <!-- التبويبات -->
                <div
                  v-if="userPermissions.sections[section.id].allowed"
                  class="mr-6 space-y-3"
                >
                  <div
                    v-for="tab in section.tabs"
                    :key="tab.id"
                    class="border-r-2 border-secondary-200 pr-4"
                  >
                    <!-- عنوان التبويب -->
                    <div class="flex items-center gap-2 mb-2">
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input
                          v-model="userPermissions.sections[section.id].tabs[tab.id].allowed"
                          type="checkbox"
                          class="neumorphic-checkbox"
                          @change="onTabToggle(section.id, tab.id)"
                        />
                        <i :class="tab.icon" class="text-blue-600 text-sm"></i>
                        <span class="text-sm font-medium text-secondary-700">{{ tab.displayName }}</span>
                      </label>
                    </div>

                    <!-- الإجراءات -->
                    <div
                      v-if="userPermissions.sections[section.id].tabs[tab.id].allowed"
                      class="mr-6 grid grid-cols-1 gap-2"
                    >
                      <label
                        v-for="action in tab.actions"
                        :key="action.id"
                        class="flex items-center gap-2 cursor-pointer text-xs"
                      >
                        <input
                          v-model="userPermissions.sections[section.id].tabs[tab.id].actions[action.id]"
                          type="checkbox"
                          class="neumorphic-checkbox scale-75"
                        />
                        <i :class="action.icon" class="text-green-600"></i>
                        <span class="text-secondary-600">{{ action.displayName }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            type="button"
            @click="$emit('cancel')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700 px-6 py-2"
          >
            <i class="fas fa-times ml-2"></i>
            إلغاء
          </button>
          <button
            type="submit"
            class="neumorphic-button bg-primary-500 text-white px-6 py-2 hover:bg-primary-600"
            :disabled="!isFormValid || isLoading"
          >
            <i v-if="isLoading" class="fas fa-spinner fa-spin ml-2"></i>
            <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'" class="ml-2"></i>
            {{ isLoading ? 'جاري الحفظ...' : (isEditing ? 'تحديث' : 'إضافة') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { User, UserRole, UserPermissions } from '@/types'
import { userService } from '@/services/UserService'
import { permissionsService } from '@/services/PermissionsService'

// Props
interface Props {
  user?: User | null
  isEditing: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [user: User]
  cancel: []
}>()

// Reactive data
const isLoading = ref(false)
const showPassword = ref(false)
const availableRoles = ref<UserRole[]>([])
const appSections = ref(permissionsService.getAppStructure())

// Form data
const formData = ref<{
  username: string
  name: string
  email: string
  role: UserRole | null
  password: string
  isActive: boolean
}>({
  username: '',
  name: '',
  email: '',
  role: null,
  password: '',
  isActive: true
})

// User permissions
const userPermissions = ref<UserPermissions>({
  userId: '',
  sections: {}
})

// Computed
const isFormValid = computed(() => {
  const hasRequiredFields = formData.value.username.trim().length > 0 && 
                           formData.value.name.trim().length > 0 &&
                           formData.value.email.trim().length > 0 &&
                           formData.value.role
  
  if (!props.isEditing) {
    return hasRequiredFields && formData.value.password.length >= 8
  }
  
  return hasRequiredFields
})

// Methods
async function loadAvailableRoles() {
  availableRoles.value = userService.getAvailableRoles()
}

function initializeFormData() {
  if (props.isEditing && props.user) {
    formData.value = {
      username: props.user.username,
      name: props.user.name,
      email: props.user.email,
      role: props.user.role,
      password: '',
      isActive: props.user.isActive
    }
    
    // تحميل صلاحيات المستخدم الحالية
    loadUserPermissions(props.user.id)
  } else {
    // إعادة تعيين النموذج للمستخدم الجديد
    formData.value = {
      username: '',
      name: '',
      email: '',
      role: null,
      password: '',
      isActive: true
    }
    
    // إنشاء صلاحيات فارغة
    initializeEmptyPermissions()
  }
}

async function loadUserPermissions(userId: string) {
  const permissions = await userService.getUserPermissions(userId)
  if (permissions) {
    userPermissions.value = permissions
  } else if (formData.value.role) {
    // إنشاء صلاحيات افتراضية إذا لم توجد
    userPermissions.value = permissionsService.createDefaultPermissions(userId, formData.value.role.id)
  }
}

function initializeEmptyPermissions() {
  userPermissions.value = {
    userId: '',
    sections: {}
  }
  
  // إنشاء هيكل فارغ للصلاحيات
  appSections.value.forEach(section => {
    userPermissions.value.sections[section.id] = {
      allowed: false,
      tabs: {}
    }
    
    section.tabs.forEach(tab => {
      userPermissions.value.sections[section.id].tabs[tab.id] = {
        allowed: false,
        actions: {}
      }
      
      tab.actions.forEach(action => {
        userPermissions.value.sections[section.id].tabs[tab.id].actions[action.id] = false
      })
    })
  })
}

function onRoleChange() {
  if (formData.value.role) {
    // إنشاء صلاحيات افتراضية للدور المختار
    userPermissions.value = permissionsService.createDefaultPermissions('', formData.value.role.id)
  }
}

function onSectionToggle(sectionId: string) {
  const sectionAllowed = userPermissions.value.sections[sectionId].allowed
  
  // إذا تم إلغاء تفعيل القسم، إلغاء تفعيل جميع التبويبات والإجراءات
  if (!sectionAllowed) {
    Object.keys(userPermissions.value.sections[sectionId].tabs).forEach(tabId => {
      userPermissions.value.sections[sectionId].tabs[tabId].allowed = false
      Object.keys(userPermissions.value.sections[sectionId].tabs[tabId].actions).forEach(actionId => {
        userPermissions.value.sections[sectionId].tabs[tabId].actions[actionId] = false
      })
    })
  }
}

function onTabToggle(sectionId: string, tabId: string) {
  const tabAllowed = userPermissions.value.sections[sectionId].tabs[tabId].allowed
  
  // إذا تم إلغاء تفعيل التبويب، إلغاء تفعيل جميع الإجراءات
  if (!tabAllowed) {
    Object.keys(userPermissions.value.sections[sectionId].tabs[tabId].actions).forEach(actionId => {
      userPermissions.value.sections[sectionId].tabs[tabId].actions[actionId] = false
    })
  }
}

async function handleSubmit() {
  if (!isFormValid.value || isLoading.value) return
  
  isLoading.value = true
  
  try {
    let savedUser: User
    
    if (props.isEditing && props.user) {
      // تحديث المستخدم
      savedUser = await userService.updateUser(props.user.id, {
        name: formData.value.name,
        email: formData.value.email,
        role: formData.value.role!,
        isActive: formData.value.isActive
      })
    } else {
      // إضافة مستخدم جديد
      savedUser = await userService.addUser({
        username: formData.value.username,
        name: formData.value.name,
        email: formData.value.email,
        role: formData.value.role!,
        permissions: [],
        isActive: formData.value.isActive
      }, formData.value.password)
    }
    
    // حفظ الصلاحيات المخصصة
    userPermissions.value.userId = savedUser.id
    await userService.saveUserPermissions(savedUser.id, userPermissions.value)
    
    emit('save', savedUser)
    
  } catch (error) {
    console.error('Error saving user:', error)
    alert(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ المستخدم')
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadAvailableRoles()
  initializeFormData()
})

// Watch for prop changes
watch(() => props.user, () => {
  initializeFormData()
}, { deep: true })
</script>

<style scoped>
.neumorphic-checkbox {
  @apply w-4 h-4 text-primary-600 bg-white border-2 border-secondary-300 rounded focus:ring-primary-500 focus:ring-2;
}

.neumorphic-input {
  @apply px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white;
}

.neumorphic-button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md;
}

.neumorphic-card {
  @apply rounded-xl shadow-lg border border-secondary-100;
}

.neumorphic-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center shadow-inner bg-gradient-to-br from-white to-secondary-100;
}
</style>
