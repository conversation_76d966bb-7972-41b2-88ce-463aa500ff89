<template>
  <div class="neumorphic-card">
    <div class="flex items-center gap-3 mb-6">
      <div class="neumorphic-icon">
        <i class="fas fa-database text-primary-600"></i>
      </div>
      <div>
        <h2 class="text-xl font-bold text-secondary-800">النسخ الاحتياطي والاستعادة</h2>
        <p class="text-secondary-600">إدارة النسخ الاحتياطية واستعادة البيانات</p>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Backup Settings -->
      <div class="space-y-6">
        <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
          <i class="fas fa-cog"></i>
          إعدادات النسخ الاحتياطي
        </h3>

        <!-- Auto Backup -->
        <div>
          <label class="flex items-center gap-3 neumorphic-button cursor-pointer p-4">
            <input
              v-model="backupData.autoBackup"
              type="checkbox"
              class="hidden"
              @change="updateBackup"
            />
            <div :class="[
              'w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-300',
              backupData.autoBackup 
                ? 'bg-success-500 border-success-500 text-white' 
                : 'border-secondary-300'
            ]">
              <i v-if="backupData.autoBackup" class="fas fa-check text-xs"></i>
            </div>
            <div>
              <span class="font-medium">النسخ الاحتياطي التلقائي</span>
              <p class="text-sm text-secondary-600">إنشاء نسخة احتياطية تلقائياً بشكل دوري</p>
            </div>
          </label>
        </div>

        <!-- Backup Interval -->
        <div v-if="backupData.autoBackup">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            فترة النسخ الاحتياطي (بالساعات)
          </label>
          <select
            v-model.number="backupData.backupInterval"
            class="neumorphic-select w-full"
            @change="updateBackup"
          >
            <option :value="6">كل 6 ساعات</option>
            <option :value="12">كل 12 ساعة</option>
            <option :value="24">يومياً</option>
            <option :value="168">أسبوعياً</option>
            <option :value="720">شهرياً</option>
          </select>
        </div>

        <!-- Max Backups -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            عدد النسخ الاحتياطية المحفوظة: {{ backupData.maxBackups }}
          </label>
          <input
            v-model.number="backupData.maxBackups"
            type="range"
            min="5"
            max="50"
            class="w-full"
            @input="updateBackup"
          />
          <div class="flex justify-between text-xs text-secondary-500 mt-1">
            <span>5</span>
            <span>50</span>
          </div>
        </div>

        <!-- Last Backup Info -->
        <div v-if="backupData.lastBackup" class="neumorphic-card bg-secondary-50 p-4">
          <div class="flex items-center gap-3">
            <div class="neumorphic-icon text-sm">
              <i class="fas fa-clock text-success-600"></i>
            </div>
            <div>
              <p class="font-medium text-secondary-800">آخر نسخة احتياطية</p>
              <p class="text-sm text-secondary-600">{{ formatDate(backupData.lastBackup) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Backup Actions -->
      <div class="space-y-6">
        <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2">
          <i class="fas fa-tools"></i>
          عمليات النسخ والاستعادة
        </h3>

        <!-- Manual Backup -->
        <div class="neumorphic-card bg-white p-6">
          <div class="flex items-center gap-3 mb-4">
            <div class="neumorphic-icon">
              <i class="fas fa-download text-primary-600"></i>
            </div>
            <div>
              <h4 class="font-semibold text-secondary-800">نسخة احتياطية يدوية</h4>
              <p class="text-sm text-secondary-600">تصدير جميع البيانات والإعدادات</p>
            </div>
          </div>
          <button
            @click="createManualBackup"
            class="neumorphic-button text-primary-600 hover:text-primary-700 w-full"
            :disabled="loading"
          >
            <i class="fas fa-download ml-2"></i>
            إنشاء نسخة احتياطية الآن
          </button>
        </div>

        <!-- Restore from Backup -->
        <div class="neumorphic-card bg-white p-6">
          <div class="flex items-center gap-3 mb-4">
            <div class="neumorphic-icon">
              <i class="fas fa-upload text-warning-600"></i>
            </div>
            <div>
              <h4 class="font-semibold text-secondary-800">استعادة من نسخة احتياطية</h4>
              <p class="text-sm text-secondary-600">استيراد البيانات من ملف نسخة احتياطية</p>
            </div>
          </div>
          <div class="space-y-3">
            <div
              @click="triggerRestore"
              @dragover.prevent
              @drop.prevent="handleRestoreDrop"
              class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-6 text-center cursor-pointer hover:border-warning-400 transition-colors"
            >
              <i class="fas fa-cloud-upload-alt text-2xl text-secondary-400 mb-2"></i>
              <p class="text-secondary-600">اضغط أو اسحب ملف النسخة الاحتياطية هنا</p>
              <p class="text-xs text-secondary-500">JSON files only</p>
            </div>
            <input
              ref="restoreInput"
              type="file"
              accept=".json"
              @change="handleRestoreUpload"
              class="hidden"
            />
          </div>
        </div>

        <!-- Export Data -->
        <div class="neumorphic-card bg-white p-6">
          <div class="flex items-center gap-3 mb-4">
            <div class="neumorphic-icon">
              <i class="fas fa-file-export text-success-600"></i>
            </div>
            <div>
              <h4 class="font-semibold text-secondary-800">تصدير البيانات</h4>
              <p class="text-sm text-secondary-600">تصدير بيانات محددة بصيغ مختلفة</p>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-2">
            <button
              @click="exportData('json')"
              class="neumorphic-button text-blue-600 hover:text-blue-700 text-sm"
              :disabled="loading"
            >
              <i class="fas fa-file-code ml-1"></i>
              JSON
            </button>
            <button
              @click="exportData('excel')"
              class="neumorphic-button text-green-600 hover:text-green-700 text-sm"
              :disabled="loading"
            >
              <i class="fas fa-file-excel ml-1"></i>
              Excel
            </button>
            <button
              @click="exportData('csv')"
              class="neumorphic-button text-orange-600 hover:text-orange-700 text-sm"
              :disabled="loading"
            >
              <i class="fas fa-file-csv ml-1"></i>
              CSV
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Backup History -->
    <div class="mt-8 pt-8 border-t border-secondary-200">
      <h3 class="text-lg font-semibold text-secondary-800 flex items-center gap-2 mb-6">
        <i class="fas fa-history"></i>
        سجل النسخ الاحتياطية
      </h3>

      <div class="space-y-3">
        <div
          v-for="backup in backupHistory"
          :key="backup.id"
          class="neumorphic-card bg-white p-4 flex items-center justify-between"
        >
          <div class="flex items-center gap-3">
            <div class="neumorphic-icon text-sm">
              <i :class="backup.type === 'auto' ? 'fas fa-robot text-blue-600' : 'fas fa-user text-green-600'"></i>
            </div>
            <div>
              <p class="font-medium text-secondary-800">
                {{ backup.type === 'auto' ? 'نسخة تلقائية' : 'نسخة يدوية' }}
              </p>
              <p class="text-sm text-secondary-600">{{ formatDate(backup.date) }}</p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-xs text-secondary-500">{{ formatFileSize(backup.size) }}</span>
            <button
              @click="downloadBackup(backup)"
              class="neumorphic-button p-2 text-sm text-primary-600"
            >
              <i class="fas fa-download"></i>
            </button>
            <button
              @click="deleteBackup(backup)"
              class="neumorphic-button p-2 text-sm text-danger-600"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!backupHistory.length" class="text-center py-8">
          <div class="neumorphic-icon mx-auto mb-4 text-secondary-400">
            <i class="fas fa-database text-2xl"></i>
          </div>
          <p class="text-secondary-600">لا توجد نسخ احتياطية محفوظة</p>
        </div>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <ConfirmModal
      v-if="showConfirmRestore"
      title="تأكيد الاستعادة"
      message="هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية."
      confirm-text="استعادة"
      confirm-class="text-warning-600"
      @confirm="confirmRestore"
      @cancel="showConfirmRestore = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { BackupSettings } from '@/types'

// Components
import ConfirmModal from '../common/ConfirmModal.vue'

// Props
interface Props {
  backup: BackupSettings
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  updateBackup: [backup: Partial<BackupSettings>]
  exportData: [format: string]
  importData: [file: File]
}>()

// Reactive data
const backupData = ref<BackupSettings>({ ...props.backup })
const restoreInput = ref<HTMLInputElement>()
const showConfirmRestore = ref(false)
const pendingRestoreFile = ref<File | null>(null)

// Mock backup history (in real app, this would come from a store or API)
const backupHistory = ref([
  {
    id: '1',
    type: 'manual',
    date: new Date(Date.now() - 86400000), // 1 day ago
    size: 1024 * 1024 * 2.5 // 2.5MB
  },
  {
    id: '2',
    type: 'auto',
    date: new Date(Date.now() - 86400000 * 3), // 3 days ago
    size: 1024 * 1024 * 2.3 // 2.3MB
  }
])

// Methods
function updateBackup() {
  emit('updateBackup', { ...backupData.value })
}

async function createManualBackup() {
  try {
    // Update last backup time
    backupData.value.lastBackup = new Date()
    updateBackup()

    // Add to history
    backupHistory.value.unshift({
      id: Date.now().toString(),
      type: 'manual',
      date: new Date(),
      size: Math.random() * 1024 * 1024 * 3 // Random size for demo
    })

    // Trigger export
    emit('exportData', 'json')
  } catch (error) {
    console.error('Error creating backup:', error)
  }
}

function triggerRestore() {
  restoreInput.value?.click()
}

function handleRestoreUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processRestoreFile(file)
  }
  // Reset input
  target.value = ''
}

function handleRestoreDrop(event: DragEvent) {
  const file = event.dataTransfer?.files[0]
  if (file && file.name.endsWith('.json')) {
    processRestoreFile(file)
  }
}

function processRestoreFile(file: File) {
  if (!file.name.endsWith('.json')) {
    alert('يجب أن يكون الملف بصيغة JSON')
    return
  }

  pendingRestoreFile.value = file
  showConfirmRestore.value = true
}

function confirmRestore() {
  if (pendingRestoreFile.value) {
    emit('importData', pendingRestoreFile.value)
    pendingRestoreFile.value = null
  }
  showConfirmRestore.value = false
}

function exportData(format: string) {
  emit('exportData', format)
}

function downloadBackup(backup: any) {
  // In a real app, this would download the actual backup file
  console.log('Downloading backup:', backup.id)
}

function deleteBackup(backup: any) {
  const index = backupHistory.value.findIndex(b => b.id === backup.id)
  if (index > -1) {
    backupHistory.value.splice(index, 1)
  }
}

function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Initialize
onMounted(() => {
  // Sync props changes
  backupData.value = { ...props.backup }
})
</script>
