const fs = require('fs')
const path = require('path')
const csv = require('csv-parser')
const XLSX = require('xlsx')
const { Transform } = require('stream')
const { pipeline } = require('stream/promises')

class ImportService {
  constructor(databaseService) {
    this.db = databaseService
    this.isProcessing = false
    this.currentProgress = {
      stage: 'idle',
      progress: 0,
      totalRows: 0,
      processedRows: 0,
      errors: [],
      startTime: null,
      estimatedTimeRemaining: 0
    }
  }

  /**
   * استيراد ملف CSV أو Excel كبير باستخدام Streams
   */
  async importLargeFile(filePath, options = {}) {
    if (this.isProcessing) {
      throw new Error('Import already in progress')
    }

    this.isProcessing = true
    this.resetProgress()
    this.currentProgress.startTime = Date.now()

    try {
      console.log('🚀 Starting large file import:', filePath)
      
      // تحديد نوع الملف
      const fileExtension = path.extname(filePath).toLowerCase()
      
      if (fileExtension === '.csv') {
        return await this.importCSVStream(filePath, options)
      } else if (['.xlsx', '.xls'].includes(fileExtension)) {
        return await this.importExcelStream(filePath, options)
      } else {
        throw new Error(`Unsupported file type: ${fileExtension}`)
      }
    } catch (error) {
      console.error('❌ Import failed:', error)
      this.currentProgress.stage = 'error'
      throw error
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * استيراد ملف CSV باستخدام Streams
   */
  async importCSVStream(filePath, options) {
    const {
      tabName = 'Imported Data',
      batchSize = 1000,
      skipRows = 0,
      maxRows = null,
      onProgress = null
    } = options

    this.updateProgress('analyzing', 5, 'تحليل الملف...')

    // الحصول على حجم الملف لتقدير التقدم
    const fileStats = fs.statSync(filePath)
    const fileSize = fileStats.size
    let bytesRead = 0

    const columns = []
    const rows = []
    let rowCount = 0
    let headerProcessed = false
    let batch = []

    // إنشاء Transform stream لمعالجة البيانات
    const processStream = new Transform({
      objectMode: true,
      transform(chunk, encoding, callback) {
        try {
          // تحديث التقدم بناءً على البايتات المقروءة
          bytesRead += Buffer.byteLength(JSON.stringify(chunk))
          const progress = Math.min((bytesRead / fileSize) * 80, 80) // 80% للقراءة
          this.updateProgress('reading', progress, `قراءة البيانات... ${rowCount} صف`)

          // معالجة العنوان
          if (!headerProcessed) {
            const headers = Object.keys(chunk)
            headers.forEach((header, index) => {
              columns.push({
                id: `col_${index}`,
                name: header,
                type: 'text',
                width: 150,
                visible: true
              })
            })
            headerProcessed = true
            this.updateProgress('processing', 10, 'معالجة العناوين...')
          }

          // تخطي الصفوف إذا طُلب ذلك
          if (rowCount < skipRows) {
            rowCount++
            callback()
            return
          }

          // التحقق من الحد الأقصى للصفوف
          if (maxRows && rowCount >= maxRows + skipRows) {
            callback()
            return
          }

          // إضافة الصف للدفعة
          const rowData = Object.values(chunk)
          batch.push({
            id: `row_${rowCount}`,
            data: rowData,
            isGroupHeader: false
          })

          rowCount++

          // معالجة الدفعة عند الوصول للحد المطلوب
          if (batch.length >= batchSize) {
            this.processBatch(batch, rows)
            batch = []
            
            // تحديث التقدم
            const progress = Math.min(20 + (rowCount / (maxRows || 100000)) * 60, 80)
            this.updateProgress('processing', progress, `معالجة البيانات... ${rowCount} صف`)
          }

          callback()
        } catch (error) {
          callback(error)
        }
      }.bind(this),

      flush(callback) {
        // معالجة آخر دفعة
        if (batch.length > 0) {
          this.processBatch(batch, rows)
        }
        callback()
      }.bind(this)
    })

    try {
      // تنفيذ pipeline للقراءة والمعالجة
      await pipeline(
        fs.createReadStream(filePath),
        csv(),
        processStream
      )

      this.updateProgress('saving', 85, 'حفظ البيانات في قاعدة البيانات...')

      // حفظ التبويب في قاعدة البيانات
      const result = await this.saveTabToDatabase(tabName, columns, rows)

      this.updateProgress('completed', 100, `تم الاستيراد بنجاح! ${rowCount} صف`)

      return {
        success: true,
        tabId: result.lastInsertRowid,
        totalRows: rowCount,
        columns: columns.length,
        processingTime: Date.now() - this.currentProgress.startTime,
        errors: this.currentProgress.errors
      }
    } catch (error) {
      this.currentProgress.errors.push({
        row: rowCount,
        message: error.message,
        timestamp: new Date().toISOString()
      })
      throw error
    }
  }

  /**
   * استيراد ملف Excel باستخدام معالجة محسنة
   */
  async importExcelStream(filePath, options) {
    const {
      tabName = 'Imported Data',
      sheetName = null,
      batchSize = 1000,
      skipRows = 0,
      maxRows = null
    } = options

    this.updateProgress('analyzing', 5, 'تحليل ملف Excel...')

    try {
      // قراءة الملف
      const workbook = XLSX.readFile(filePath)
      const sheetNames = workbook.SheetNames
      const targetSheet = sheetName || sheetNames[0]

      if (!workbook.Sheets[targetSheet]) {
        throw new Error(`Sheet "${targetSheet}" not found`)
      }

      this.updateProgress('reading', 15, 'قراءة بيانات الورقة...')

      // تحويل الورقة إلى JSON مع معالجة محسنة للذاكرة
      const worksheet = workbook.Sheets[targetSheet]
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
      
      const columns = []
      const rows = []
      let rowCount = 0
      let headerProcessed = false

      // معالجة الصفوف دفعة بدفعة
      for (let rowIndex = range.s.r; rowIndex <= range.e.r; rowIndex++) {
        // تخطي الصفوف إذا طُلب ذلك
        if (rowIndex < skipRows) continue
        
        // التحقق من الحد الأقصى للصفوف
        if (maxRows && rowCount >= maxRows) break

        const rowData = []
        
        // قراءة الخلايا في الصف
        for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
          const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex })
          const cell = worksheet[cellAddress]
          const cellValue = cell ? this.formatCellValue(cell) : ''
          rowData.push(cellValue)
        }

        // معالجة العنوان
        if (!headerProcessed && rowIndex === (skipRows || range.s.r)) {
          rowData.forEach((header, index) => {
            columns.push({
              id: `col_${index}`,
              name: header || `Column ${index + 1}`,
              type: 'text',
              width: 150,
              visible: true
            })
          })
          headerProcessed = true
          this.updateProgress('processing', 20, 'معالجة العناوين...')
          continue
        }

        // إضافة الصف
        if (headerProcessed) {
          rows.push({
            id: `row_${rowCount}`,
            data: rowData,
            isGroupHeader: false
          })
          rowCount++

          // تحديث التقدم
          if (rowCount % batchSize === 0) {
            const progress = Math.min(20 + (rowCount / (maxRows || (range.e.r - range.s.r))) * 60, 80)
            this.updateProgress('processing', progress, `معالجة البيانات... ${rowCount} صف`)
            
            // إعطاء فرصة للـ event loop
            await new Promise(resolve => setImmediate(resolve))
          }
        }
      }

      this.updateProgress('saving', 85, 'حفظ البيانات في قاعدة البيانات...')

      // حفظ التبويب في قاعدة البيانات
      const result = await this.saveTabToDatabase(tabName, columns, rows)

      this.updateProgress('completed', 100, `تم الاستيراد بنجاح! ${rowCount} صف`)

      return {
        success: true,
        tabId: result.lastInsertRowid,
        totalRows: rowCount,
        columns: columns.length,
        processingTime: Date.now() - this.currentProgress.startTime,
        errors: this.currentProgress.errors
      }
    } catch (error) {
      this.currentProgress.errors.push({
        row: 0,
        message: error.message,
        timestamp: new Date().toISOString()
      })
      throw error
    }
  }

  /**
   * معالجة دفعة من البيانات
   */
  processBatch(batch, targetArray) {
    // إضافة الدفعة للمصفوفة الرئيسية
    targetArray.push(...batch)
    
    // تنظيف الذاكرة
    batch.length = 0
  }

  /**
   * تنسيق قيمة الخلية من Excel
   */
  formatCellValue(cell) {
    if (!cell) return ''
    
    switch (cell.t) {
      case 'n': // رقم
        return cell.v
      case 's': // نص
        return cell.v
      case 'b': // boolean
        return cell.v ? 'نعم' : 'لا'
      case 'd': // تاريخ
        return new Date(cell.v).toLocaleDateString('ar-SA')
      default:
        return cell.v || ''
    }
  }

  /**
   * حفظ التبويب في قاعدة البيانات
   */
  async saveTabToDatabase(tabName, columns, rows) {
    const sql = `
      INSERT INTO database_tabs (name, columns, rows, settings, updated_at) 
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `
    const settings = {
      importedAt: new Date().toISOString(),
      totalRows: rows.length,
      totalColumns: columns.length
    }

    return this.db.run(sql, [
      tabName,
      JSON.stringify(columns),
      JSON.stringify(rows),
      JSON.stringify(settings)
    ])
  }

  /**
   * تحديث التقدم
   */
  updateProgress(stage, progress, message, details = null) {
    this.currentProgress.stage = stage
    this.currentProgress.progress = Math.round(progress)
    this.currentProgress.message = message
    
    if (details) {
      this.currentProgress.details = details
    }

    // حساب الوقت المتبقي المقدر
    if (this.currentProgress.startTime && progress > 0) {
      const elapsed = Date.now() - this.currentProgress.startTime
      const estimated = (elapsed / progress) * (100 - progress)
      this.currentProgress.estimatedTimeRemaining = Math.round(estimated / 1000) // بالثواني
    }

    console.log(`📊 Import Progress: ${stage} - ${progress}% - ${message}`)
  }

  /**
   * إعادة تعيين التقدم
   */
  resetProgress() {
    this.currentProgress = {
      stage: 'idle',
      progress: 0,
      totalRows: 0,
      processedRows: 0,
      errors: [],
      startTime: null,
      estimatedTimeRemaining: 0,
      message: '',
      details: null
    }
  }

  /**
   * الحصول على حالة التقدم الحالية
   */
  getProgress() {
    return { ...this.currentProgress }
  }

  /**
   * إلغاء عملية الاستيراد
   */
  cancelImport() {
    if (this.isProcessing) {
      this.isProcessing = false
      this.currentProgress.stage = 'cancelled'
      this.currentProgress.message = 'تم إلغاء عملية الاستيراد'
      console.log('🛑 Import cancelled by user')
    }
  }

  /**
   * التحقق من حالة المعالجة
   */
  isImportInProgress() {
    return this.isProcessing
  }
}

module.exports = ImportService
