<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>👥 إنشاء المستخدمين الافتراضيين</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: center;
      margin: 50px;
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .title {
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .info {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      line-height: 1.6;
    }

    .create-button {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      padding: 20px 40px;
      border: none;
      border-radius: 25px;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      transition: all 0.3s;
      margin: 20px;
    }

    .create-button:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
    }

    .success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      display: none;
    }

    .loading {
      display: none;
      margin: 20px 0;
    }

    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 4px solid white;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .log {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 15px;
      margin: 15px 0;
      font-family: monospace;
      text-align: right;
      max-height: 200px;
      overflow-y: auto;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">👥 إنشاء المستخدمين الافتراضيين</h1>
    
    <div class="info">
      <h3>🔧 إصلاح مشكلة تسجيل الدخول</h3>
      <p>سيتم إنشاء المستخدمين الافتراضيين مباشرة في قاعدة البيانات:</p>
      <ul style="text-align: right; margin: 15px 0;">
        <li><strong>admin</strong> - المدير العام</li>
        <li><strong>investigator</strong> - محقق رئيسي</li>
        <li><strong>viewer</strong> - مراقب</li>
      </ul>
    </div>

    <button class="create-button" onclick="createDefaultUsers()">
      👥 إنشاء المستخدمين الافتراضيين
    </button>

    <div class="loading" id="loading">
      <div class="spinner"></div>
      <p>جاري إنشاء المستخدمين...</p>
    </div>

    <div class="log" id="log"></div>

    <div class="success" id="success">
      <h3>✅ تم بنجاح!</h3>
      <p>تم إنشاء المستخدمين الافتراضيين بنجاح.</p>
      <p><strong>يمكنك الآن تسجيل الدخول بـ:</strong></p>
      <ul style="text-align: right; margin: 15px 0;">
        <li><strong>admin / admin123</strong></li>
        <li><strong>investigator / inv123</strong></li>
        <li><strong>viewer / view123</strong></li>
      </ul>
      <a href="http://localhost:5175" style="color: white; text-decoration: underline; font-weight: bold;">🚀 العودة للتطبيق</a>
    </div>
  </div>

  <script>
    function log(message) {
      const logDiv = document.getElementById('log');
      logDiv.style.display = 'block';
      logDiv.innerHTML += message + '<br>';
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    async function createDefaultUsers() {
      document.querySelector('.create-button').style.display = 'none';
      document.getElementById('loading').style.display = 'block';

      try {
        log('🔄 بدء إنشاء المستخدمين الافتراضيين...');

        // فتح قاعدة البيانات
        const dbRequest = indexedDB.open('SuspectsDatabase', 3);
        
        dbRequest.onsuccess = async function(event) {
          const db = event.target.result;
          log('✅ تم الاتصال بقاعدة البيانات');

          const transaction = db.transaction(['users'], 'readwrite');
          const userStore = transaction.objectStore('users');

          // المستخدمين الافتراضيين
          const defaultUsers = [
            {
              username: 'admin',
              name: 'المدير العام',
              email: '<EMAIL>',
              role: {
                id: 'admin',
                name: 'admin',
                displayName: 'مدير عام',
                description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
                permissions: []
              },
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              username: 'investigator',
              name: 'محقق رئيسي',
              email: '<EMAIL>',
              role: {
                id: 'investigator',
                name: 'investigator',
                displayName: 'محقق رئيسي',
                description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل',
                permissions: []
              },
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              username: 'viewer',
              name: 'مراقب',
              email: '<EMAIL>',
              role: {
                id: 'viewer',
                name: 'viewer',
                displayName: 'مراقب',
                description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير',
                permissions: []
              },
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ];

          // إضافة المستخدمين
          for (const user of defaultUsers) {
            try {
              // التحقق من وجود المستخدم
              const existingUser = await new Promise((resolve, reject) => {
                const getRequest = userStore.index('username').get(user.username);
                getRequest.onsuccess = () => resolve(getRequest.result);
                getRequest.onerror = () => reject(getRequest.error);
              });

              if (existingUser) {
                log(`⚠️ المستخدم ${user.username} موجود بالفعل`);
                continue;
              }

              // إضافة المستخدم
              const addRequest = userStore.add(user);
              const userId = await new Promise((resolve, reject) => {
                addRequest.onsuccess = () => resolve(addRequest.result);
                addRequest.onerror = () => reject(addRequest.error);
              });

              log(`✅ تم إنشاء المستخدم: ${user.username} (ID: ${userId})`);

              // حفظ كلمة المرور
              const passwords = {
                'admin': 'admin123',
                'investigator': 'inv123',
                'viewer': 'view123'
              };

              // تشفير كلمة المرور (نفس الطريقة المستخدمة في UserService)
              const password = passwords[user.username];
              const salt = 'suspects_app_salt';
              const combined = password + salt;
              let hashedPassword = btoa(combined);

              // إضافة طبقة تشفير إضافية (نفس UserService)
              for (let i = 0; i < 3; i++) {
                hashedPassword = btoa(hashedPassword + salt);
              }
              localStorage.setItem(`user_password_${userId}`, hashedPassword);
              
              log(`🔑 تم حفظ كلمة المرور للمستخدم: ${user.username}`);

            } catch (error) {
              log(`❌ خطأ في إنشاء المستخدم ${user.username}: ${error.message}`);
            }
          }

          log('🎉 تم الانتهاء من إنشاء المستخدمين الافتراضيين');
          
          // إظهار رسالة النجاح
          document.getElementById('loading').style.display = 'none';
          document.getElementById('success').style.display = 'block';
        };

        dbRequest.onerror = function(event) {
          log('❌ فشل في الاتصال بقاعدة البيانات: ' + event.target.error);
          document.getElementById('loading').style.display = 'none';
          document.querySelector('.create-button').style.display = 'block';
        };

      } catch (error) {
        log('❌ خطأ عام: ' + error.message);
        document.getElementById('loading').style.display = 'none';
        document.querySelector('.create-button').style.display = 'block';
      }
    }

    console.log('👥 صفحة إنشاء المستخدمين الافتراضيين جاهزة');
  </script>
</body>
</html>
