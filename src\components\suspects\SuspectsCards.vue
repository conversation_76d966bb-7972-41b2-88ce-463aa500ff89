<template>
  <div class="space-y-6">
    <!-- Search and Controls -->
    <div class="neumorphic-card">
      <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
        <!-- Search -->
        <div class="flex-1 max-w-md">
          <div class="relative">
            <input
              :value="searchQuery"
              @input="$emit('updateSearch', $event.target.value)"
              type="text"
              class="neumorphic-input w-full pl-10"
              placeholder="البحث الذكي في جميع البيانات..."
            />
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400"></i>
          </div>
        </div>

        <!-- View Options -->
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-2">
            <span class="text-sm text-secondary-600">عرض:</span>
            <select v-model="cardsPerRow" class="neumorphic-select text-sm">
              <option value="1">بطاقة واحدة</option>
              <option value="2">بطاقتان</option>
              <option value="3">ثلاث بطاقات</option>
              <option value="4">أربع بطاقات</option>
            </select>
          </div>
          
          <div class="flex items-center gap-2">
            <span class="text-sm text-secondary-600">ترتيب:</span>
            <select v-model="sortBy" class="neumorphic-select text-sm">
              <option value="newest">الأحدث</option>
              <option value="oldest">الأقدم</option>
              <option value="name">الاسم</option>
              <option value="fileNumber">رقم الملف</option>
            </select>
          </div>

          <button
            @click="exportAllData"
            class="neumorphic-button text-primary-600 hover:text-primary-700"
            title="تصدير جميع البيانات"
          >
            <i class="fas fa-download"></i>
          </button>
        </div>
      </div>

      <!-- Results Count -->
      <div class="mt-4 pt-4 border-t border-secondary-200">
        <p class="text-sm text-secondary-600">
          عرض {{ sortedSuspects.length }} من أصل {{ suspects.length }} متهم
          <span v-if="searchQuery.trim()" class="text-primary-600">
            - نتائج البحث عن "{{ searchQuery.trim() }}"
          </span>
        </p>
      </div>
    </div>

    <!-- Cards Grid -->
    <div :class="getGridClass()">
      <div
        v-for="suspect in sortedSuspects"
        :key="suspect.id"
        class="neumorphic-card bg-white hover:shadow-neumorphic-lg transform hover:scale-102 transition-all duration-300 cursor-pointer"
        @click="viewSuspectDetails(suspect)"
      >
        <!-- Card Header -->
        <div class="flex items-center justify-between mb-4 pb-3 border-b border-secondary-200">
          <div class="flex items-center gap-3">
            <!-- Status Indicator -->
            <div :class="[
              'w-3 h-3 rounded-full',
              getStatusColor(suspect)
            ]"></div>
            <div>
              <h3 class="font-bold text-secondary-800">{{ getSuspectName(suspect) }}</h3>
              <p class="text-sm text-secondary-600">ملف رقم: {{ suspect.fields.fileNumber }}</p>
            </div>
          </div>
          
          <!-- Actions -->
          <div class="flex gap-1">
            <button
              @click.stop="editSuspect(suspect)"
              class="neumorphic-button p-2 text-xs text-primary-600"
              title="تعديل"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              @click.stop="exportSuspect(suspect)"
              class="neumorphic-button p-2 text-xs text-success-600"
              title="تصدير"
            >
              <i class="fas fa-download"></i>
            </button>
            <button
              @click.stop="deleteSuspect(suspect)"
              class="neumorphic-button p-2 text-xs text-danger-600"
              title="حذف"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Suspect Photo -->
        <div v-if="getSuspectImage(suspect)" class="mb-4">
          <div class="neumorphic-card bg-secondary-50 p-2">
            <img
              :src="getSuspectImage(suspect)"
              :alt="suspect.fields.fullName"
              class="w-full h-32 object-cover rounded-neumorphic"
            />
          </div>
        </div>

        <!-- Suspect Details -->
        <div class="space-y-3">
          <div
            v-for="field in getVisibleFields(suspect)"
            :key="field.id"
            class="flex items-start gap-3"
          >
            <div class="neumorphic-icon text-xs flex-shrink-0">
              <i :class="field.icon"></i>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-xs font-medium text-secondary-600 mb-1">{{ field.label }}</p>
              <div v-if="field.inputType === 'date' && suspect.fields[field.id!]">
                <p class="text-sm text-secondary-800">{{ formatDate(suspect.fields[field.id!]) }}</p>
              </div>
              <div v-else-if="field.inputType === 'image' && suspect.fields[field.id!]">
                <p class="text-sm text-blue-600">
                  <i class="fas fa-image ml-1"></i>
                  صورة مرفقة
                </p>
              </div>
              <div v-else-if="field.inputType === 'file' && suspect.fields[field.id!]">
                <p class="text-sm text-purple-600">
                  <i class="fas fa-file ml-1"></i>
                  ملف مرفق
                </p>
              </div>
              <div v-else>
                <p class="text-sm text-secondary-800 break-words">
                  {{ suspect.fields[field.id!] || '-' }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Information -->
        <div class="mt-4 pt-3 border-t border-secondary-200">
          <div class="flex items-center justify-between text-xs">
            <div :class="[
              'px-2 py-1 rounded-full font-medium',
              getStatusBadgeClass(suspect)
            ]">
              {{ getStatusText(suspect) }}
            </div>
            <div class="text-secondary-500">
              {{ getStatusDetails(suspect) }}
            </div>
          </div>
        </div>

        <!-- Attachments -->
        <div v-if="suspect.attachments.length > 0" class="mt-3 pt-3 border-t border-secondary-200">
          <p class="text-xs font-medium text-secondary-600 mb-2">المرفقات:</p>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="attachment in suspect.attachments.slice(0, 3)"
              :key="attachment.id"
              class="px-2 py-1 bg-secondary-100 rounded-neumorphic-sm text-xs text-secondary-700"
              :title="attachment.originalName"
            >
              <i class="fas fa-paperclip ml-1"></i>
              {{ truncateFileName(attachment.originalName) }}
            </span>
            <span
              v-if="suspect.attachments.length > 3"
              class="px-2 py-1 bg-primary-100 rounded-neumorphic-sm text-xs text-primary-700"
            >
              +{{ suspect.attachments.length - 3 }} أخرى
            </span>
          </div>
        </div>

        <!-- Creation Date -->
        <div class="mt-3 pt-3 border-t border-secondary-200">
          <p class="text-xs text-secondary-500 text-center">
            <i class="fas fa-calendar ml-1"></i>
            تم الإنشاء: {{ formatDate(suspect.createdAt) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="sortedSuspects.length === 0" class="neumorphic-card text-center py-12">
      <div class="neumorphic-icon mx-auto mb-4 text-secondary-400">
        <i class="fas fa-search text-2xl"></i>
      </div>
      <h3 class="text-lg font-semibold text-secondary-600 mb-2">لا توجد نتائج</h3>
      <p class="text-secondary-500 mb-4">
        {{ searchQuery.trim() ? 'لم يتم العثور على متهمين يطابقون البحث' : 'لا توجد بيانات متهمين' }}
      </p>
      <button
        v-if="searchQuery.trim()"
        @click="$emit('updateSearch', '')"
        class="neumorphic-button text-primary-600"
      >
        <i class="fas fa-times ml-2"></i>
        مسح البحث
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <div class="spinner mx-auto mb-4"></div>
      <p class="text-secondary-600">جاري تحميل البيانات...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SuspectData, SuspectField } from '@/types'

// Props
interface Props {
  suspects: SuspectData[]
  fields: SuspectField[]
  loading: boolean
  searchQuery: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  updateSearch: [query: string]
  updateSuspect: [id: string, updates: Partial<SuspectData>]
  deleteSuspect: [id: string]
  exportSuspect: [suspect: SuspectData]
  editSuspect: [suspect: SuspectData]
}>()

// Reactive data
const cardsPerRow = ref(3)
const sortBy = ref('newest')

// Computed
const sortedSuspects = computed(() => {
  let sorted = [...props.suspects]

  switch (sortBy.value) {
    case 'newest':
      sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
    case 'oldest':
      sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      break
    case 'name':
      sorted.sort((a, b) => (a.fields.fullName || '').localeCompare(b.fields.fullName || '', 'ar'))
      break
    case 'fileNumber':
      sorted.sort((a, b) => (a.fields.fileNumber || '').localeCompare(b.fields.fileNumber || ''))
      break
  }

  return sorted
})

const visibleFields = computed(() => {
  return props.fields
    .filter(field => field.isVisible && field.id !== 'fullName' && field.id !== 'fileNumber')
    .sort((a, b) => a.order - b.order)
    .slice(0, 8) // Limit to 8 fields for better card display
})

// Methods
function getGridClass(): string {
  const classes = {
    1: 'grid grid-cols-1 gap-6',
    2: 'grid grid-cols-1 md:grid-cols-2 gap-6',
    3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
  }
  return classes[cardsPerRow.value as keyof typeof classes] || classes[3]
}

function getStatusColor(suspect: SuspectData): string {
  if (suspect.fields.isReleased) return 'bg-success-500'
  if (suspect.fields.isTransferred) return 'bg-blue-500'
  return 'bg-danger-500'
}

function getStatusBadgeClass(suspect: SuspectData): string {
  if (suspect.fields.isReleased) return 'bg-success-100 text-success-800'
  if (suspect.fields.isTransferred) return 'bg-blue-100 text-blue-800'
  return 'bg-danger-100 text-danger-800'
}

function getStatusText(suspect: SuspectData): string {
  if (suspect.fields.isReleased) return 'مفرج عنه'
  if (suspect.fields.isTransferred) return 'محال للنيابة'
  return 'رهن الاعتقال'
}

function getStatusDetails(suspect: SuspectData): string {
  if (suspect.fields.isReleased && suspect.fields.releaseDate) {
    return `تاريخ الإفراج: ${formatDate(suspect.fields.releaseDate)}`
  }
  if (suspect.fields.isTransferred && suspect.fields.transferDate) {
    return `تاريخ الإحالة: ${formatDate(suspect.fields.transferDate)}`
  }

  // Calculate detention days
  const arrestDate = suspect.fields.arrestDate ? new Date(suspect.fields.arrestDate) : suspect.createdAt
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - arrestDate.getTime())
  const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return `${days} يوم في الاعتقال`
}

function getSuspectImage(suspect: SuspectData): string | null {
  const imageField = props.fields.find(f => f.inputType === 'image')
  if (!imageField) return null

  const attachment = suspect.attachments.find(att => att.fieldId === imageField.id)
  return attachment?.filePath || null
}

function getVisibleFields(suspect: SuspectData): SuspectField[] {
  return visibleFields.value.filter(field => {
    const value = suspect.fields[field.id!]
    return value !== undefined && value !== null && value !== ''
  })
}

function formatDate(date: any): string {
  if (!date) return '-'
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

function getSuspectName(suspect: SuspectData): string {
  // Try different ways to get the name
  if (suspect.fields.fullName) {
    return suspect.fields.fullName
  }

  // Find the name field by order (should be order 2)
  const nameField = props.fields.find(field => field.order === 2)
  if (nameField && suspect.fields[nameField.id!]) {
    return suspect.fields[nameField.id!]
  }

  // Try common field names
  const possibleNameFields = ['fullName', 'name', 'الاسم_الرباعي', 'اسم_المتهم']
  for (const fieldName of possibleNameFields) {
    if (suspect.fields[fieldName]) {
      return suspect.fields[fieldName]
    }
  }

  // Find any field with "اسم" in the label
  const nameFieldByLabel = props.fields.find(field =>
    field.label.includes('اسم') || field.label.includes('الاسم')
  )
  if (nameFieldByLabel && suspect.fields[nameFieldByLabel.id!]) {
    return suspect.fields[nameFieldByLabel.id!]
  }

  return 'غير محدد'
}

function truncateFileName(fileName: string): string {
  if (fileName.length <= 15) return fileName
  const extension = fileName.split('.').pop()
  const name = fileName.substring(0, fileName.lastIndexOf('.'))
  return `${name.substring(0, 10)}...${extension}`
}

function viewSuspectDetails(suspect: SuspectData) {
  // This would open a detailed view modal or navigate to detail page
  console.log('View suspect details:', suspect.id)
}

function editSuspect(suspect: SuspectData) {
  // Emit event to parent component to handle editing
  emit('editSuspect', suspect)
}

function deleteSuspect(suspect: SuspectData) {
  if (confirm(`هل أنت متأكد من حذف المتهم "${suspect.fields.fullName}"؟`)) {
    emit('deleteSuspect', suspect.id!)
  }
}

function exportSuspect(suspect: SuspectData) {
  emit('exportSuspect', suspect)
}

function exportAllData() {
  // This would export all visible suspects data
  console.log('Export all data')
}
</script>
